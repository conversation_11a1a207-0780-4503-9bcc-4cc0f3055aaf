import { Sheet, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>rollView, <PERSON>tar, Button } from 'tamagui'
import { Check, Settings } from '@tamagui/lucide-icons'
import { useCallback, useState } from 'react'



type Props = {
  open: boolean
  onOpenChange: (open: boolean) => void
  wallets: any[]
  selectedId?: number
  onSelect: (wallet: any, index?: number) => void
}

export const WalletSheet = ({ open, onOpenChange, wallets, selectedId, onSelect }: Props) => {
  const handleSelect = useCallback(
    (index, account) => {
      // 隐藏底部抽屉
      onOpenChange(false)
      onSelect(account, index)
    },
    [onSelect]
  )
  const [position, setPosition] = useState(0)

  return (
    <Sheet
      open={open}
      forceRemoveScrollEnabled={open}
      modal={false}
      onOpenChange={onOpenChange}
      snapPoints={['60%', 256, 190]}
      snapPointsMode={'mixed'}
      dismissOnSnapToBottom
      position={position}
      onPositionChange={setPosition}
      zIndex={100_000}
      animation="medium"
    >
      <Sheet.Overlay
        animation="lazy"
        backgroundColor="$shadow6"
        enterStyle={{ opacity: 0 }}
        exitStyle={{ opacity: 0 }}
      />
      <Sheet.Handle />
      <Sheet.Frame
        padding="$4"
        backgroundColor="$color2"
        borderWidth={1}
        borderColor="#E5E6EB"
        borderTopLeftRadius={16}
        borderTopRightRadius={16}
      >
        <YStack gap="$3" flex={1}>
          <XStack justifyContent="space-between">
            <Text fontWeight="600" fontSize={18}>
              钱包
            </Text>
            <Text fontWeight="500" fontSize={16}>
              合计: $
              {(() => {
                let total = 0
                wallets.forEach((wallet) => {
                  wallet.accounts.forEach((account) => {
                    const chains = ['eth', 'bsc', 'btc', 'solana']
                    chains.forEach((chain) => {
                      if (account[chain]?.balance) {
                        total += parseFloat(account[chain].balance) || 0
                      }
                    })
                  })
                })
                return total.toFixed(4)
              })()}
            </Text>
          </XStack>

          <ScrollView>
            {wallets.map((wallet, index) => (
              <YStack key={wallet.walletId}>
                <Text fontSize={14} color="$color11" my={16}>{`钱包 ${index + 1}`}</Text>
                <YStack>
                  {wallet.accounts.map((account, index) => (
                    <XStack
                      key={account.accountId}
                      alignItems="center"
                      justifyContent="space-between"
                      padding="$3"
                      borderRadius="$4"
                      my={12}
                      backgroundColor={account.accountId === selectedId ? '$blue4' : 'transparent'}
                      onPress={() => handleSelect(index, account)}
                    >
                      <XStack alignItems="center">
                        <Avatar circular size="$4" mr={12}>
                          <Avatar.Image
                            src={`https://api.dicebear.com/7.x/identicon/svg?seed=${account.accountId}`}
                            accessibilityLabel={account.accountId}
                          />
                          <Avatar.Fallback backgroundColor="$blue10" />
                        </Avatar>
                        <YStack>
                          <Text fontWeight="600" fontSize={16}>
                            {account.name || `地址 ${index + 1}`}
                          </Text>
                          <Text fontSize={14} color="$color11">
                            $
                            {(() => {
                              let total = 0
                              const chains = ['eth', 'bsc', 'btc', 'solana']
                              chains.forEach((chain) => {
                                if (account[chain]?.balance) {
                                  total += parseFloat(account[chain].balance) || 0
                                }
                              })
                              return total.toFixed(4)
                            })()}
                          </Text>
                        </YStack>
                      </XStack>

                      {account.accountId === selectedId && <Check size={16} color={'orange'} />}
                    </XStack>
                  ))}
                </YStack>
              </YStack>
            ))}
          </ScrollView>

          <Button
            onPress={() => onSelect('addWallet')}
            borderRadius={40}
            height={60}
            iconAfter={<Settings />}
            fontSize={16}
            color="$color"
            fontWeight={600}
          >
            添加和管理钱包
          </Button>
        </YStack>
      </Sheet.Frame>
    </Sheet>
  )
}
