import { XStack, <PERSON><PERSON><PERSON><PERSON>, Button } from 'tamagui'

export const KeyButton = ({
  children,
  onPress,
}: {
  children: React.ReactNode
  onPress: () => void
}) => (
  <Button
    fontSize={24}
    fontWeight={500}
    bg="$color0"
    width="33.3%"
    flex={1}
    pressStyle={{
      bg: '$color0',
      borderColor: '$color0',
      shadowColor: 'transparent',
      opacity: 1,
      borderWidth: 0,
      outlineWidth: 0,
      outlineColor: 'transparent',
    }}
    focusStyle={{
      outlineWidth: 0,
      outlineColor: 'transparent',
      borderColor: '$color0',
    }}
    hoverStyle={{
      bg: '$color0',
      borderColor: '$color0',
      shadowColor: 'transparent',
      opacity: 1,
    }}
    onPress={onPress}
  >
    {children}
  </Button>
)

export const Keyboard = ({ onKeyPress }: { onKeyPress: (input: string) => void }) => {

  const keys = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0']

  return (
    <YStack width={'100%'} gap="$4">
      {[0, 1, 2].map((row) => (
        <XStack key={row} gap="$3" justify="center">
          {keys.slice(row * 3, row * 3 + 3).map((key) => (
            <KeyButton key={key} onPress={() => onKeyPress(key)}>{key}</KeyButton>
          ))}
        </XStack>
      ))}
      <XStack gap="$3" justify="center">
        <KeyButton onPress={() => onKeyPress('.')}>.</KeyButton>
        <KeyButton onPress={() => onKeyPress('0')}>0</KeyButton>
        <KeyButton onPress={() => onKeyPress('back')}>←</KeyButton>
      </XStack>
    </YStack>
  )
} 
