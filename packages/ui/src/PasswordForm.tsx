import { useCallback, useEffect, useState } from "react";
import { XS<PERSON>ck, YS<PERSON>ck, ZS<PERSON>ck } from "tamagui";
import { View, Text, Image, H1, H2, Input, Checkbox, Button } from 'tamagui'
import { Eye, EyeOff } from '@tamagui/lucide-icons'
import { PASSWORD_MIN_LENGTH } from "./utils/constants";

type PasswordFormProps = {
  onChange: (password: string) => void;
};

export function PasswordForm({ onChange }: PasswordFormProps) {

  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [passwordError, setPasswordError] = useState('')

  const [confirmPassword, setConfirmPassword] = useState('');
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [confirmPasswordError, setConfirmPasswordError] = useState('');

  const handlePasswordChange = useCallback((passwordInput: string) => {
    console.log(passwordInput)
    const isTooShort = passwordInput.length < PASSWORD_MIN_LENGTH;
    if (isTooShort) {
      setPasswordError('密码长度不足')
    } else {
      setPasswordError('')
    }
    if (confirmPassword.length > 0 && passwordInput !== confirmPassword) {
      setConfirmPasswordError('密码不匹配')
    } else {
      setConfirmPasswordError('')
    }
    setPassword(passwordInput);

  }, [confirmPassword])

  const handleConfirmPasswordChange = useCallback(
    (confirmPasswordInput: string) => {
      const error =
        password === confirmPasswordInput || confirmPasswordInput.length === 0
          ? ''
          : '密码不匹配'

      setConfirmPassword(confirmPasswordInput);
      setConfirmPasswordError(error);
    },
    [password],
  );

  useEffect(() => {
    if (
      password.length >= PASSWORD_MIN_LENGTH &&
      confirmPassword.length >= PASSWORD_MIN_LENGTH &&
      password === confirmPassword
    ) {
      onChange(password);
    } else {
      onChange('');
    }
  }, [password, confirmPassword, onChange]);

  return (
    <YStack>
      <YStack>
        <Text>输入新密码</Text>

        <YStack height={64} position="relative">
          <Input mt={8} size="$4" borderWidth={2}
            secureTextEntry={!showPassword}
            onChange={e => {
              // @ts-ignore
              handlePasswordChange(e.target.value)
            }} />
          <XStack width={20} position="absolute" left={'auto'} right={12} top={20}
            onPress={e => {
              setShowPassword(!showPassword)
            }}
          >
            {showPassword ? <Eye size={18} /> : <EyeOff size={18} />}
          </XStack>
        </YStack>
        <Text fontSize={14} mt={12} color="$red8">{passwordError}</Text>
      </YStack>
      <YStack mt={24}>
        <Text>确认密码</Text>
        <YStack height={64} position="relative">
          <Input mt={8} size="$4" borderWidth={2}
            secureTextEntry={!showConfirmPassword}
            onChange={e => {
              // @ts-ignore
              handleConfirmPasswordChange(e.target.value)
            }} />
          <XStack width={20} position="absolute" left={'auto'} right={12} top={20}
            onPress={e => {
              setShowConfirmPassword(!showConfirmPassword)
            }}
          >
            {showConfirmPassword ? <Eye size={18} /> : <EyeOff size={18} />}
          </XStack>
        </YStack>

        <Text fontSize={14} mt={12} color="$red8">{confirmPasswordError}</Text>
      </YStack>

    </YStack>
  )
}