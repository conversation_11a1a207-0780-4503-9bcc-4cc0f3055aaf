import { XStack, Text, YStack, Button } from 'tamagui'
import { ArrowLeft } from '@tamagui/lucide-icons'

type NavBarProps = {
  title?: string
  onBack?: () => void
}

export function NavBar({ title, onBack }: NavBarProps) {
  return (
    <YStack height={64} justifyContent="center" bg="$background">
      <XStack alignItems="center" height={32} px="$2" space="$2">
        <Button
          chromeless
          size="$2"
          onPress={onBack}
          icon={<ArrowLeft key="navbar-arrowleft" size={20} />}
          aria-label="Back"
          circular
        />
        {(
          <Text key="navbar-title" fontSize={16} fontWeight="600">
            {title || ''}
          </Text>
        )}
      </XStack>
    </YStack>
  )
}