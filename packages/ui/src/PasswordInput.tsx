import { useState, useEffect, useCallback } from 'react'
import { Text, XStack, YStack, Button, Circle, AnimatePresence, Stack } from 'tamagui'
import { Keyboard } from './Keyboard'

export const PasswordInput = ({ onSuccess, onKeyPress }: { onSuccess: (password: string) => void, onKeyPress: (key: string) => void }) => {
  const [input, setInput] = useState('')
  const handleKeyPress = useCallback((key: string) => {
    onKeyPress(key)
    if (key === 'back') {
      setInput(input.slice(0, -1))
    } else if (input.length < 6) {
      setInput(input + key)
    }

  }, [input])

  useEffect(() => {
    if (input.length === 6) {
      onSuccess(input)
      setTimeout(() => {
        setInput('')
      }, 300);

    }
  }, [input])

  const keys = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0']

  return (
    <YStack items="center" justifyContent='space-between' gap="$4" flex={1}>
      <XStack gap={12} mb="$2">
        {[...Array(6)].map((_, i) => (
          <AnimatePresence key={i}>
            <Circle
              size="$2"
              bg={i < input.length ? "$color" : "$color0"}
              borderStyle='solid'
              borderWidth={1}
              borderColor={'#4575FF'}
              enterStyle={{ scale: 0 }}
              exitStyle={{ scale: 0 }}
              animation="quick"
            />
          </AnimatePresence>
        ))}
      </XStack>
      <YStack width={'100%'}>
        <Keyboard onKeyPress={handleKeyPress} />
        <YStack height={100}></YStack>
      </YStack>

    </YStack>
  )
} 
