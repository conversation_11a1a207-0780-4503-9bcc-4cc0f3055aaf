import { defaultConfig } from '@tamagui/config/v4'
import { createTamagui, createTokens } from 'tamagui'
import { color, radius, size, space, themes, zIndex } from '@tamagui/themes'
import { bodyFont, headingFont } from './fonts'
import { animations } from './animations'


export const config = createTamagui({
  ...defaultConfig,
  // themes,
  // tokens,
  animations,
  fonts: {
    body: bodyFont,
    heading: headingFont,
  },
  settings: { ...defaultConfig.settings, onlyAllowShorthands: false, },
  // shorthands: undefined
  
})

type AppConfig = typeof config

declare module 'tamagui' {
  interface TamaguiCustomConfig extends AppConfig {}
}