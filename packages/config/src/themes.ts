const dark = {
  // Standard keys for all components
  background: '#000',
  backgroundHover: '#111',
  backgroundPress: '#222',
  backgroundFocus: '#333',
  backgroundStrong: '#444',
  backgroundTransparent: 'rgba(0, 0, 0, 0.5)',
  color: '#fff',
  colorHover: '#eee',
  colorPress: '#ddd',
  colorFocus: '#ccc',
  colorTransparent: 'rgba(255, 255, 255, 0.5)',
  borderColor: '#555',
  borderColorHover: '#666',
  borderColorFocus: '#777',
  borderColorPress: '#888',
  placeholderColor: '#999',
  outlineColor: '#aaa',
  // Custom tokens like "brand"
  brandBackground: '#000', // You can add your own tokens like "brand"
  brandColor: '#fff', // and use them in your components
}

const light = {
  // Standard keys for all components
  background: '#fff',
  backgroundHover: '#f5f5f5',
  backgroundPress: '#e0e0e0',
  backgroundFocus: '#d5d5d5',
  backgroundStrong: '#ccc',
  backgroundTransparent: 'rgba(255, 255, 255, 0.5)',
  color: '#000',
  colorHover: '#111',
  colorPress: '#222',
  colorFocus: '#333',
  colorTransparent: 'rgba(0, 0, 0, 0.5)',
  borderColor: '#444',
  borderColorHover: '#555',
  borderColorFocus: '#666',
  borderColorPress: '#777',
  placeholderColor: '#888',
  outlineColor: '#999',
  // Custom tokens like "brand"
  brandBackground: '#000', // You can add your own tokens like "brand"
  brandColor: '#fff', // and use them in your components
}