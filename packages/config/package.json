{"name": "@my/config", "version": "0.0.1", "sideEffects": ["*.css"], "private": true, "types": "./src", "main": "src/index.ts", "files": ["types", "dist"], "scripts": {"build": "tamagui-build --skip-types", "watch": "tamagui-build --skip-types --watch"}, "dependencies": {"@tamagui/animations-react-native": "^1.129.0", "@tamagui/font-inter": "^1.129.0", "@tamagui/shorthands": "^1.129.0", "@tamagui/themes": "^1.129.0", "tamagui": "^1.129.0"}, "devDependencies": {"@tamagui/build": "^1.129.0"}}