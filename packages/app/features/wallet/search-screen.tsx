import { Avatar, Button, H3, H5, <PERSON>vB<PERSON>, Paragraph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'
import net1Icon from '../../../assets/images/wallet/net1.png'
import net2Icon from '../../../assets/images/wallet/net2.png'
import net3Icon from '../../../assets/images/wallet/net3.png'
import searchIcon from '../../../assets/images/search.png'

import { Pressable } from 'react-native'
import { useState } from 'react'


const Underline = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 20
})
const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2
})
const ActiveUnderline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF'
})

export function SearchScreen() {
  const router = useRouter()
  const tabList = [
    '全部', '交换', '赚取', '社交媒体', '管理', '监听'
  ]
  const [currentTab, setCurrentTab] = useState(0)
  const dataList = [
    {
      id: 1,
      name: 'PancakeSwap',
      desc: 'DeFi 交换和 NFT 交易',
    },
    {
      id: 2,
      name: 'Curve',
      desc: '稳定币项目',
      isSelected: false
    },
    {
      id: 3,
      name: 'Coinbase NFT',
      desc: '创建、收集、连接',
      isSelected: false
    },
    {
      id: 4,
      name: 'Biswap',
      desc: 'DeFi交换和 NFT 交易',
      isSelected: false
    },
    {
      id: 5,
      name: 'Lido',
      desc: 'DeFi质押',
      isSelected: false
    },
  ]

  const onMnemonicClick = () => {
    router.push('/wallet/password')
  }

  return (
    <YStack bg="$background">
      <XStack px={16} alignItems='center' mb={20} mt={32} justifyContent="space-between">
        <XStack position='relative' width="86%">
          <Image source={searchIcon.src} width={16} height={16} mr={10} position='absolute' top={14} left={10} />
          <Input placeholder="搜索或输入网址" width="100%" pl={30} />
        </XStack>
        <Text onPress={() => router.back()}>取消</Text>
      </XStack>
      <YStack px={16} py={10} rowGap={16}>
        <Text color="$accent11">热门搜索</Text>
        <YStack py={10}>
          {
            dataList.map((i) =>
              <XStack items="center" key={i.id} mb={16}>
                <Image source={{ uri: '' }} width={40} height={40} rounded={20} mr={10} bg="$white1" />
                <View>
                  <XStack>
                    <Text mr={20}>{i.name}</Text>
                    {i.isSelected && <Text bg="#141519" rounded={10} width={55} height={22} fontSize={12} textAlign='center' lineHeight={22}>精选</Text>}
                  </XStack>
                  <Text color="$color10" fontSize={14} mt={11}>{i.desc}</Text>
                </View>
              </XStack>
            )
          }
        </YStack>
      </YStack>
    </YStack>

  )
}