import {
  Nav<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  But<PERSON>,
} from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text } from 'tamagui'
import { Pressable, Alert, Platform } from 'react-native'
import { useState } from 'react'
import { Check, Share } from '@tamagui/lucide-icons'
import { useWalletStore } from 'app/stores/walletStore'
import { useTranslation } from 'app/i18n'
import QRCode from 'react-qr-code'



export function ReceiveKeetaScreen() {
  const router = useRouter()
  const walletStore = useWalletStore()
  const { t } = useTranslation()
  const [selectedChain, setSelectedChain] = useState<'eth' | 'bsc' | 'btc' | 'solana'>('eth')
  const [copyStatus, setCopyStatus] = useState<'idle' | 'copied'>('idle')

  const currentAccount = walletStore.currentAccount

  // 获取当前链的地址和信息
  const getCurrentChainInfo = () => {
    if (!currentAccount || !currentAccount[selectedChain]) return null

    const chainInfo = currentAccount[selectedChain]
    const chainNames = {
      eth: 'Ethereum',
      bsc: 'BSC',
      btc: 'Bitcoin',
      solana: 'Solana',
    }

    return {
      ...chainInfo,
      chainName: chainNames[selectedChain],
      address: chainInfo.address,
    }
  }

  const chainInfo = getCurrentChainInfo()

  // 复制地址到剪贴板
  const copyAddress = async () => {
    if (chainInfo?.address) {
      try {
        // 使用现代剪贴板API
        if (navigator.clipboard) {
          await navigator.clipboard.writeText(chainInfo.address)
        } else {
          // 降级方案
          const textArea = document.createElement('textarea')
          textArea.value = chainInfo.address
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
        }

        // 设置复制状态
        setCopyStatus('copied')

        // 2秒后恢复状态
        setTimeout(() => {
          setCopyStatus('idle')
        }, 2000)

      } catch (error) {
        Alert.alert(t('common.error'), t('errors.copyFailed') || '复制失败')
      }
    }
  }

  // 分享地址
  const shareAddress = async () => {
    if (chainInfo?.address) {
      try {
        if (navigator.share) {
          // 使用Web Share API
          await navigator.share({
            title: `我的${chainInfo.chainName}地址`,
            text: `这是我的${chainInfo.chainName}钱包地址，您可以向此地址发送代币和NFT。`,
            url: chainInfo.address,
          })
        } else {
          // 降级方案：复制到剪贴板并提示
          if (navigator.clipboard) {
            await navigator.clipboard.writeText(chainInfo.address)
          }
          Alert.alert(
            '分享地址',
            `地址已复制到剪贴板：\n${chainInfo.address}\n\n您可以将此地址分享给他人以接收${chainInfo.chainName}代币和NFT。`,
            [{ text: '确定', style: 'default' }]
          )
        }
      } catch (error) {
        console.error('分享失败:', error)
        Alert.alert('分享失败', '无法分享地址，请手动复制。')
      }
    }
  }

  // 格式化地址显示
  const formatAddress = (address: string) => {
    if (!address) return ''
    return `${address.slice(0, 6)}...${address.slice(-6)}`
  }

  if (!chainInfo) {
    return (
      <YStack bg="$background" flex={1} justifyContent="center" alignItems="center">
        <Text color="white">请先选择钱包账户</Text>
      </YStack>
    )
  }

  // 获取安全距离
  const getSafeAreaBottom = () => {
    if (Platform.OS === 'web') {
      return 'max(44px, env(safe-area-inset-bottom))'
    }
    return 44
  }

  return (
    <YStack bg="$background" position="relative" flex={1}>
      <XStack justifyContent="space-between" items="center">
        <NavBar title="接收加密货币和NFT" onBack={() => router.back()} />
      </XStack>
      {/* 链选择器 */}
      <XStack px={16} mt={20} justifyContent="center">
        <XStack gap={10}>
          {(['eth', 'bsc', 'btc', 'solana'] as const).map((chain) => (
            <Pressable
              key={chain}
              onPress={() => setSelectedChain(chain)}
              style={{
                paddingHorizontal: 16,
                paddingVertical: 8,
                borderRadius: 20,
                backgroundColor: selectedChain === chain ? '#4575FF' : '#232426',
              }}
            >
              <Text color={selectedChain === chain ? 'white' : '$accent11'} fontSize={12}>
                {chain.toUpperCase()}
              </Text>
            </Pressable>
          ))}
        </XStack>
      </XStack>

      <YStack flex={1} pb={100}>
        <XStack mt={40} position="relative" px={16} items="center" justifyContent="center">
          <View
            width={207}
            height={207}
            rounded={10}
            bg="$white1"
            justifyContent="center"
            alignItems="center"
            padding={16}
          >
            {/* 真实的二维码 */}
            <QRCode
              value={chainInfo.address}
              size={175}
              bgColor="#ffffff"
              fgColor="#000000"
              level="M"
            />
          </View>
        </XStack>
        <XStack
          ml={16}
          mr={16}
          mt={30}
          px={16}
          justifyContent="space-between"
          items="center"
          borderColor="#232426"
          height={64}
          borderWidth={1}
          rounded={20}
        >
          <View flex={1}>
            <Text color="$accent11" fontSize={12}>
              {t('wallet.yourAddress') || '您的'}
              {chainInfo.chainName}
              {t('wallet.address') || '地址'}
            </Text>
            <Text fontSize={12} color="white">
              {formatAddress(chainInfo.address)}
            </Text>
          </View>
          <Pressable
            onPress={copyAddress}
            style={{
              backgroundColor: copyStatus === 'copied' ? '#22C55E' : '#4575FF',
              width: copyStatus === 'copied' ? 80 : 67,
              height: 32,
              borderRadius: 30,
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'row',
            }}
          >
            {copyStatus === 'copied' ? (
              <>
                <Check size={14} color="white" style={{ marginRight: 4 }} />
                <Text color="white" textAlign="center" fontSize={12}>
                  已复制
                </Text>
              </>
            ) : (
              <Text color="white" textAlign="center" fontSize={12}>
                {t('common.copy') || '复制'}
              </Text>
            )}
          </Pressable>
        </XStack>

        <XStack mt={20} items="center" justifyContent="space-between" px={16} mx={16}>
          <View width="90%">
            <Text color="$accent11" fontSize={12} textAlign="center">
              使用此地址在Ethereum、Polygon、Avalanche、Arbitrum 以及其他兼容网络上接收代币和
              NFT。交易可能 需要几分钟才能完成。
            </Text>
            <XStack mt={6} items="center" justifyContent="center">
              <Text color="#3C72F9" fontSize={12} fontWeight={500}>
                了解更多
              </Text>
              <Text color="$accent11" fontSize={12}>
                关于 ERC-20 代币。
              </Text>
            </XStack>
          </View>
        </XStack>
      </YStack>


      <View
        position="absolute"
        bottom={0}
        left={0}
        right={0}
        bg="$background"
        px={16}
        style={{
          paddingBottom: getSafeAreaBottom(),
          paddingTop: 16,
        }}
      >


        <Button bottom={20} ml={16} rounded={30} width="92%" mt={80} onPress={shareAddress}>
          <Text color="$white1" fontSize={14} fontWeight="bold">分享</Text>
        </Button>
      </View>
    </YStack>
  )
}
