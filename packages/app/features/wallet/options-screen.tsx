import { Avatar, Button, H3, H5, <PERSON>v<PERSON><PERSON>, Paragraph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'
import net3Icon from '../../../assets/images/wallet/net3.png'
import buy2Icon from '../../../assets/images/wallet/buy2.png'


import { Pressable } from 'react-native'
import { ChevronRight } from '@tamagui/lucide-icons'


const Underline = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 60
})
const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2
})
const ActiveUnderline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF'
})

export function OptionsScreen() {
  const router = useRouter()

  return (
    <YStack bg="$background" position='relative' pr={16}>
      <XStack justifyContent='space-between' items="center">
        <NavBar title="选项卡" onBack={() => router.back()} />
        <Image source={net3Icon.src} width={16} height={16} />
      </XStack>
      <YStack mt={20} px={16}>
        <XStack width={167} height={167} borderWidth={1} borderColor="#232426" items="center" justifyContent='center' rounded={16}>
          <Image source={buy2Icon.src} width={28} height={28} />
        </XStack>
      </YStack>
    </YStack>
  )
}