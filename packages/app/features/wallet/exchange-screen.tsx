import { Ava<PERSON>, Button, H3, H5, <PERSON>v<PERSON><PERSON>, Paragraph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'
import excehangIcon from '../../../assets/images/wallet/exchange.png'

import { Pressable } from 'react-native'
import { useState, useEffect } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'
import { FooterNavBar } from '../home/<USER>'
import { useTransactionStore } from 'app/stores/transactionStore'
import { useWalletStore } from 'app/stores/walletStore'
import { useTranslation } from 'app/i18n'


const Underline = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 20
})
const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2
})
const ActiveUnderline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF'
})

export function ExchangeScreen() {
  const router = useRouter()
  const { t } = useTranslation()
  const transactionStore = useTransactionStore()
  const walletStore = useWalletStore()

  const tabList = [
    '全部', '交换', '赚取', '社交媒体', '管理', '监听'
  ]
  const [currentTab, setCurrentTab] = useState(0)

  // 初始化交易数据
  useEffect(() => {
    transactionStore.loadTransactions()
  }, [])

  // 获取最近的交易数量
  const getRecentTransactionsCount = () => {
    const currentAccount = walletStore.currentAccount
    if (!currentAccount) return 0

    // 获取所有链的地址
    const addresses = [
      currentAccount.eth?.address,
      currentAccount.bsc?.address,
      currentAccount.btc?.address,
      currentAccount.solana?.address,
    ].filter(Boolean) // 过滤掉undefined/null值

    // 使用新的去重方法获取交易记录
    const allTransactions = transactionStore.getTransactionsByAddresses(addresses)
    return allTransactions.length
  }
  const dataList = [
    {
      id: 1,
      name: 'Aerodrome',
      desc: '交易资产',
      isSelected: true
    },
    {
      id: 2,
      name: 'Uniswap',
      desc: '交易资产',
      isSelected: false
    },
    {
      id: 3,
      name: 'SushiSwap',
      desc: '交易资产',
      isSelected: false
    },
  ]

  const onMnemonicClick = () => {
    router.push('/wallet/password')
  }

  return (
    <YStack bg="$background" px={16} py={30} minHeight={'100vh'}>
      <XStack pl={16} justifyContent='space-between'>
        <H3>交易</H3>
      </XStack>

      {/* 交易历史快捷入口 */}
      <Pressable onPress={() => router.push('/wallet/transaction')}>
        <XStack mt={20} p={16} bg="#1A1A1A" borderRadius={12} alignItems="center" justifyContent="space-between">
          <XStack alignItems="center">
            <View width={40} height={40} bg="#4575FF" borderRadius={20} alignItems="center" justifyContent="center">
              <Text color="white" fontSize={18} fontWeight="bold">📊</Text>
            </View>
            <YStack ml={12}>
              <Text color="white" fontSize={16} fontWeight="bold">
                {t('wallet.transactionHistory') || '交易历史'}
              </Text>
              <Text color="#8B8F9A" fontSize={14}>
                {getRecentTransactionsCount()} 笔交易记录
              </Text>
            </YStack>
          </XStack>
          <ChevronRight size={20} color="#8B8F9A" />
        </XStack>
      </Pressable>
      {getRecentTransactionsCount() == 0 ? <>
        <XStack pl={16} justifyContent='center' mt={50}>
          <Image source={excehangIcon.src} width={173} height={142} />
        </XStack>
        <YStack pl={16} justifyContent='center' mt={20}>
          <Text color="$white1" fontSize={16} fontWeight="bold" textAlign='center' mb={10}>还没有交易</Text>
          <Text color="$accent11" width={280} textAlign='center' margin="auto">一旦您开始使用钱包，您的加密货币和 NFT 活动将显示在这里。</Text>
        </YStack>
      </> : null}
      <Button rounded={30} position='absolute' bottom={20} width="90%" bg="$accent11">
        <Text color="$white1">将加密货币添加到您的钱包</Text>
      </Button>
      <FooterNavBar />
    </YStack>

  )
}