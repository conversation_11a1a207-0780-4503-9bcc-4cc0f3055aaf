import { Avatar, Button, H3, H5, NavB<PERSON>, Paragraph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'
import mainConnetIcon from '../../../assets/images/main-connect.png'
import qrcodeIcon from '../../../assets/images/wallet/qrcode.png'


const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2
})

const Underline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF'
})
const Underlineblock = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
})

export function ConnectDappScreen() {
  const router = useRouter()
  const [currentTab, setCurrentTab] = useState(0)

  return (
    <YStack bg="$background" height={800} >
      <XStack justifyContent='space-between' items="center" pr={16}>
        <NavBar title="连接的dapp" onBack={() => router.back()} />
        <Image source={qrcodeIcon.src} width={16} height={16} />
      </XStack >
      <YStack px={16} position='relative' height={700}>
        <XStack mt={10} gap="$5" height={20}>
          <Pressable onPress={() => setCurrentTab(0)}>
            <View style={{ position: 'relative' }} >
              {currentTab === 0 ? <ActiveText>Web</ActiveText> : <Text color="#fff">Web</Text>}
              {currentTab === 0 && <Underline />}
            </View>
          </Pressable>
          <Pressable onPress={() => setCurrentTab(1)}>
            <View style={{ position: 'relative' }} >
              {currentTab === 1 ? <ActiveText>移动</ActiveText> : <Text color="#fff">移动</Text>}
              {currentTab === 1 && <Underline />}
            </View>
          </Pressable>
        </XStack>
        <YStack cursor="pointer" margin="auto" width={300} flex={1} alignContent='center' alignItems='center' mt="$20">
          <Pressable>
            <Image
              source={mainConnetIcon.src}
              style={{ width: 174, height: 91 }}
            />
          </Pressable>
          <Text fontSize={16} color="#fff" fontWeight="bold" mt="$4" text="center">暂未关联任何去中心化应用(dapp)</Text>
          <Text fontSize={14} color="#8B8F9A" mt="$2" text="center">开始使用钱包后，您关联的去中心化应用(dapp)将显示在这里。</Text>
        </YStack>
      </YStack>
    </YStack >
  )
}