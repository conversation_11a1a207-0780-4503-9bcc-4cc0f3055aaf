import { Avatar, Button, H3, H5, <PERSON>v<PERSON><PERSON>, <PERSON>graph, Confirm<PERSON>utton, XStack, YStack } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, useThemeName } from 'tamagui'
import iconKey from '../../../assets/images/wallet/key.png'
import connectPng from '../../../assets/images/main-connect.png'
import { useWalletStore } from 'app/stores/walletStore'

export function PreaddAccountScreen() {
  const themeName = useThemeName()
  const oppositeColor = themeName === 'light' ? '$color12' : '$color1'
  const router = useRouter()
  const walletStore = useWalletStore()

  const onMnemonicClick = () => {
    router.push('/wallet/backup')
  }

  const handleToUnlock = () => {
    walletStore.createNextAccount()
    // router.push('/wallet/unlock?action=create-account')
  }
  return (
    <YStack bg="$background" flex={1}>
      <YStack flex={1}>
        <NavBar title="" onBack={() => router.back()} />
        <XStack mt={32} pl={16}>
          <H2>添加地址</H2>
        </XStack>
        <YStack px={16} py={40} rowGap={16}>
          <XStack justify={'center'} mt={100}>
            <Image
              source={connectPng.src}
              width={200}
              height={100}
            />

          </XStack>
          <Text fontSize={14} mt={50} px={32}>
            每个地址都包含属于您的钱包的唯一以太坊和Solana地址。每个钱包最多可以添加15个。您也可以将它们导入其他加密货币钱包.
          </Text>
          <Text fontSize={12} color="$color06" mt={30} textAlign='center'>其他地址目前仅支持以太坊和Solana</Text>
        </YStack>
      </YStack>
      <YStack px={16} py={32}>
        <ConfirmButton onPress={handleToUnlock}>
          <Text color={oppositeColor} fontSize={14} fontWeight={700}>继续</Text>
        </ConfirmButton>
      </YStack>

    </YStack>


  )
}