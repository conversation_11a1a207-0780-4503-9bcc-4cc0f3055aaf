import { Avatar, Button, H3, H5, <PERSON>v<PERSON><PERSON>, Paragraph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'
import mainConnetIcon from '../../../assets/images/main-connect.png'


const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2
})

const Underline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF'
})

export function TokenAuthScreen() {
  const router = useRouter()

  return (
    <YStack bg="$background" height={800} >
      <XStack justifyContent='space-between' items="center">
        <NavBar title="代币授权" onBack={() => router.back()} />
      </XStack >
      <YStack px={16} position='relative' height={700}>
        <XStack mt={20} >
          <Pressable>
            <View style={{ position: 'relative' }} >
              <ActiveText>加密货币</ActiveText>
              <Underline />
            </View>
          </Pressable>
        </XStack>
        <YStack cursor="pointer" margin="auto" width={300} flex={1} alignContent='center' alignItems='center' mt="$20">
          <Pressable>
            <Image
              source={mainConnetIcon.src}
              style={{ width: 174, height: 91 }}
            />
          </Pressable>
          <Text fontSize={16} color="#fff" fontWeight="bold" mt="$4" text="center">您没有任何代币授权</Text>
          <Text fontSize={14} color="#8B8F9A" mt="$2" text="center">如果您允许 dapp 将资产从该钱包中转出，则 dapp、资产和允许的金额将在此处列出</Text>
        </YStack>
      </YStack>
    </YStack >
  )
}