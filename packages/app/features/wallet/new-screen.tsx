import { Avatar, Button, H3, H5, <PERSON>v<PERSON><PERSON>, Paragraph, SwitchThemeButton, XStack, YStack } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2 } from 'tamagui'
import iconKey from '../../../assets/images/wallet/key.png'
import iconDuanyu from '../../../assets/images/wallet/duanyu.png'

export function WalletNewScreen() {
  const router = useRouter()

  const onMnemonicClick = () => {
    router.push('/wallet/unlock?action=addWallet')
  }

  return (
    <YStack bg="$background">
      <NavBar title="" onBack={() => router.back()} />
      <XStack mt={32} pl={16}>
        <H2>创建钱包</H2>
      </XStack>
      <YStack px={16} py={40} rowGap={16}>
        <YStack px={32} py={24} borderRadius={12} borderWidth={1} borderColor="$borderColor" borderStyle="solid">
          <XStack items="center">
            <Image source={iconKey.src} width={18} height={18} mr={10} />
            <Text>通行密钥</Text>
          </XStack>
          <XStack>
            <Text color="$color10" fontSize={14} mt={11}>
              通过 iCloud、Google、Yubikey、密码管理器等软件密钥确保安全。
            </Text>
          </XStack>
        </YStack>
        <YStack px={32} py={24} borderRadius={12} borderWidth={1} borderColor="$borderColor" borderStyle="solid"
          onPress={onMnemonicClick}
        >
          <XStack items="center">
            <Image source={iconDuanyu.src} width={18} height={18} mr={10} />
            <Text>恢复短语</Text>
          </XStack>
          <XStack>
            <Text color="$color10" fontSize={14} mt={11}>
              通过 iCloud、Google、Yubikey、密码管理器等软件密钥确保安全。
            </Text>
          </XStack>
        </YStack>
      </YStack>
      {/* <YStack flex={1} justify="center" items="center" gap="$4">
        <Paragraph text="center" fontWeight="700" color="$blue10">{`User ID: `}</Paragraph>
        <Button icon={ArrowLeft} onPress={() => router.back()}>
          Go Home
        </Button>
        <SwitchThemeButton />
      </YStack> */}
    </YStack>

  )
}