import {
  Avatar,
  Button,
  H3,
  H5,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>graph,
  SwitchThemeButton,
  XStack,
  YStack,
  Input,
} from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'
import net3Icon from '../../../assets/images/wallet/net3.png'
import { useTranslation } from 'app/i18n'
import buy1Icon from '../../../assets/images/wallet/buy1.png'
import buy2Icon from '../../../assets/images/wallet/buy2.png'

import changeIcon from '../../../assets/images/wallet/change.png'
import ethIcon from '../../../assets/images/wallet/eth.png'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'

const Underline = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 60,
})
const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2,
})
const ActiveUnderline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF',
})

export function BuyCoinScreen() {
  const router = useRouter()
  const { t } = useTranslation()
  const tabList = [
    t('trading.all') || '全部',
    t('trading.exchange') || '交换',
    t('trading.earn') || '赚取',
    t('trading.socialMedia') || '社交媒体',
    t('trading.manage') || '管理',
    t('trading.listen') || '监听',
  ]
  const [currentTab, setCurrentTab] = useState(0)
  const dataList = [
    {
      id: 1,
      name: 'Aerodrome',
      desc: t('trading.tradeAssets') || '交易资产',
      isSelected: true,
    },
    {
      id: 2,
      name: 'Uniswap',
      desc: t('trading.tradeAssets') || '交易资产',
      isSelected: false,
    },
    {
      id: 3,
      name: 'SushiSwap',
      desc: t('trading.tradeAssets') || '交易资产',
      isSelected: false,
    },
  ]

  const onMnemonicClick = () => {
    router.push('/wallet/password')
  }

  return (
    <YStack bg="$background" position="relative" height={800}>
      <XStack justifyContent="space-between" items="center">
        <NavBar title={t('trading.buy') || '买入'} onBack={() => router.back()} />
        <Image source={net3Icon.src} width={16} height={16} />
      </XStack>
      <YStack>
        <XStack mt={20} position="relative" px={16}>
          <Input
            placeholder="USD"
            width="100%"
            height={100}
            bg="$black1"
            borderColor="$black1"
            borderRadius={10}
            fontSize={54}
            value="0"
            fontWeight="bold"
            p={0}
            color="#3C72F9"
            focusStyle={{ borderColor: 'transparent' }}
          ></Input>
        </XStack>
        <XStack mt={10} items="center" px={16}>
          <Text color="$accent11" fontSize={14} fontWeight="bold">
            0.00000 ETH
          </Text>
        </XStack>
        <Underline />

        <XStack
          mt={10}
          px={16}
          rounded={20}
          bg="#141519"
          width={140}
          height={36}
          items="center"
          ml={16}
        >
          <Image source={buy1Icon.src} width={20} height={20} />
          <Text fontSize={12} ml={4}>
            网络：Base
          </Text>
          <ChevronRight size={20} color="$accent11" />
        </XStack>
        <XStack mt={20} items="center" justifyContent="space-between" px={16}>
          <XStack items="center">
            <Image source={{ uri: '' }} width={28} height={28} rounded={14} bg="$accent11" mr={6} />
            <View>
              <Text color="white" fontSize={14} fontWeight="bold">
                买入
              </Text>
              <Text color="$accent11" fontSize={12} fontWeight={500}>
                KTA
              </Text>
            </View>
          </XStack>
          <XStack justifyContent="flex-end">
            <View />
            <ChevronRight size={20} color="$white6" />
          </XStack>
        </XStack>
        <View width={1} height={16} bg="$black10" ml={30} mt={10}></View>
        <XStack mt={10} items="center" justifyContent="space-between" px={16}>
          <XStack items="center">
            <Image
              source={buy2Icon.src}
              width={28}
              height={28}
              rounded={14}
              bg="$accent11"
              mr={6}
            />
            <View>
              <Text color="white" fontSize={14} fontWeight="bold">
                付款方式
              </Text>
              <Text color="$accent11" fontSize={12} fontWeight={500}>
                选择付款方式
              </Text>
            </View>
          </XStack>
          <XStack justifyContent="flex-end">
            <View />
            <ChevronRight size={20} color="$white6" />
          </XStack>
        </XStack>
      </YStack>
      <Button
        position="absolute"
        bottom={20}
        ml={16}
        rounded={30}
        width="92%"
        style={{
          // backgroundColor: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',
          background: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',
        }}
      >
        <Text color="$black1" fontSize={14} fontWeight="bold">
          继续
        </Text>
      </Button>
    </YStack>
  )
}
