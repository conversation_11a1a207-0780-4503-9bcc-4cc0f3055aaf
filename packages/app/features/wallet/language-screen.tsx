import { NavBar, XStack, YStack } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text } from 'tamagui'
import { Pressable } from 'react-native'
import { Check } from '@tamagui/lucide-icons'
import { useTranslation, Language, SUPPORTED_LANGUAGES } from 'app/i18n'

export function LanguageScreen() {
  const router = useRouter()
  const { t, currentLanguage, setLanguage } = useTranslation()
  
  const handleLanguageSelect = async (language: Language) => {
    await setLanguage(language)
    // 语言切换成功后返回上一页
    setTimeout(() => {
      router.back()
    }, 100) // 短暂延迟确保状态更新完成
  }
  
  return (
    <YStack bg="$background" flex={1}>
      <XStack justifyContent="space-between" items="center">
        <NavBar title={t('navigation.language') || '语言'} onBack={() => router.back()} />
      </XStack>
      
      <YStack px={16} mt={20}>
        <Text fontSize={14} color="$accent11" mb={20}>
          {t('form.selectLanguage') || '选择语言'}
        </Text>
        
        {SUPPORTED_LANGUAGES.map((lang) => (
          <Pressable
            key={lang.code}
            onPress={() => handleLanguageSelect(lang.code)}
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingVertical: 16,
              paddingHorizontal: 16,
              marginBottom: 12,
              backgroundColor: currentLanguage === lang.code ? '#1A1B1F' : '#0F1014',
              borderRadius: 12,
              borderWidth: currentLanguage === lang.code ? 1 : 0,
              borderColor: currentLanguage === lang.code ? '#4575FF' : 'transparent',
            }}
          >
            <View>
              <Text fontSize={16} fontWeight="600" color="white" mb={2}>
                {lang.nativeName}
              </Text>
              <Text fontSize={14} color="$accent11">
                {lang.name}
              </Text>
            </View>
            
            {currentLanguage === lang.code && (
              <Check size={20} color="#4575FF" />
            )}
          </Pressable>
        ))}
        
        <View mt={40} px={16}>
          <Text fontSize={12} color="$accent11" textAlign="center" lineHeight={18}>
            {t('settings.languageNote') || '语言设置将立即生效并保存到本地存储。'}
          </Text>
        </View>
      </YStack>
    </YStack>
  )
}
