import { Ava<PERSON>, Button, H3, H5, <PERSON>v<PERSON><PERSON>, Paragraph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled, Switch } from 'tamagui'
import enoticeIcon from '../../../assets/images/wallet/notice.png'
import settingIcon from '../../../assets/images/setting.png'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'

export function NoticeSettingScreen() {
  const router = useRouter()

  return (
    <YStack bg="$background">
      <XStack justifyContent='space-between' items="center">
        <NavBar title="通知" onBack={() => router.back()} />
      </XStack>
      <YStack px={16} mt={10} mb={20}>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={14} fontWeight="bold">产品公告</Text>
            <Text fontSize={14} fontWeight="bold" color="$accent11" mt={2}>率先了解最新功能</Text>
          </View>
          <View onPress={() => { console.log(11) }}>
            <Switch size="$4">
              <Switch.Thumb animation="bouncy" />
            </Switch>
          </View>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={14} fontWeight="bold">见解和技巧</Text>
          </View>
          <View onPress={() => { console.log(11) }}>
            <Switch size="$4">
              <Switch.Thumb animation="bouncy" />
            </Switch>
          </View>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={14} fontWeight="bold">特别优惠</Text>
          </View>
          <View onPress={() => { console.log(11) }}>
            <Switch size="$4">
              <Switch.Thumb animation="bouncy" />
            </Switch>
          </View>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={14} fontWeight="bold">价格提醒</Text>
            <Text fontSize={14} fontWeight="bold" color="$accent11" mt={2}>资产价格发生大幅变化时收到自动通知</Text>
          </View>
          <View onPress={() => { console.log(11) }}>
            <Switch size="$4">
              <Switch.Thumb animation="bouncy" />
            </Switch>
          </View>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={14} fontWeight="bold">BTC 价格提醒</Text>
            <Text fontSize={14} fontWeight="bold" color="$accent11" mt={2}>BTC 价格发生大幅变化时收到自动通知</Text>
          </View>
          <View onPress={() => { console.log(11) }}>
            <Switch size="$4">
              <Switch.Thumb animation="bouncy" />
            </Switch>
          </View>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={14} fontWeight="bold">ETH 价格提醒</Text>
            <Text fontSize={14} fontWeight="bold" color="$accent11" mt={2}>ETH 价格发生大幅变化时收到自动通知</Text>
          </View>
          <View onPress={() => { console.log(11) }}>
            <Switch size="$4">
              <Switch.Thumb animation="bouncy" />
            </Switch>
          </View>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View width="80%">
            <Text fontSize={14} fontWeight="bold">NFT 报价提醒</Text>
            <Text fontSize={14} fontWeight="bold" color="$accent11" mt={2}>当有人对您的 NFT 出价时收到通知。您不会
              收到极低报价的通知。</Text>
          </View>
          <View onPress={() => { console.log(11) }}>
            <Switch size="$4">
              <Switch.Thumb animation="bouncy" />
            </Switch>
          </View>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={14} fontWeight="bold">账户活动</Text>
          </View>
          <View onPress={() => { console.log(11) }}>
            <Switch size="$4">
              <Switch.Thumb animation="bouncy" />
            </Switch>
          </View>
        </XStack>
      </YStack>
    </YStack >

  )
}