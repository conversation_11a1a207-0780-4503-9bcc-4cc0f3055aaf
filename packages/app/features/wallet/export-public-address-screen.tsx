import { Avatar, Button, H3, H5, <PERSON>v<PERSON><PERSON>, Paragraph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'
import export1Icon from '../../../assets/images/wallet/export1.png'
import export2Icon from '../../../assets/images/wallet/export2.png'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'
import mainConnetIcon from '../../../assets/images/main-connect.png'
import qrcodeIcon from '../../../assets/images/wallet/qrcode.png'


const Line = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 30
})

export function ExportPublicAddressScreen() {
  const router = useRouter()

  return (
    <YStack bg="$background" height={800} >
      <XStack justifyContent='space-between' items="center" pr={16}>
        <NavBar title="" onBack={() => router.back()} />
      </XStack >
      <YStack px={16} mt={20}>
        <Text fontSize={24} fontWeight={800} color="$white1">导出公共地址</Text>
        <XStack mt={10} width="90%" flexDirection='column'>
          <Text fontSize={14} fontWeight={500} color="$accent11">您可以导出钱包的地址和公钥，让适用于加密货币的税务软件安全地跟踪您的交易。导出不会访问您的资金。
            <Text fontSize={14} fontWeight={500} color="#3C72F9">了解更多</Text>
          </Text>
        </XStack>
      </YStack>
      <Line />
      <YStack px={16} mt={20}>
        <Text fontSize={12} fontWeight="bold" color="$accent11">促销</Text>
        <XStack mt={10} items="center" justifyContent='space-between'>
          <Image source={export1Icon.src} width={50} height={50} mr={10} />
          <Text fontSize={14} fontWeight="bold" >值得信赖的加密货币税务解决方案。所有计
            划均可享受 20% 的优惠，外加价值高达140
            美元的高级功能</Text>
        </XStack>
      </YStack>
      <Line />
      <YStack px={16} mt={20}>
        <Text fontSize={12} fontWeight="bold" color="$accent11">促销</Text>
        <XStack mt={10} items="center" justifyContent='space-between'>
          <Image source={export2Icon.src} width={50} height={50} mr={10} />
          <Text fontSize={14} fontWeight="bold" >使用 Crypto Tax Calculator 付费套餐可享受高达 30%的优惠</Text>
        </XStack>
      </YStack>
      <Line />
      <YStack px={16} mt={20}>
        <Text fontSize={12} fontWeight="bold" color="$accent11">地址和XPUB</Text>
        <Button mt={20} rounded={30} style={{
          // backgroundColor: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',
          background: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)'
        }}>
          <Text color="$black1" fontSize={14} fontWeight="bold">复制</Text>
        </Button>
      </YStack>
    </YStack >
  )
}