import { Avatar, Button, H3, H5, Nav<PERSON>ar, Paragraph, SwitchThemeButton, XStack, YStack, PasswordForm } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, Input, Checkbox } from 'tamagui'
import { Check as CheckIcon } from '@tamagui/lucide-icons'
import { useCallback, useState } from 'react'
import { useWalletStore } from 'app/stores/walletStore'

export function CreatePasswordScreen() {
  const walletStore = useWalletStore()

  const router = useRouter()
  const [password, setPassword] = useState('')
  const [termsChecked, setTermsChecked] = useState(false);
  const [newAccountCreationInProgress, setNewAccountCreationInProgress] =
    useState(false);
  // 
  const handleCreateNewWallet = async () => {
    setNewAccountCreationInProgress(true);
    // await createNewAccount(password);
    await walletStore.createNewVaultAndGetSeedPhrase(password)
  };
  const handleCreatePassword = useCallback(async () => {
    if (!password) {
      return;
    }
    await handleCreateNewWallet();
  }, [password])
  return (
    <YStack bg="$background">
      <NavBar title="" onBack={() => router.back()} />
      <XStack mt={32} pl={16}>
        <H2>创建密码</H2>
      </XStack>
      <YStack px={16} py={40} rowGap={16}>
        <PasswordForm onChange={(newPassword) => setPassword(newPassword)} />
        <XStack items="center" mt={24}>
          <Checkbox size="$4" checked={termsChecked} onCheckedChange={() => {
            console.log(termsChecked)
            setTermsChecked(!termsChecked)
          }} >
            <Checkbox.Indicator>
              <CheckIcon />
            </Checkbox.Indicator>
          </Checkbox>
          <Text ml={8} fontSize={12}>Coinbase无法为你恢复些密码</Text>
        </XStack>

        <YStack mt={48}>
          <Button
            disabled={!password || !termsChecked}
            onPress={handleCreatePassword}
          >创建密码</Button>
        </YStack>
      </YStack>


      {/* <YStack flex={1} justify="center" items="center" gap="$4">
        <Paragraph text="center" fontWeight="700" color="$blue10">{`User ID: `}</Paragraph>
        <Button icon={ArrowLeft} onPress={() => router.back()}>
          Go Home
        </Button>
        <SwitchThemeButton />
      </YStack> */}
    </YStack>

  )
}