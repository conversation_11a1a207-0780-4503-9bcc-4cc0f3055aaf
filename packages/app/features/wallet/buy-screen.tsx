import {
  Avatar,
  Button,
  H3,
  H5,
  <PERSON>vBar,
  Paragraph,
  SwitchThemeButton,
  XStack,
  YStack,
  Input,
} from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'
import qrcodeIcon from '../../../assets/images/wallet/qrcode.png'
import changeIcon from '../../../assets/images/wallet/change.png'
import ethIcon from '../../../assets/images/wallet/eth.png'

import { Pressable } from 'react-native'
import { useCallback, useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'
import { FooterNavBar } from '../home/<USER>'
import { Keyboard } from '@my/ui/src/Keyboard'

const Underline = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 20,
})
const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2,
})
const ActiveUnderline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF',
})

export function BuyScreen() {
  const router = useRouter()

  const [input, setInput] = useState('')
  const handleKeyPress = useCallback((key: string) => {
    if (key === 'back') {
      setInput(input.slice(0, -1))
    } else {
      if (input.includes('.') && key === '.') {
        return
      }
      setInput(input + key)
    }

  }, [input])

  return (
    <YStack bg="$background" px={16} py={30} height={'100vh'}>
      <XStack pl={16} justifyContent="space-between">
        <Text></Text>
        <Image source={qrcodeIcon.src} width={16} height={16} />
      </XStack>
      <XStack mt={20} position="relative">
        <Input
          value={input}
          placeholder="USD"
          width="100%"
          height={100}
          bg="$black1"
          borderColor="$black1"
          borderRadius={10}
          fontSize={54}
          fontWeight="bold"
          p={0}
          color="white"
          focusStyle={{ borderColor: 'transparent' }}
        ></Input>
      </XStack>
      <XStack mt={10} items="center">
        <Image source={changeIcon.src} width={14} height={14} mr={6} />
        <Text color="$blue10" fontSize={14} fontWeight="bold">
          0.00000 ETH
        </Text>
      </XStack>
      <XStack mt={30} items="center" justifyContent="space-between">
        <XStack items="center">
          <Image source={ethIcon.src} width={32} height={32} mr={6} />
          <Text color="white" fontSize={14} fontWeight="bold">
            Ethereum
          </Text>
        </XStack>
        <XStack
          justifyContent="flex-end"
          onPress={() => {
            router.push('/wallet/selectAssets')
          }}
        >
          <View mr={5}>
            <Text fontSize={12} fontWeight="bold">
              $0.00
            </Text>
            <Text fontSize={12} color="#8B8F9A">
              可用
            </Text>
          </View>
          <ChevronRight size={20} color="$white6" />
        </XStack>
      </XStack>
      <XStack
        bg="#001032"
        width="100%"
        height={48}
        rounded={10}
        mt={50}
        items="center"
        justify="center"
      >
        <Text fontSize={14} fontWeight="bold" color="$white1">
          向您的钱包添加或请求资金，以开始使用
        </Text>
      </XStack>

      <YStack
        width="90%"
        items="center"
        justifyContent="space-between"
        position="absolute"
        bottom={120}
      >
        <YStack width={'100%'}>
          <Keyboard onKeyPress={handleKeyPress} />
          <YStack height={30}></YStack>
        </YStack>
        <XStack
          width="100%"
          items="center"
          justifyContent="space-between"
        >
          <Pressable
            onPress={() => {
              router.push('/wallet/receiveKeeta')
            }}
          >
            <View
              width={168}
              bg="$accent11"
              height={48}
              rounded={30}
              items="center"
              justifyContent="center"
            >
              <Text>接收</Text>
            </View>
          </Pressable>
          <Pressable
            onPress={() => {
              router.push('/wallet/convertAssets')
            }}
          >
            <View
              width={168}
              height={48}
              rounded={30}
              items="center"
              justifyContent="center"
              style={{
                background: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',
              }}
            >
              <Text color="$black1">添加加密货币</Text>
            </View>
          </Pressable>
        </XStack>

      </YStack>
      <FooterNavBar />
    </YStack>
  )
}
