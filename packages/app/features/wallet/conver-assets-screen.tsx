import {
  Avatar,
  Button,
  H3,
  H5,
  <PERSON>vB<PERSON>,
  Paragraph,
  SwitchThemeButton,
  XStack,
  YStack,
  Input,
} from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'
import net3Icon from '../../../assets/images/wallet/net3.png'
import convert1Icon from '../../../assets/images/wallet/convert1.png'
import convert2Icon from '../../../assets/images/wallet/convert2.png'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'

const Underline = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 60,
})

export function ConvertAssetsScreen() {
  const router = useRouter()

  return (
    <YStack bg="$background" position="relative" height={800}>
      <XStack justifyContent="space-between" items="center">
        <NavBar title="" onBack={() => router.back()} />
      </XStack>
      <YStack mt={40} px={16}>
        <Image source={convert1Icon.src} width={112} height={112} margin="auto" />
        <H3 width="70%" textAlign="center" margin="auto">
          兑换并从一种资产桥接到 另一种资产
        </H3>
        <XStack px={36} width="70%" mt={60}>
          <Image source={convert2Icon.src} width={16} height={16} />
          <YStack ml={10}>
            <Text fontSize={14} fontWeight="bold">
              获得最优价格
            </Text>
            <Text color="$accent11" fontSize={12} fontWeight={500} mt={6} width={260}>
              我们调查超过 75 个去中心化交易所，为您找到最佳兑换路线和价格。
            </Text>
          </YStack>
        </XStack>
        <XStack px={36} width="70%" mt={30}>
          <Image source={convert2Icon.src} width={16} height={16} />
          <YStack ml={10}>
            <Text fontSize={14} fontWeight="bold">
              交易超过 200 万种代币
            </Text>
            <Text color="$accent11" fontSize={12} fontWeight={500} mt={6} width={260}>
              在 Base、以太坊、Arbitrum、Optimism、 Polygon、Solana、BNB 以及 Avalanche 网络上
              进行兑换。
            </Text>
          </YStack>
        </XStack>
        <XStack px={36} width="70%" mt={30}>
          <Image source={convert2Icon.src} width={16} height={16} />
          <YStack ml={10}>
            <Text fontSize={14} fontWeight="bold">
              通过兑换模拟进行保护
            </Text>
            <Text color="$accent11" fontSize={12} fontWeight={500} mt={6} width={260}>
              通过兑换模拟避免资金损失。如果您的兑换可能失败，我们会向您发出警告。
            </Text>
          </YStack>
        </XStack>
      </YStack>
      <YStack px={16} mt={50}>
        <Button
          rounded={30}
          style={{
            // backgroundColor: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',
            background: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',
          }}
          onPress={() => router.push('/wallet/buyCoin')}
        >
          <Text color="$black1" fontSize={14} fontWeight="bold">
            将加密货币添加到您的钱包
          </Text>
        </Button>
        <Button rounded={30} mt={10}>
          <Text fontSize={14} fontWeight="bold">
            了解更多
          </Text>
        </Button>
      </YStack>
    </YStack>
  )
}
