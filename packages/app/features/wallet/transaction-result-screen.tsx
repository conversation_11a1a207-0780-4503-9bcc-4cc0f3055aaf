import { NavBar, XStack, YStack } from '@my/ui'
import { useRouter, useSearchParams } from 'solito/navigation'
import { View, Text, Image, styled } from 'tamagui'
import { useWalletStore } from 'app/stores/walletStore'
import { useTranslation } from 'app/i18n'
import walletIcon from '../../../assets/images/wallet/wallet.png'
import res1Icon from '../../../assets/images/wallet/res1.png'
import res2Icon from '../../../assets/images/wallet/res2.png'
import res3Icon from '../../../assets/images/wallet/res3.png'
import buy16Icon from '../../../assets/images/buy/buy16.png'
import ethIcon from '../../../assets/images/wallet/eth.png'

import { Pressable } from 'react-native'


const Line = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 10,
  mb: 20,
})

export function TransactionResultScreen() {
  const router = useRouter()
  const params = useSearchParams()
  const walletStore = useWalletStore()
  const { t } = useTranslation()

  // 从路由参数获取交易信息
  const txHash = params?.get('txHash') || ''
  const amount = params?.get('amount') || '0'
  const symbol = params?.get('symbol') || 'ETH'
  const toAddress = params?.get('toAddress') || ''
  const status = params?.get('status') || 'success'

  const currentAccount = walletStore.currentAccount
  const fromAddress = currentAccount?.eth?.address || currentAccount?.bsc?.address || ''

  // 格式化地址显示
  const formatAddress = (address: string) => {
    if (!address) return ''
    return `${address.slice(0, 6)}...${address.slice(-4)}`
  }

  // 格式化时间
  const formatTime = () => {
    return new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <YStack bg="$background">
      <XStack justifyContent='space-between' alignItems="center">
        <NavBar onBack={() => router.back()} />
      </XStack>
      <YStack px={16} mt={50} mb={20}>
        <XStack position='relative' width={80} margin="auto">
          <Image source={ethIcon.src} width={60} height={60} margin="auto" />
          <Image source={ethIcon.src} width={24} height={24} position='absolute' bottom={2} right={2} />
        </XStack>
        <Text fontSize={24} fontWeight={800} mt={10} mb={10} textAlign='center'>
          {status === 'success' ? (t('wallet.transactionSuccess') || '已发送') : (t('wallet.transactionFailed') || '发送失败')}
        </Text>
        <Text fontSize={14} fontWeight={500} color="$accent11" textAlign='center'>{formatTime()}</Text>
      </YStack>
      <YStack mx={16} height={158} rounded={20} borderWidth={1} borderColor="#242426" px={16} py={16}>
        <XStack justifyContent='space-between' mb={20}>
          <Image source={walletIcon.src} width={36} height={36} />
          <YStack width="55%">
            <Text fontSize={14} fontWeight="bold">自</Text>
            <Text fontSize={12} fontWeight="bold" color="$accent11" mt={5}>{formatAddress(fromAddress)}</Text>
          </YStack>
          <YStack flexDirection='column' alignItems='flex-end'>
            <Text fontSize={14} fontWeight="bold">-{amount} {symbol}</Text>
            <Text fontSize={12} fontWeight="bold" color="$accent11" mt={5}>{amount} {symbol}</Text>
          </YStack>
        </XStack>
        <Image source={buy16Icon.src} width={5} height={12} ml={16} />
        <XStack justifyContent='space-between' mt={20}>
          <Image source={walletIcon.src} width={36} height={36} />
          <YStack width="55%">
            <Text fontSize={14} fontWeight="bold">至</Text>
            <Text fontSize={12} fontWeight="bold" color="$accent11" mt={5}>{formatAddress(toAddress)}</Text>
          </YStack>
          <YStack flexDirection='column' alignItems='flex-end'>
            <Text fontSize={14} fontWeight="bold" color="#55AE80">+{amount} {symbol}</Text>
            <Text fontSize={12} fontWeight="bold" color="$accent11" mt={5}>{amount} {symbol}</Text>
          </YStack>
        </XStack>
      </YStack>
      <YStack px={16} mt={40} mb={20}>
        <XStack mb={20} alignItems="center" justifyContent="space-between">
          <Text fontSize={14} fontWeight={500}>交易散列</Text>
          <XStack alignItems="center">
            <Text fontSize={14} fontWeight={500} color="$accent11">{formatAddress(txHash)}</Text>
          </XStack>
        </XStack>
        <XStack mb={20} alignItems="center" justifyContent="space-between">
          <Text fontSize={14} fontWeight={500}>网络</Text>
          <XStack alignItems="center">
            <Text fontSize={14} fontWeight={500} color="$accent11">{symbol === 'ETH' ? 'Ethereum' : symbol === 'BNB' ? 'BSC' : 'Solana'}</Text>
            <Image source={ethIcon.src} width={12} height={12} ml={6} />
          </XStack>
        </XStack>
        <XStack mb={20} alignItems="center" justifyContent="space-between">
          <Text fontSize={14} fontWeight={500}>网络费用</Text>
          <XStack alignItems="center">
            <Text fontSize={14} fontWeight={500} color="$accent11">0.00000223 {symbol}</Text>
          </XStack>
        </XStack>
        <XStack mb={20} alignItems="center" justifyContent="space-between">
          <Text fontSize={14} fontWeight={500}>预估合计</Text>
          <XStack alignItems="center">
            <Text fontSize={14} fontWeight={500} color="$accent11">{amount} {symbol}</Text>
          </XStack>
        </XStack>
      </YStack>
      <Line />
      <XStack px={32} py={24} justifyContent='space-between' alignItems='flex-start'>
        <Pressable onPress={() => {
          // 重新发送逻辑
          router.push('/wallet/send')
        }}>
          <YStack alignItems="center" width={80}>
            <View
              width={48}
              height={48}
              borderRadius={24}
              backgroundColor="#2A2D36"
              alignItems="center"
              justifyContent="center"
              mb={8}
            >
              <Image source={res1Icon.src} width={40} height={40} />
            </View>
            <Text fontSize={12} fontWeight={500} textAlign='center' color="white" lineHeight={16}>
              重新发送
            </Text>
          </YStack>
        </Pressable>

        <Pressable onPress={() => {
          // 查看钱包资产
          router.push('/')
        }}>
          <YStack alignItems="center" width={80}>
            <View
              width={48}
              height={48}
              borderRadius={24}
              backgroundColor="#2A2D36"
              alignItems="center"
              justifyContent="center"
              mb={8}
            >
              <Image source={res2Icon.src} width={40} height={40} />
            </View>
            <Text fontSize={12} fontWeight={500} textAlign='center' color="white" lineHeight={16}>
              查看钱包中{'\n'}的资产
            </Text>
          </YStack>
        </Pressable>

        <Pressable onPress={() => {
          // 在区块链浏览器中查看
          if (txHash) {
            const explorerUrl = symbol === 'ETH'
              ? `https://etherscan.io/tx/${txHash}`
              : symbol === 'BNB'
                ? `https://bscscan.com/tx/${txHash}`
                : `https://explorer.solana.com/tx/${txHash}`
            // 这里可以打开外部链接
            console.log('Open explorer:', explorerUrl)
          }
        }}>
          <YStack alignItems="center" width={80}>
            <View
              width={48}
              height={48}
              borderRadius={24}
              backgroundColor="#2A2D36"
              alignItems="center"
              justifyContent="center"
              mb={8}
            >
              <Image source={res3Icon.src} width={40} height={40} />
            </View>
            <Text fontSize={12} fontWeight={500} textAlign='center' color="white" lineHeight={16}>
              在{'\n'}Etherscan{'\n'}上查看
            </Text>
          </YStack>
        </Pressable>
      </XStack>

    </YStack >

  )
}