import { Avatar, Button, H3, H5, NavBar, Paragraph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled, H4 } from 'tamagui'
import convert3Icon from '../../../assets/images/wallet/convert3.png'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'


const Underline = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 60
})

export function BuyTransferScreen() {
  const router = useRouter()

  return (
    <YStack bg="$background" position='relative' height={800}>
      <XStack justifyContent='space-between' items="center">
        <NavBar title="" onBack={() => router.back()} />
      </XStack>
      <YStack mt={60} px={16}>
        <Image source={convert3Icon.src} width={112} height={112} margin="auto" />
        <H5 width="70%" textAlign='center' margin="auto">购买或转账加密货币
        </H5>
        <Text width="80%" textAlign='center' margin="auto" fontSize={14} fontWeight={500} color="$accent11">您可以使用 Coinbase 账户为您的钱包充值。如果
          没有 Coinbase 账户，您也可以使用借记卡每周买
          入 500 美元。</Text>
      </YStack>
      <YStack px={16} mt={100}>
        <Button rounded={30} style={{
          // backgroundColor: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',
          background: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)'
        }}>
          <Text color="$black1" fontSize={14} fontWeight="bold">通过 Coinbase 购买或转移</Text>
        </Button>
        <Button rounded={30} mt={10}>
          <Text fontSize={14} fontWeight="bold">继续使用借记卡</Text>
        </Button>
      </YStack>

    </YStack>
  )
}