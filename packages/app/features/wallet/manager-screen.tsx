import {
  Avatar,
  H5,
  <PERSON>v<PERSON>ar,
  XStack,
  YStack,
} from '@my/ui'
import { ChevronRight } from '@tamagui/lucide-icons'
import { useRouter } from 'solito/navigation'
import { Text, Image, ScrollView } from 'tamagui'
import iconAdd from '../../../assets/images/wallet/add.png'
import iconNew from '../../../assets/images/wallet/new.png'
import iconDaoru from '../../../assets/images/wallet/daoru.png'
import iconJiesuo from '../../../assets/images/wallet/jiesuo.png'
import { useCallback, useEffect, useState } from 'react'
import { useWalletStore } from 'app/stores/walletStore'
import { useTranslation } from 'app/i18n'

export function WalletManagerScreen() {
  const router = useRouter()
  const walletStore = useWalletStore()
  const { t } = useTranslation()
  const [localWallet, setLocalWallet] = useState<any[]>([])
  const handleAction = useCallback((action, wallet) => {
    if (action === 'addAddress') {
      walletStore.setAddAccountWalletId(wallet.walletId)
      router.push('/wallet/preadd-account')
    }
    if (action === 'addWallet') {
      router.push('/wallet/unlock?action=addWallet')
    }
    if (action === 'importWallet') {
      router.push('/wallet/import?action=importWallet')
    }
  }, [])

  // 处理地址点击，跳转到profile页面
  const handleAddressClick = useCallback((wallet: any, account: any, index: number) => {
    try {
      const params = new URLSearchParams({
        walletId: wallet.walletId || '',
        accountId: account.accountId || '',
        accountName: account.name || `地址 ${index + 1}`,
      })
      console.log('Navigating to profile with params:', params.toString())
      router.push(`/user/profile?${params.toString()}`)
    } catch (error) {
      console.error('Navigation error:', error)
      // 降级方案：直接跳转到profile页面
      router.push('/user/profile')
    }
  }, [router])

  useEffect(() => {
    // const _wallet = walletStore.getLocalWallet()
    setLocalWallet(walletStore.walletList)
  }, [walletStore.walletList])

  return (
    <YStack bg="$background" flex={1}>
      <YStack position="absolute" top={0} left={0}>
        <NavBar
          title={t('walletManagement.addAndManage') || '添加和管理钱包'}
          onBack={() => router.back()}
        />
      </YStack>
      <ScrollView flex={1} mt={64} height={'80vh'}>
        {localWallet.map((wallet, index) => (
          <YStack key={wallet.walletId}>
            <XStack mt={32} pl={16}>
              <H5>{`钱包 ${index + 1}`} </H5>
            </XStack>
            {wallet.accounts.map((account: any, index2: number) => (
              <XStack
                key={account.accountId}
                my={20}
                justify="space-between"
                px={16}
                items={'center'}
                onPress={() => handleAddressClick(wallet, account, index2)}
              >
                <XStack items={'center'}>
                  <Avatar circular size="$4">
                    <Avatar.Image
                      src={`https://api.dicebear.com/7.x/identicon/svg?seed=${account.accountId}`}
                      accessibilityLabel={account.accountId}
                    />
                    <Avatar.Fallback backgroundColor="$blue10" />
                  </Avatar>
                  <YStack pl={16}>
                    <Text fontSize={14}>{account.name || `地址 ${index2 + 1}`}</Text>
                    <Text fontSize={12} color="$white11">
                      $
                      {(() => {
                        let total = 0
                        const chains = ['eth', 'bsc', 'btc', 'solana']
                        chains.forEach((chain) => {
                          if (account[chain]?.balance) {
                            total += parseFloat(account[chain].balance) || 0
                          }
                        })
                        return total.toFixed(4)
                      })()}
                    </Text>
                  </YStack>
                </XStack>
                <ChevronRight size={20} color="$white6" />
              </XStack>
            ))}

            <XStack
              my={20}
              justify="space-between"
              px={16}
              items={'center'}
              onPress={() => handleAction('addAddress', wallet)}
            >
              <XStack items={'center'}>
                <Image source={iconAdd.src} width={36} height={36} />
                <YStack pl={16}>
                  <Text fontSize={14}>添加地址</Text>
                </YStack>
              </XStack>
              <ChevronRight size={20} color="$white6" />
            </XStack>
          </YStack>
        ))}

        <YStack height={1} bg="$color6"></YStack>
        <XStack
          my={20}
          justify="space-between"
          px={16}
          items={'center'}
          onPress={() => handleAction('addWallet', '')}
        >
          <XStack items={'center'}>
            <Image source={iconNew.src} width={36} height={36} />
            <YStack pl={16}>
              <Text fontSize={14}>创建新钱包</Text>
            </YStack>
          </XStack>
          <ChevronRight size={20} color="$white6" />
        </XStack>
        <XStack
          my={20}
          justify="space-between"
          px={16}
          items={'center'}
          onPress={() => handleAction('importWallet', '')}
        >
          <XStack items={'center'}>
            <Image source={iconDaoru.src} width={36} height={36} />
            <YStack pl={16}>
              <Text fontSize={14}>导入钱包</Text>
            </YStack>
          </XStack>
          <ChevronRight size={20} color="$white6" />
        </XStack>
        <XStack
          my={20}
          justify="space-between"
          px={16}
          items={'center'}
          onPress={() => router.push('/wallet/backup')}
        >
          <XStack items={'center'}>
            <Image source={iconJiesuo.src} width={36} height={36} />
            <YStack pl={16}>
              <Text fontSize={14}>显示恢复短语</Text>
            </YStack>
          </XStack>
          <ChevronRight size={20} color="$white6" />
        </XStack>
      </ScrollView>

      {/* <YStack flex={1} justify="center" items="center" gap="$4">
        <Paragraph text="center" fontWeight="700" color="$blue10">{`User ID: `}</Paragraph>
        <Button icon={ArrowLeft} onPress={() => router.back()}>
          Go Home
        </Button>
        <SwitchThemeButton />
      </YStack> */}
    </YStack>
  )
}
