import {
  Avatar,
  Button,
  H3,
  H5,
  <PERSON><PERSON><PERSON><PERSON>,
  Para<PERSON>,
  SwitchThemeButton,
  XStack,
  YStack,
  Input,
} from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'
import { useEffect } from 'react'
import {
  useTransactionStore,
  formatTransaction,
  getStatusDisplayText,
  getTypeDisplayText,
} from 'app/stores/transactionStore'
import { useWalletStore } from 'app/stores/walletStore'
import { useTranslation } from 'app/i18n'
import buy1Icon from '../../../assets/images/buy/buy1.png'
import buy2Icon from '../../../assets/images/buy/buy2.png'
import buy3Icon from '../../../assets/images/buy/buy3.png'
import buy4Icon from '../../../assets/images/buy/buy4.png'
import buy5Icon from '../../../assets/images/buy/buy5.png'
import buy6Icon from '../../../assets/images/buy/buy6.png'
import buy7Icon from '../../../assets/images/buy/buy7.png'
import buy8Icon from '../../../assets/images/buy/buy8.png'
import buy9Icon from '../../../assets/images/buy/buy9.png'
import buy10Icon from '../../../assets/images/buy/buy10.png'

import changeIcon from '../../../assets/images/wallet/change.png'
import ethIcon from '../../../assets/images/wallet/eth.png'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'

export function TransactionScreen() {
  const router = useRouter()
  const transactionStore = useTransactionStore()
  const walletStore = useWalletStore()
  const { t } = useTranslation()

  useEffect(() => {
    transactionStore.loadTransactions()
  }, [])

  // 获取当前账户的所有链的交易
  const currentAccount = walletStore.currentAccount
  const transactions = currentAccount
    ? (() => {
      // 获取所有链的地址
      const addresses = [
        currentAccount.eth?.address,
        currentAccount.bsc?.address,
        currentAccount.btc?.address,
        currentAccount.solana?.address,
      ].filter(Boolean) // 过滤掉undefined/null值

      // 使用新的去重方法获取交易记录
      return transactionStore.getTransactionsByAddresses(addresses)
    })()
    : []

  // 按日期分组交易
  const groupTransactionsByDate = (transactions: any[]) => {
    const groups: { [key: string]: any[] } = {}

    transactions.forEach((tx) => {
      const date = new Date(tx.timestamp)
      const today = new Date()
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)

      let dateKey = ''
      if (date.toDateString() === today.toDateString()) {
        dateKey = t('time.today') || '今天'
      } else if (date.toDateString() === yesterday.toDateString()) {
        dateKey = t('time.yesterday') || '昨天'
      } else {
        dateKey = date.toLocaleDateString()
      }

      if (!groups[dateKey]) {
        groups[dateKey] = []
      }
      groups[dateKey].push(formatTransaction(tx))
    })

    return groups
  }

  const groupedTransactions = groupTransactionsByDate(transactions)

  // 获取链图标
  const getChainIcon = (chain: string) => {
    switch (chain) {
      case 'eth':
        return ethIcon.src
      case 'bsc':
        return ethIcon.src // 暂时使用同一个图标
      case 'btc':
        return ethIcon.src
      case 'solana':
        return ethIcon.src
      default:
        return ethIcon.src
    }
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return '#2FAB77'
      case 'pending':
        return '#FFA500'
      case 'failed':
        return '#C7545E'
      default:
        return '#8B8F9A'
    }
  }

  if (transactionStore.isLoading) {
    return (
      <YStack bg="$background" flex={1} justifyContent="center" alignItems="center">
        <Text color="white">加载中...</Text>
      </YStack>
    )
  }

  return (
    <YStack bg="$background">
      <XStack justifyContent="space-between" items="center">
        <NavBar title={t('wallet.transaction') || '交易'} onBack={() => router.back()} />
        <Text color="#4575FF" fontSize={14} fontWeight={500} mr={16}>
          {t('time.filter') || '筛选'}
        </Text>
      </XStack>

      {Object.keys(groupedTransactions).length === 0 ? (
        <YStack flex={1} justifyContent="center" alignItems="center" mt={100}>
          <Text color="$accent11" fontSize={16} textAlign="center">
            暂无交易记录
          </Text>
          <Text color="$accent11" fontSize={14} textAlign="center" mt={10}>
            开始使用钱包后，交易记录将显示在这里
          </Text>
        </YStack>
      ) : (
        Object.entries(groupedTransactions).map(([date, txs]) => (
          <YStack key={date} px={16} mt={20}>
            <Text fontSize={14} fontWeight="bold" color="$accent11" mb={10}>
              {date}
            </Text>
            {txs.map((tx) => (
              <XStack key={tx.id} justifyContent="space-between" mt={25} alignItems="center">
                <XStack alignItems="center">
                  <View position="relative">
                    <Image source={getChainIcon(tx.chain)} width={38} height={38} />
                    {/* <View
                      position="absolute"
                      top={20}
                      right={-5}
                      width={16}
                      height={16}
                      borderRadius={8}
                      backgroundColor={getStatusColor(tx.status)}
                    /> */}
                  </View>
                  <View ml={10}>
                    <Text fontSize={14} fontWeight="bold" color="white">
                      {getTypeDisplayText(tx.type)}
                    </Text>
                    <Text fontSize={12} fontWeight="bold" color="$accent11" mt={2}>
                      {tx.displayAddress}
                    </Text>
                    {/* {tx.status === 'pending' && (
                      <Text fontSize={10} color="#FFA500" mt={1}>
                        待确认
                      </Text>
                    )}
                    {tx.status === 'failed' && (
                      <Text fontSize={10} color="#C7545E" mt={1}>
                        失败
                      </Text>
                    )} */}
                  </View>
                </XStack>
                <YStack flexDirection="column" alignItems="flex-end">
                  <Text fontSize={14} fontWeight="bold" color="white">
                    {tx.displayAmount}
                  </Text>
                  <Text fontSize={12} fontWeight="bold" color="$accent11" mt={2}>
                    {tx.displayTime}
                  </Text>
                  {tx.txHash ? (
                    <Text fontSize={10} color="$accent11" mt={1}>
                      {tx.txHash.slice(0, 8)}...
                    </Text>
                  ) : null}
                </YStack>
              </XStack>
            ))}
          </YStack>
        ))
      )}
    </YStack>
  )
}
