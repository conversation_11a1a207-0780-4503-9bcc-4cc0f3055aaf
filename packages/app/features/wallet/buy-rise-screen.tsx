import {
  Avatar,
  Button,
  H3,
  H<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  SwitchThemeButton,
  XStack,
  YStack,
  Input,
} from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'
import buy1Icon from '../../../assets/images/buy/buy1.png'
import buy2Icon from '../../../assets/images/buy/buy2.png'
import buy3Icon from '../../../assets/images/buy/buy3.png'
import buy4Icon from '../../../assets/images/buy/buy4.png'
import buy5Icon from '../../../assets/images/buy/buy5.png'
import buy6Icon from '../../../assets/images/buy/buy6.png'
import buy7Icon from '../../../assets/images/buy/buy7.png'
import buy8Icon from '../../../assets/images/buy/buy8.png'
import buy9Icon from '../../../assets/images/buy/buy9.png'
import buy10Icon from '../../../assets/images/buy/buy10.png'

import changeIcon from '../../../assets/images/wallet/change.png'
import ethIcon from '../../../assets/images/wallet/eth.png'
import bitcoinIcon from '../../../assets/images/wallet/bitcoin.png'
import bnb_smartIcon from '../../../assets/images/wallet/bnb_smart.png'
import solanaIcon from '../../../assets/images/wallet/solana.png'

import { Pressable } from 'react-native'
import { useEffect, useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'
import { useTransactionStore, formatTransaction, getTypeDisplayText } from 'app/stores/transactionStore'
import { useWalletStore } from 'app/stores/walletStore'
import storage from 'app/utils/storage'

const Line = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 10,
})

export function BuyRiseScreen() {
  const router = useRouter()
  const transactionStore = useTransactionStore()
  const walletStore = useWalletStore()

  useEffect(() => {
    transactionStore.loadTransactions()
  }, [])

  // 获取最新的两条交易记录
  const getLatestTransactions = () => {
    const currentAccount = walletStore.currentAccount
    if (!currentAccount) return []

    // 获取所有链的地址
    const addresses = [
      currentAccount.eth?.address,
      currentAccount.bsc?.address,
      currentAccount.btc?.address,
      currentAccount.solana?.address,
    ].filter(Boolean) // 过滤掉undefined/null值

    // 使用去重方法获取交易记录，并只取前两条
    const allTransactions = transactionStore.getTransactionsByAddresses(addresses)
    return allTransactions.slice(0, 5).map(tx => formatTransaction(tx))
  }

  // 获取链图标
  const getChainIcon = (chain: string) => {
    switch (chain) {
      case 'eth':
        return ethIcon.src
      case 'bsc':
        return bnb_smartIcon.src // 暂时使用同一个图标
      case 'btc':
        return bitcoinIcon.src
      case 'solana':
        return solanaIcon.src
      default:
        return ethIcon.src
    }
  }
  // 获取链图标
  const getChainCoinName = (chain: string) => {
    switch (chain) {
      case 'eth':
        return 'ETH'
      case 'bsc':
        return 'BNB'
      case 'btc':
        return 'BTC'
      case 'solana':
        return 'SOL'
      default:
        return 'ETH'
    }
  }

  const latestTransactions = getLatestTransactions()

  const tabList = [
    {
      icon: buy2Icon.src,
      name: '买入',
      path: '/wallet/buy'
    },
    {
      icon: buy3Icon.src,
      name: '兑换',
      path: '/wallet/buy'
    },
    {
      icon: buy4Icon.src,
      name: '桥接',
    },
    {
      icon: buy5Icon.src,
      name: '提现',
      link: 'https://buy.onramper.com/?apiKey=pk_prod_01HWQY38G07M0VGP4EP17Z6TXT&mode=sell&onlyOfframps=alchemypay&sell_defaultCrypto=USDC_ETHEREUM'
    },
    {
      icon: buy6Icon.src,
      name: '发送',
      path: '/wallet/send',
    },
    {
      icon: buy7Icon.src,
      name: '接收',
      path: '/wallet/receiveKeeta'
    },
    {
      icon: buy8Icon.src,
      name: '短信发送',
    },
    {
      icon: buy9Icon.src,
      name: '分享',
    },
  ]

  const [buyRiseAccount, setBuyRiseAccount] = useState<any>({})
  useEffect(() => {
    try {
      storage.getItem('buyRiseAccount')
        .then(res => {
          if (res) {
            const _account = JSON.parse(res)
            _account.logo = getChainIcon(_account.accountType)
            _account.coinName = getChainCoinName(_account.accountType)
            setBuyRiseAccount(_account)
          }
        })

    } catch (error) {
      console.log(error)
    }
  }, [])

  return (
    <YStack bg="$background">
      <XStack justifyContent="space-between" items="center">
        <NavBar onBack={() => router.back()} />
        <Text fontSize={14} fontWeight={500}>
          {/* 0.5 BNB */}
        </Text>
        <Image source={buy1Icon.src} width={16} height={16} mr={16} />
      </XStack>
      <XStack px={16} justifyContent="space-between" alignItems="center">
        <Image source={buyRiseAccount.logo} width={20} height={20} />
        <Text
          bg="#282B32"
          width={53}
          height={28}
          fontSize={12}
          fontWeight="bold"
          textAlign="center"
          lineHeight={28}
          rounded={30}
        >
          关注
        </Text>
      </XStack>
      <YStack px={16} mt={20}>
        <Text fontSize={14} fontWeight="bold" color="$accent11">
          您的余额
        </Text>
        <Text fontSize={14} fontWeight="bold" mt={20} color="$accent11">
          {buyRiseAccount.balance ? parseFloat(buyRiseAccount.balance).toFixed(4) : '0.0000'} {buyRiseAccount.coinName}
        </Text>
      </YStack>
      <XStack mt={20} justifyContent="space-between" flexWrap="wrap" px={16}>
        {tabList.map((i) => (
          <View
            key={i.name}
            width="25%"
            mb={30}
            onPress={() => {
              if (i.path) {
                router.push(i.path as any)
              }
              if (i.link) {
                window.open(i.link, '_blank')
              }
            }}
          >
            <Image source={i.icon} width={36} height={36} margin="auto" />
            <Text fontSize={12} fontWeight={500} mt={10} textAlign="center">
              {i.name}
            </Text>
          </View>
        ))}
      </XStack>
      <Line />
      {/* 历史记录 */}
      {latestTransactions.length > 0 && (
        <YStack mt={20} px={16}>
          <Text fontSize={16} fontWeight="bold">
            历史记录
          </Text>
          {latestTransactions.map((tx) => (
            <XStack key={tx.id} justifyContent="space-between" mt={20}>
              <XStack>
                <View position="relative">
                  <Image source={getChainIcon(tx.chain)} width={38} height={38} />
                </View>
                <View ml={10}>
                  <Text fontSize={14} fontWeight="bold">
                    {getTypeDisplayText(tx.type)}
                  </Text>
                  <Text fontSize={12} fontWeight="bold" color="$accent11" mt={5}>
                    {tx.displayAddress}
                  </Text>
                </View>
              </XStack>
              <YStack flexDirection="column" alignItems="flex-end">
                <Text fontSize={14} fontWeight="bold">
                  {tx.displayAmount}
                </Text>
                <Text fontSize={12} fontWeight="bold" color="$accent11" mt={5}>
                  {tx.displayTime}
                </Text>
              </YStack>
            </XStack>
          ))}
        </YStack>
      )}
      <Line />
      {/*资源 */}
      <YStack mt={20} px={16}>
        <Text fontSize={16} fontWeight="bold">
          资源
        </Text>
        <Text fontSize={14} color="$accent11">
          没有可用资源
        </Text>
      </YStack>

      {latestTransactions.length === 0 && (
        <YStack mt={60} px={16}>
          <Image source={buy10Icon.src} width={173} height={142} margin="auto" />
          <Text color="$white1" fontSize={16} fontWeight="bold" textAlign="center" mb={10}>
            还没有交易
          </Text>
          <Text fontSize={14} color="$accent11" width={280} textAlign="center" margin="auto">
            一旦您开始使用钱包，您的加密货币和NFT活动将显示在这里。
          </Text>
        </YStack>
      )}
    </YStack>
  )
}
