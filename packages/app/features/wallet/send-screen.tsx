import {
  But<PERSON>,
  <PERSON>v<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ck,
  Y<PERSON><PERSON>ck,
  Input,
  ConfirmButton,
} from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, styled } from 'tamagui'
import { useState, useEffect } from 'react'
import { useWalletStore } from 'app/stores/walletStore'
import { useTransactionStore } from 'app/stores/transactionStore'
import { sendTransaction, estimateTransactionFee } from 'app/services/transactionService'
import { useTranslation } from 'app/i18n'
import buy15Icon from '../../../assets/images/buy/buy15.png'
import buy12Icon from '../../../assets/images/buy/buy12.png'
import buy14Icon from '../../../assets/images/buy/buy14.png'
import buy13Icon from '../../../assets/images/buy/buy13.png'
import bnbIcon from '../../../assets/images/wallet/bnb.png'
import ethIcon from '../../../assets/images/wallet/eth.png'
import arrowRightIcon from '../../../assets/images/wallet/arrowright.png'
import buy16Icon from '../../../assets/images/buy/buy16.png'

import { Pressable } from 'react-native'
import { ChevronRight } from '@tamagui/lucide-icons'

const Line = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 10,
})

export function SendScreen() {
  const router = useRouter()
  const walletStore = useWalletStore()
  const transactionStore = useTransactionStore()
  const { t } = useTranslation()

  const [amount, setAmount] = useState('')
  const [toAddress, setToAddress] = useState('')
  const [estimatedFee, setEstimatedFee] = useState('0')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const currentAccount = walletStore.currentAccount

  // 获取当前钱包中可用的链
  const getAvailableChains = () => {
    if (!currentAccount) return []

    const chains: Array<{ key: 'eth' | 'bsc' | 'btc' | 'solana', name: string, symbol: string, icon: string }> = []
    if (currentAccount.eth) chains.push({ key: 'eth', name: 'Ethereum', symbol: 'ETH', icon: ethIcon.src })
    if (currentAccount.bsc) chains.push({ key: 'bsc', name: 'BSC', symbol: 'BNB', icon: bnbIcon.src })
    if (currentAccount.btc) chains.push({ key: 'btc', name: 'Bitcoin', symbol: 'BTC', icon: bnbIcon.src })
    if (currentAccount.solana) chains.push({ key: 'solana', name: 'Solana', symbol: 'SOL', icon: ethIcon.src })

    return chains
  }

  const availableChains = getAvailableChains()

  // 设置默认选中的链（优先选择第一个可用的链）
  const [selectedChain, setSelectedChain] = useState<'eth' | 'bsc' | 'btc' | 'solana'>(() => {
    if (availableChains.length > 0) {
      return availableChains[0].key as 'eth' | 'bsc' | 'btc' | 'solana'
    }
    return 'eth' // 默认值
  })

  // 获取当前链的余额和信息
  const getCurrentChainInfo = () => {
    if (!currentAccount || !currentAccount[selectedChain]) {
      return null
    }

    const chainInfo = currentAccount[selectedChain]
    const symbols = {
      eth: 'ETH',
      bsc: 'BNB',
      btc: 'BTC',
      solana: 'SOL',
    }

    return {
      ...chainInfo,
      symbol: symbols[selectedChain],
      balance: chainInfo.balance || '0',
      address: chainInfo.address || '',
    }
  }

  // 获取链图标
  const getChainIcon = () => {
    switch (selectedChain) {
      case 'eth':
        return ethIcon.src
      case 'bsc':
        return bnbIcon.src
      case 'btc':
        return bnbIcon.src // 暂时使用 BNB 图标
      case 'solana':
        return ethIcon.src // 暂时使用 ETH 图标
      default:
        return bnbIcon.src
    }
  }

  // 获取链名称
  const getChainName = () => {
    switch (selectedChain) {
      case 'eth':
        return 'Ethereum'
      case 'bsc':
        return 'BSC'
      case 'btc':
        return 'Bitcoin'
      case 'solana':
        return 'Solana'
      default:
        return 'BSC'
    }
  }

  // 获取链符号
  const getChainSymbol = () => {
    switch (selectedChain) {
      case 'eth':
        return 'ETH'
      case 'bsc':
        return 'BNB'
      case 'btc':
        return 'BTC'
      case 'solana':
        return 'SOL'
      default:
        return 'BNB'
    }
  }

  const chainInfo = getCurrentChainInfo()

  // 如果没有当前账户，显示提示
  if (!currentAccount || !currentAccount[selectedChain]) {
    return (
      <YStack bg="$background" flex={1} justifyContent="center" alignItems="center" px={16}>
        <NavBar title={t('wallet.send') || '发送'} onBack={() => router.back()} />
        <YStack mt={100} alignItems="center">
          <Text color="white" fontSize={18} fontWeight="bold" mb={10}>
            未找到钱包账户
          </Text>
          <Text color="#8B8F9A" fontSize={14} textAlign="center" mb={20}>
            请先创建或导入钱包账户
          </Text>
          <Button onPress={() => router.push('/wallet/new')}>
            <Text color="white">创建钱包</Text>
          </Button>
        </YStack>
      </YStack>
    )
  }

  // 地址验证函数
  const validateAddress = (address: string, chain: 'eth' | 'bsc' | 'btc' | 'solana'): boolean => {
    if (!address) return false

    try {
      switch (chain) {
        case 'eth':
        case 'bsc':
          // EVM地址验证
          return /^0x[a-fA-F0-9]{40}$/.test(address)
        case 'solana':
          // Solana地址验证 (Base58格式，32-44个字符)
          return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address)
        case 'btc':
          // BTC地址验证 (简化版)
          return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(address) ||
            /^bc1[a-z0-9]{39,59}$/.test(address) ||
            /^[2mn][a-km-zA-HJ-NP-Z1-9]{33,34}$/.test(address)
        default:
          return false
      }
    } catch {
      return false
    }
  }

  // 金额验证函数  
  const validateAmount = (value: string): { isValid: boolean; error?: string } => {
    if (!value || value.trim() === '') {
      return { isValid: false, error: t('form.required') || '请输入金额' }
    }

    const num = parseFloat(value)
    if (isNaN(num) || !isFinite(num)) {
      return { isValid: false, error: '请输入有效的数字' }
    }

    if (num <= 0) {
      return { isValid: false, error: '金额必须大于0' }
    }

    if (num > parseFloat(chainInfo?.balance || '0')) {
      return { isValid: false, error: t('wallet.insufficientBalance') || '余额不足' }
    }

    // 检查小数位数 (最多8位)
    const decimalPlaces = (value.split('.')[1] || '').length
    if (decimalPlaces > 8) {
      return { isValid: false, error: '小数位数不能超过8位' }
    }

    return { isValid: true }
  }

  // 实时验证
  useEffect(() => {
    if (amount) {
      const validation = validateAmount(amount)
      if (!validation.isValid && validation.error !== (t('wallet.insufficientBalance') || '余额不足')) {
        setError(validation.error || '')
      } else if (validation.isValid) {
        setError('')
      }
    }
  }, [amount, chainInfo?.balance])

  useEffect(() => {
    if (toAddress) {
      if (!validateAddress(toAddress, selectedChain)) {
        setError(`无效的${selectedChain.toUpperCase()}地址格式`)
      } else if (toAddress.toLowerCase() === chainInfo?.address?.toLowerCase()) {
        setError('不能给自己转账')
      } else {
        setError('')
      }
    }
  }, [toAddress, selectedChain, chainInfo?.address])

  // 估算手续费
  useEffect(() => {
    if (amount && toAddress && chainInfo?.address && validateAddress(toAddress, selectedChain) && validateAmount(amount).isValid) {
      setIsLoading(true)
      estimateTransactionFee({
        fromAddress: chainInfo.address,
        toAddress,
        amount,
        chain: selectedChain,
      }).then((result) => {
        setEstimatedFee(result.estimatedFee)
      }).catch((error) => {
        console.error('估算手续费失败:', error)
        setEstimatedFee('0.001') // 设置默认手续费
      }).finally(() => {
        setIsLoading(false)
      })
    } else {
      setEstimatedFee('0')
    }
  }, [amount, toAddress, selectedChain, chainInfo])

  // 发送交易
  const handleSendTransaction = async () => {
    // 基础验证
    if (!chainInfo || !chainInfo.address || !chainInfo.privateKey) {
      setError('钱包信息不完整')
      return
    }

    if (!amount || !toAddress) {
      setError(t('form.required') || '请填写完整信息')
      return
    }

    // 金额验证
    const amountValidation = validateAmount(amount)
    if (!amountValidation.isValid) {
      setError(amountValidation.error || '金额验证失败')
      return
    }

    // 地址验证
    if (!validateAddress(toAddress, selectedChain)) {
      setError(`无效的${selectedChain.toUpperCase()}地址格式`)
      return
    }

    // 检查是否自己给自己转账
    if (toAddress.toLowerCase() === chainInfo.address.toLowerCase()) {
      setError('不能给自己转账')
      return
    }

    // 检查链是否支持
    if (selectedChain === 'btc') {
      setError('BTC转账功能暂未开放')
      return
    }

    setIsLoading(true)
    setError('')

    console.log(`准备发送 ${selectedChain.toUpperCase()} 交易:`, {
      from: chainInfo.address,
      to: toAddress,
      amount: `${amount} ${chainInfo.symbol}`,
      chain: selectedChain
    })

    try {
      // 添加待确认交易记录
      transactionStore.addTransaction({
        txHash: '', // 暂时为空，发送成功后更新
        fromAddress: chainInfo.address || '',
        toAddress,
        amount,
        chain: selectedChain,
        status: 'pending',
        type: 'send',
        symbol: chainInfo.symbol || selectedChain.toUpperCase(),
      })

      // 发送交易
      const result = await sendTransaction({
        fromAddress: chainInfo.address || '',
        toAddress,
        amount,
        privateKey: chainInfo.privateKey || '',
        chain: selectedChain,
      })

      if (result.success && result.txHash) {
        console.log('交易发送成功:', result.txHash)

        // 更新交易记录
        const transactions = transactionStore.transactions
        const pendingTx = transactions.find(
          (tx) =>
            tx.fromAddress === chainInfo.address &&
            tx.toAddress === toAddress &&
            tx.amount === amount &&
            tx.status === 'pending' &&
            !tx.txHash
        )

        if (pendingTx) {
          transactionStore.updateTransaction(pendingTx.id, {
            txHash: result.txHash,
            status: 'confirmed',
            gasUsed: result.gasUsed,
            blockNumber: result.blockNumber,
          })
        }

        // 清空表单
        setAmount('')
        setToAddress('')
        setError('')

        // 延迟刷新余额，避免过于频繁的请求
        setTimeout(() => {
          walletStore.fetchAllBalances()
        }, 3000)

        // 跳转到交易结果页面
        const params = new URLSearchParams({
          txHash: result.txHash,
          amount: amount,
          symbol: chainInfo.symbol || selectedChain.toUpperCase(),
          toAddress: toAddress,
          status: 'success',
          chain: selectedChain
        })
        router.push(`/wallet/transactionResult?${params.toString()}`)
      } else {
        console.error('交易发送失败:', result.error)
        setError(result.error || '交易发送失败，请重试')

        // 更新失败的交易记录
        const transactions = transactionStore.transactions
        const pendingTx = transactions.find(
          (tx) =>
            tx.fromAddress === chainInfo.address &&
            tx.toAddress === toAddress &&
            tx.amount === amount &&
            tx.status === 'pending' &&
            !tx.txHash
        )

        if (pendingTx) {
          transactionStore.updateTransaction(pendingTx.id, {
            status: 'failed',
            error: result.error,
          })
        }
      }
    } catch (error: any) {
      console.error('交易处理异常:', error)
      let errorMessage = '交易处理失败，请重试'

      if (error.message?.includes('network')) {
        errorMessage = '网络连接异常，请检查网络后重试'
      } else if (error.message?.includes('timeout')) {
        errorMessage = '网络超时，请稍后重试'
      } else if (error.message?.includes('insufficient')) {
        errorMessage = '余额不足'
      } else if (error.message) {
        errorMessage = error.message
      }

      setError(errorMessage)

      // 更新失败的交易记录
      const transactions = transactionStore.transactions
      const pendingTx = transactions.find(
        (tx) =>
          tx.fromAddress === chainInfo?.address &&
          tx.toAddress === toAddress &&
          tx.amount === amount &&
          tx.status === 'pending' &&
          !tx.txHash
      )

      if (pendingTx) {
        transactionStore.updateTransaction(pendingTx.id, {
          status: 'failed',
          error: errorMessage,
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  if (!chainInfo) {
    return (
      <YStack bg="$background" flex={1} justifyContent="center" alignItems="center">
        <Text color="white">请先选择钱包账户</Text>
      </YStack>
    )
  }

  return (
    <YStack bg="$background" height={800} position="relative">
      {/* Loading 遮罩 */}
      {isLoading ? (
        <View
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          backgroundColor="rgba(0, 0, 0, 0.7)"
          zIndex={1000}
          justifyContent="center"
          alignItems="center"
        >
          <YStack alignItems="center" backgroundColor="#1A1A1A" borderRadius={12} padding={24}>
            <View
              width={40}
              height={40}
              borderRadius={20}
              borderWidth={3}
              borderColor="#4575FF"
              borderTopColor="transparent"
              mb={16}
            />
            <Text color="white" fontSize={16} fontWeight="bold">
              {t('common.loading') || '发送中...'}
            </Text>
            <Text color="#8B8F9A" fontSize={14} mt={8} textAlign="center">
              请稍候，正在处理您的交易
            </Text>
          </YStack>
        </View>
      ) : null}

      <NavBar title={t('wallet.send') || '发送'} onBack={() => router.back()} />
      <XStack mt={20} position="relative" px={16}>
        <Input
          placeholder={chainInfo?.symbol || getChainSymbol()}
          value={amount}
          onChangeText={setAmount}
          width="100%"
          height={100}
          bg="$black1"
          borderColor="$black1"
          borderRadius={10}
          fontSize={54}
          fontWeight="bold"
          p={0}
          color="#3C72F9"
          borderWidth={0}
          focusStyle={{ borderColor: 'transparent', outlineWidth: 0 }}
          keyboardType="numeric"
        />
        <Pressable
          onPress={() => {
            // 设置为当前链的最大余额（减去一些手续费）
            const maxBalance = parseFloat(chainInfo?.balance || '0')
            const feeReserve = 0.001 // 预留手续费
            const maxAmount = Math.max(0, maxBalance - feeReserve)
            setAmount(maxAmount.toString())
          }}
          style={{
            position: 'absolute',
            right: 16,
            top: 40,
            backgroundColor: '#282B32',
            borderWidth: 1,
            borderColor: '#92929A',
            width: 53,
            height: 28,
            borderRadius: 30,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Text fontSize={12} color="white">
            {t('wallet.max') || '上限'}
          </Text>
        </Pressable>
      </XStack>
      <YStack px={16} mt={20} mb={20}>
        {/* 显示可用链的数量提示 */}
        {/* {availableChains.length > 1 && (
          <Text fontSize={12} color="#8B8F9A" mb={10} textAlign="center">
            点击切换链 ({availableChains.length} 个可用)
          </Text>
        )} */}

        <Pressable
          onPress={() => {
            // 在可用的链之间切换
            if (availableChains.length > 1) {
              const currentIndex = availableChains.findIndex(chain => chain.key === selectedChain)
              const nextIndex = (currentIndex + 1) % availableChains.length
              setSelectedChain(availableChains[nextIndex].key as 'eth' | 'bsc' | 'btc' | 'solana')
              setError('') // 清除错误
            }
          }}
        >
          <XStack justifyContent="space-between" alignItems="center" mb={10}>
            <XStack position="relative">
              <Image source={getChainIcon()} width={32} height={32} mr={10} />
              <Image
                source={buy15Icon.src}
                width={16}
                height={16}
                mr={16}
                position="absolute"
                right={20}
                top={20}
              />
              <Text color="white">{chainInfo?.symbol || getChainSymbol()}</Text>
            </XStack>
            <XStack alignItems="center">
              <Text mr={10} fontSize={12} fontWeight="bold" color="white">
                {chainInfo?.balance || '0'} {chainInfo?.symbol || getChainSymbol()}
              </Text>
              <Image source={arrowRightIcon.src} width={5} height={9} />
            </XStack>
          </XStack>
        </Pressable>
        <Image source={buy16Icon.src} width={5} height={12} ml={16} />
        <XStack alignItems="center" mb={10} mt={10}>
          <XStack position="relative">
            <Image source={buy12Icon.src} width={32} height={32} mr={10} />
            <Image
              source={buy15Icon.src}
              width={16}
              height={16}
              mr={16}
              position="absolute"
              right={-10}
              top={20}
            />
          </XStack>
          <Input
            placeholder={t('wallet.enterRecipientAddress') || '输入接收地址'}
            value={toAddress}
            onChangeText={setToAddress}
            fontSize={14}
            color="white"
            borderWidth={0}
            backgroundColor="transparent"
            flex={1}
          />
        </XStack>
      </YStack>
      <Line />
      <YStack px={16}>
        <XStack mt={30} alignItems="center" justifyContent="space-between">
          <XStack alignItems="center">
            <Image source={getChainIcon()} width={32} height={32} mr={6} />
            <Text color="white" fontSize={14} fontWeight="bold">
              {chainInfo?.symbol || getChainSymbol()}
            </Text>
          </XStack>
          <XStack justifyContent="flex-end">
            <View mr={5} flexDirection="column" alignItems="flex-end">
              <Text fontSize={12} fontWeight="bold">
                {chainInfo?.balance || '0'} {chainInfo?.symbol || getChainSymbol()}
              </Text>
              <Text fontSize={12} color="#8B8F9A">
                可用
              </Text>
            </View>
            <ChevronRight size={20} color="$white6" />
          </XStack>
        </XStack>
      </YStack>

      <YStack px={16}>
        <XStack mt={30} alignItems="center" justifyContent="space-between">
          <Text fontSize={14} fontWeight="bold">
            {t('wallet.usingWallet') || '使用的钱包'}
          </Text>
          <XStack alignItems="center">
            <Text fontSize={14} fontWeight="bold">
              {chainInfo?.address
                ? `${chainInfo.address.slice(0, 6)}...${chainInfo.address.slice(-4)}`
                : '未连接钱包'
              }
            </Text>
            <Image source={buy14Icon.src} width={12} height={12} ml={6} />
          </XStack>
        </XStack>
        <XStack mt={10} alignItems="center" justifyContent="space-between">
          <Text fontSize={14} fontWeight="bold">
            {t('wallet.network') || '网络'}
          </Text>
          <XStack alignItems="center">
            <Text fontSize={14} fontWeight="bold">
              {getChainName()}
            </Text>
          </XStack>
        </XStack>
        <XStack mt={10} alignItems="center" justifyContent="space-between">
          <XStack alignItems="center">
            <Text fontSize={14} fontWeight="bold">
              {t('wallet.networkFee') || '网络费用'}
            </Text>
            <Image source={buy13Icon.src} width={12} height={12} ml={6} />
          </XStack>
          <XStack alignItems="center">
            <Text fontSize={14} fontWeight="bold">
              {estimatedFee} {chainInfo?.symbol || getChainSymbol()}
            </Text>
          </XStack>
        </XStack>

      </YStack>

      {error ? (
        <YStack px={16} py={10}>
          <Text color="red" fontSize={14} textAlign="center">
            {error}
          </Text>
        </YStack>
      ) : null}

      <XStack
        px={16}
        py={24}
        justifyContent="space-between"
        position="absolute"
        bottom={0}
        left={0}
        right={0}
      >
        <Button rounded={30} width="45%">
          <Text>取消</Text>
        </Button>
        <ConfirmButton
          onPress={handleSendTransaction}
          disabled={isLoading || !amount || !toAddress}
          style={{
            width: '45%',
            borderRadius: 30,
            textAlign: 'center',
            lineHeight: 40,
            opacity: !amount || !toAddress ? 0.5 : 1,
          }}
        >
          <Text fontSize={14} fontWeight={700} color="$black1">
            {t('common.confirm') || '确认'}
          </Text>
        </ConfirmButton>
      </XStack>
      {/* <YStack px={16} py={24} position='absolute' bottom={0} left={0} right={0}>
        <ConfirmButton onPress={() => router.push('/wallet/chooseSend')}>
          <Text fontSize={14} fontWeight={700} color="$black1">下一步</Text>
        </ConfirmButton>
      </YStack> */}
    </YStack>
  )
}
