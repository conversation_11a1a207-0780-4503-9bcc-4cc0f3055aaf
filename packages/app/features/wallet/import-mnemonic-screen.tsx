import { <PERSON><PERSON>, Button, H3, H5, <PERSON>v<PERSON><PERSON>, <PERSON>graph, <PERSON><PERSON>rmButton, XStack, YStack } from '@my/ui'
import { useRouter, useSearchParams } from 'solito/navigation'
import { View, Text, Image, H1, H2, useThemeName, TextArea } from 'tamagui'
import iconCopy from '../../../assets/images/wallet/copy.png'
import { useCallback, useEffect, useState } from 'react'
import { Eye, EyeOff } from '@tamagui/lucide-icons'
import { useWalletStore } from 'app/stores/walletStore'
import { useTranslation } from 'app/i18n'

export function ImportMnemonicScreen() {
  const params = useSearchParams()

  const themeName = useThemeName()
  const oppositeColor = themeName === 'light' ? '$color12' : '$color1'

  const router = useRouter()
  const walletStore = useWalletStore()
  const { t } = useTranslation()

  const [copyStatus, setCopyStatus] = useState<'idle' | 'success'>('idle')

  const handleCopy = async () => {
    if (!walletStore.lastestMnemonic) return
    try {
      await navigator.clipboard.writeText(walletStore.lastestMnemonic)
      setCopyStatus('success')
      setTimeout(() => setCopyStatus('idle'), 1000)
    } catch (e) {
      // 可选：处理复制失败
    }
  }

  const [mnemonic, setMnemonic] = useState('')

  const handlePasswordChange = useCallback(async (mnemonic: string) => {
    setMnemonic(mnemonic)
  }, [])

  const handleConfirm = useCallback(() => {
    walletStore.importVaultAndGetSeedPhrase(mnemonic)
      .then(res => {
        console.log('导入成功', res)
        if (res) {
          router.push('/user/home')
          // 导入成功后，更新余额
          walletStore.fetchAllBalances()
            .then(() => {

            })
        }
      })
  }, [mnemonic])

  return (
    <YStack bg="$background" flex={1}>
      <YStack flex={1}>
        <NavBar
          title={t('walletManagement.importMnemonic') || '导入助记词'}
          onBack={() => router.back()}
        />
        <YStack mt={32} px={26}>
          <Text fontSize={14} color="$color10">
            {t('walletManagement.importDescription') ||
              '输入助记词来添加或恢复你的钱包。导入的助记词将被加密并安全存储在你的设备上。为了你的资产安全，不会存储你的助记词。'}
          </Text>
        </YStack>
        <YStack px={16} py={40} rowGap={16}>
          <XStack height={110} px={12} py={12} position="relative" overflow="hidden">
            <TextArea
              flex={1}
              fontSize={14}
              lineHeight={24}
              placeholder={
                t('walletManagement.mnemonicPlaceholder') || '输入助记词单词，并使用空格分隔'
              }
              onChange={(e) => {
                // @ts-ignore
                handlePasswordChange(e.target.value)
              }}
            />
          </XStack>
          <XStack mt={12} onPress={handleCopy}>
            <Image source={iconCopy.src} width={20} height={20} mr={8} />
            <Text fontSize={14}>
              {copyStatus === 'success'
                ? t('success.copySuccess') || '复制成功'
                : t('walletManagement.copyToClipboard') || '复制到剪贴板'}
            </Text>
          </XStack>
        </YStack>
      </YStack>
      <YStack px={16} py={24}>
        <ConfirmButton onPress={handleConfirm}>
          <Text color={oppositeColor} fontSize={14} fontWeight={700}>
            马上导入
          </Text>
        </ConfirmButton>
      </YStack>
    </YStack>
  )
}
