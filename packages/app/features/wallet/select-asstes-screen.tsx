import { Ava<PERSON>, Button, H3, H5, <PERSON>v<PERSON><PERSON>, Paragraph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'
import searchIcon from '../../../assets/images/search.png'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'


const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2
})

const Underline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF'
})

export function SelectAssetsScreen() {
  const router = useRouter()

  return (
    <YStack bg="$background" height={800} >
      <XStack justifyContent='space-between' items="center">
        <NavBar title="选择资产" onBack={() => router.back()} />
      </XStack>
      <XStack mt={20} px={16}>
        <Pressable>
          <View style={{ position: 'relative' }} >
            <ActiveText>加密货币</ActiveText>
            <Underline />
          </View>
        </Pressable>
      </XStack>
      <XStack alignItems='center' mb={20} mt={14} justifyContent="space-between" px={16}>
        <XStack position='relative' width="100%" bg="#0A0B0D">
          <Image source={searchIcon.src} width={16} height={16} mr={10} position='absolute' top={14} left={10} />
          <Input placeholder="搜索或输入网址" width="100%" pl={30} bg="#272A31" rounded={30} />
        </XStack>
      </XStack>
    </YStack >

  )
}