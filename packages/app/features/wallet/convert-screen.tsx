import { Avatar, Button, H3, H5, <PERSON>v<PERSON><PERSON>, Paragraph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'
import net3Icon from '../../../assets/images/wallet/net3.png'
import buy2Icon from '../../../assets/images/wallet/buy2.png'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'


const Underline = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 60
})

export function ConvertScreen() {
  const router = useRouter()

  return (
    <YStack bg="$background" position='relative' height={800}>
      <XStack justifyContent='space-between' items="center">
        <NavBar title="兑换" onBack={() => router.back()} />
        <Image source={net3Icon.src} width={16} height={16} />
      </XStack>
      <YStack>
        <XStack mt={60} position='relative' px={16} items="center" justifyContent='center' height={100}>
          <Text color="#3C72F9" fontSize={60} textAlign='center'>$0</Text>
        </XStack>
        <XStack mt={80} items="center" justifyContent="space-between" px={16}>
          <XStack items="center">
            <Image source={{ uri: '' }} width={28} height={28} rounded={14} bg="$accent11" mr={6} />
            <View>
              <Text color="white" fontSize={14} fontWeight="bold">从</Text>
              <Text color="$accent11" fontSize={12} fontWeight={500}>选择资产</Text>
            </View>
          </XStack>
          <XStack justifyContent="flex-end">
            <View />
            <ChevronRight size={20} color="$white6" />
          </XStack>
        </XStack>
        <View width={1} height={16} bg="$black10" ml={30} mt={10}></View>
        <XStack mt={10} items="center" justifyContent="space-between" px={16}>
          <XStack items="center">
            <Image source={buy2Icon.src} width={28} height={28} rounded={14} bg="$accent11" mr={6} />
            <View>
              <Text color="white" fontSize={14} fontWeight="bold">到 KTA</Text>
              <Text color="$accent11" fontSize={12} fontWeight={500}>在 Base 上</Text>
            </View>
          </XStack>
          <XStack justifyContent="flex-end">
            <View />
            <ChevronRight size={20} color="$white6" />
          </XStack>
        </XStack>
      </YStack>
      <Underline />

      <Button position='absolute' bottom={20} ml={16} rounded={30} width="92%" style={{
        // backgroundColor: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',
        background: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)'
      }}
        onPress={() => {
          router.push('/wallet/convertAssets')
        }}
      >
        <Text color="$black1" fontSize={14} fontWeight="bold">将加密货币添加到您的钱包</Text>
      </Button>
    </YStack>
  )
}