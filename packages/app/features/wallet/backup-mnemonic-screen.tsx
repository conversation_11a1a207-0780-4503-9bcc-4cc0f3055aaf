import { <PERSON><PERSON>, Button, H3, H5, <PERSON>v<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>rm<PERSON>utton, XStack, YStack } from '@my/ui'
import { useRouter, useSearchParams } from 'solito/navigation'
import { View, Text, Image, H1, H2, useThemeName } from 'tamagui'
import iconCopy from '../../../assets/images/wallet/copy.png'
import { useEffect, useState } from 'react'
import { Eye, EyeOff } from '@tamagui/lucide-icons'
import { useWalletStore } from 'app/stores/walletStore'
import { useTranslation } from 'app/i18n'

export function BackupMnemonicScreen() {
  const params = useSearchParams()

  const themeName = useThemeName()
  const oppositeColor = themeName === 'light' ? '$color12' : '$color1'

  const router = useRouter()
  const walletStore = useWalletStore()
  const { t } = useTranslation()

  const [showPassword, setShowPassword] = useState(false)
  const [copyStatus, setCopyStatus] = useState<'idle' | 'success'>('idle')

  useEffect(() => {
    if (params?.get('action') === 'importWallet') {
      // 导入钱包
    } else {
      walletStore.createNewVaultAndGetSeedPhrase()
    }
  }, [params])

  const handleCopy = async () => {
    if (!walletStore.lastestMnemonic) return
    try {
      await navigator.clipboard.writeText(walletStore.lastestMnemonic)
      setCopyStatus('success')
      setTimeout(() => setCopyStatus('idle'), 1000)
    } catch (e) {
      // 可选：处理复制失败
    }
  }

  const handleToHome = () => {
    router.push('/user/home')
  }

  return (
    <YStack bg="$background" flex={1}>
      <YStack flex={1}>
        <NavBar
          title={t('walletManagement.backupWallet') || '备份您的钱包'}
          onBack={() => router.back()}
        />
        <YStack mt={32} px={26}>
          <Text fontSize={14} color="$color10">
            绝对不要分享这些词。任何得知它们的人都可以窃取您所有的加密货币。Coinbase
            绝不会要求您提供这些信息。
          </Text>
          <Text mt={24} fontSize={14} color="$color10">
            以下 12
            个单词是您钱包的恢复短语。该短语可让您在丢失设备时恢复钱包。将其备份到iCloud(推荐)或记下来。或同时采用这两种方式。
          </Text>
        </YStack>
        <YStack px={16} py={40} rowGap={16}>
          <XStack
            height={110}
            px={12}
            py={12}
            borderRadius={12}
            borderWidth={1}
            borderColor="$color9"
            borderStyle="solid"
            position="relative"
            overflow="hidden"
          >
            {!showPassword && (
              <View
                position="absolute"
                top={0}
                left={0}
                right={0}
                bottom={0}
                zIndex={10}
                bg="rgba(255,255,255,0.2)" // 可根据主题调整
                style={{
                  backdropFilter: 'blur(8px)',
                  WebkitBackdropFilter: 'blur(8px)',
                  pointerEvents: 'none',
                }}
              />
            )}

            <Text flex={1} fontSize={14} lineHeight={24} mt={16}>
              {walletStore.lastestMnemonic}
            </Text>
            <YStack width={30} bg="$color2" items="center" justify="center"></YStack>
            <YStack
              position="absolute"
              zIndex={20}
              right={0}
              top={0}
              bottom={0}
              width={30}
              bg="$color2"
              items="center"
              justify="center"
              onPress={() => {
                setShowPassword(!showPassword)
              }}
            >
              {showPassword ? <Eye size={16} /> : <EyeOff size={16} opacity={0.8} />}
            </YStack>
          </XStack>
          <XStack mt={12} onPress={handleCopy}>
            <Image source={iconCopy.src} width={20} height={20} mr={8} />
            <Text fontSize={14}>
              {copyStatus === 'success'
                ? t('success.copySuccess') || '复制成功'
                : t('walletManagement.copyToClipboard') || '复制到剪贴板'}
            </Text>
          </XStack>
        </YStack>
      </YStack>
      <YStack px={16} py={24}>
        <ConfirmButton onPress={handleToHome}>
          <Text color={oppositeColor} fontSize={14} fontWeight={700}>
            已备份
          </Text>
        </ConfirmButton>
      </YStack>
    </YStack>
  )
}
