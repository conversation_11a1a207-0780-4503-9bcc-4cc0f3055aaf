import {
  Avatar,
  Button,
  H3,
  H5,
  <PERSON>v<PERSON><PERSON>,
  Paragraph,
  SwitchThemeButton,
  XStack,
  YStack,
  Input,
} from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'
import net1Icon from '../../../assets/images/wallet/net1.png'
import net2Icon from '../../../assets/images/wallet/net2.png'
import net3Icon from '../../../assets/images/wallet/net3.png'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { FooterNavBar } from '../home/<USER>'

const Underline = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 20,
})
const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2,
})
const ActiveUnderline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF',
})

export function NetworkScreen() {
  const router = useRouter()
  const tabList = ['全部', '交换', '赚取', '社交媒体', '管理', '监听']
  const [currentTab, setCurrentTab] = useState(0)
  const dataList = [
    {
      id: 1,
      name: 'Aerodrome',
      desc: '交易资产',
      isSelected: true,
      url: 'https://aerodrome.finance/',
    },
    {
      id: 2,
      name: 'Uniswap',
      desc: '交易资产',
      isSelected: false,
      url: 'https://app.uniswap.org/swap?disableNFTs=true',
    },
    {
      id: 3,
      name: 'Seamless Protocol',
      desc: '交易资产',
      isSelected: false,
      url: 'https://seamlessprotocol.com/',
    },
  ]

  const onMnemonicClick = () => {
    router.push('/wallet/password')
  }

  const handleOpenUrl = (url: string) => {
    window.location.href = url
  }

  return (
    <YStack bg="$background" minHeight={'100vh'}>
      <XStack pl={16} alignItems="center" mb={32} justifyContent="space-between">
        <NavBar title="" onBack={() => router.back()} />
        <Input placeholder="搜索或输入网址" />
        <View flexDirection="row" justifyContent="space-between" ml={30}>
          <Pressable onPress={() => {}}>
            <Image source={net1Icon.src} width={16} height={16} mr={10} />
          </Pressable>
          <Pressable onPress={() => {}}>
            <Image source={net2Icon.src} width={16} height={16} mr={10} />
          </Pressable>
          <Pressable onPress={() => {}}>
            <Image source={net3Icon.src} width={16} height={16} mr={10} />
          </Pressable>
        </View>
      </XStack>

      <XStack
        pl={16}
        bg="#02A9DE"
        width={343}
        height={80}
        borderRadius={20}
        margin="auto"
        pt={6}
        alignItems="center"
      >
        <Image source={net1Icon.src} width={70} height={70} mr={10} />
        <View>
          <Text color="$black1" fontWeight="bold" fontSize={14}>
            用作资金的免费 NFT
          </Text>
          <Text color="$black1" fontSize={14} fontWeight={500} mt={4}>
            获取特别的 NFT 来为钱包注入资金。
          </Text>
        </View>
      </XStack>

      <Underline />
      <YStack px={16} py={20} rowGap={16} flex={1}>
        <Text>热门应用</Text>
        <XStack gap="$5">
          {tabList.map((i, index) => (
            <Pressable onPress={() => setCurrentTab(index)} key={i}>
              <View style={{ position: 'relative' }}>
                {currentTab === index ? (
                  <ActiveText>{i}</ActiveText>
                ) : (
                  <Text color="#fff">{i}</Text>
                )}
                {currentTab === index && <ActiveUnderline />}
              </View>
            </Pressable>
          ))}
        </XStack>
        <YStack py={10}>
          {dataList.map((i) => (
            <XStack items="center" key={i.id} mb={16} onPress={() => handleOpenUrl(i.url)}>
              <Image
                source={{ uri: '' }}
                width={40}
                height={40}
                rounded={20}
                mr={10}
                bg="$white1"
              />
              <View>
                <XStack>
                  <Text mr={20}>{i.name}</Text>
                  {i.isSelected && (
                    <Text
                      bg="#141519"
                      rounded={10}
                      width={55}
                      height={22}
                      fontSize={12}
                      textAlign="center"
                      lineHeight={22}
                    >
                      精选
                    </Text>
                  )}
                </XStack>
                <Text color="$color10" fontSize={14} mt={11}>
                  {i.desc}
                </Text>
              </View>
            </XStack>
          ))}
        </YStack>
      </YStack>
      <FooterNavBar />
    </YStack>
  )
}
