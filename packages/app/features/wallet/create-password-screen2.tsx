import { Avatar, Button, H3, H5, Nav<PERSON>ar, Paragraph, SwitchThemeButton, XStack, YStack, PasswordInput } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, Input, Checkbox } from 'tamagui'
import { useCallback, useEffect, useState } from 'react'
import { useWalletStore } from 'app/stores/walletStore'

export function CreatePasswordScreen() {
  const walletStore = useWalletStore()

  const router = useRouter()
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')

  const [passwordError, setPasswordError] = useState('');
  const [newAccountCreationInProgress, setNewAccountCreationInProgress] =
    useState(false);
  // 
  const handleCreateNewWallet = async () => {
    setNewAccountCreationInProgress(true);
    // await createNewAccount(password);
    await walletStore.createNewVaultAndGetSeedPhrase(password)
  };
  const handleCreatePassword = useCallback(async () => {
    if (!password) {
      return;
    }
    await handleCreateNewWallet();
  }, [password])


  const handlePasswordInput = useCallback((input) => {
    if (password.length <= 0) {
      setPassword(input)
    } else {
      setConfirmPassword(input)
    }
  }, [password])

  useEffect(() => {
    const setVault = async () => {
      await walletStore.setVault(password)
      router.push('/wallet/new')
    }
    if (password.length >= 6 && confirmPassword.length >= 6) {
      if (password !== confirmPassword) {
        setPasswordError('两次输入密码不一致')
        setTimeout(() => {
          setPassword('')
          setConfirmPassword('')
        }, 500)

      } else {
        setVault()
      }
    }
  }, [password, confirmPassword])


  return (
    <YStack bg="$background" flex={1}>
      <YStack>
        <NavBar title="" onBack={() => router.back()} />
        <YStack mt={32} px={16} boxSizing='border-box' width='100%'>
          <H2 fontSize={24}>{password.length >= 6 ? '确认密码' : '创建密码'} </H2>
          <YStack height={60}>
            <Text mt={12} fontSize={14} color="$color11">
              {password.length >= 6 ? '请确认两次输入密码一致' : '设置6位数字密码以解锁您的钱包。该密码无法用于恢复您的钱包'}
            </Text>
          </YStack>
        </YStack>
        <XStack height={100}></XStack>
        <YStack justify='center' height={24}>
          <Text color="$red10" textAlign='center'>{passwordError}</Text>
        </YStack>
      </YStack>

      <YStack flex={1} px={16} py={40} rowGap={16}>
        <PasswordInput onSuccess={handlePasswordInput} onKeyPress={(key) => { setPasswordError('') }} />
      </YStack>
    </YStack>

  )
}