import { Avatar, Button, H3, H5, NavBar, Paragraph, SwitchThemeButton, XStack, YStack, PasswordInput } from '@my/ui'
import { useRouter, useSearchParams } from 'solito/navigation'
import { View, Text, Image, H1, H2, Input, Checkbox } from 'tamagui'
import { useCallback, useEffect, useState } from 'react'
import { useWalletStore } from 'app/stores/walletStore'

export function UnlockScreen() {
  const walletStore = useWalletStore()
  const router = useRouter()
  const params = useSearchParams()
  console.log(params?.get('action'))

  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')

  const [passwordError, setPasswordError] = useState('');
  const [localVault, setLocalVault] = useState('')

  const handlePasswordInput = useCallback((input) => {
    if (password.length <= 0) {
      setPassword(input)
    } else {
      setConfirmPassword(input)
    }
  }, [password])
  useEffect(() => {
    walletStore.getVault()
      .then(_vault => {
        console.log(_vault)
        setLocalVault(_vault)
      })
  }, [])
  useEffect(() => {
    if (password.length >= 6) {
      // 如果是添加钱包，则跳到创建钱包页面，backup
      if (password !== localVault) {
        setPasswordError('密码错误')
        setTimeout(() => {
          setPassword('')
        }, 500)

      } else {
        // 调用创建派生钱包，直接返回到上个页面创建
        if (params?.get('action') === 'addWallet') {
          router.push('/wallet/backup')
        } else {
          router.back()
        }
      }

    }
  }, [password, localVault, params])


  return (
    <YStack bg="$background" flex={1}>
      <YStack>
        <NavBar title="" onBack={() => router.back()} />
        <XStack mt={100} pl={16} justifyContent='center'>
          <H2 fontSize={24} fontFamily="$body">解锁您的钱包</H2>
        </XStack>
        <XStack height={100}></XStack>
        <YStack justify='center' height={24}>
          <Text color="$red10" textAlign='center'>{passwordError}</Text>
        </YStack>
      </YStack>

      <YStack flex={1} px={16} py={40} rowGap={16}>
        <PasswordInput onSuccess={handlePasswordInput} onKeyPress={(key) => { setPasswordError('') }} />
      </YStack>
    </YStack>

  )
}