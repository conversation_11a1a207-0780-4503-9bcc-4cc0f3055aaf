import { Avatar, Button, H3, H5, <PERSON>v<PERSON><PERSON>, <PERSON>graph, SwitchThemeButton, XStack, YStack, Input, ConfirmButton } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'
import mainConnetIcon from '../../../assets/images/main-connect.png'
import qrcodeIcon from '../../../assets/images/wallet/qrcode.png'
import searchIcon from '../../../assets/images/search.png'
import sbuy11Icon from '../../../assets/images/buy/buy11.png'


const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2
})

const Underline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF'
})
const Underlineblock = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
})

export function ChooseSendScreen() {
  const router = useRouter()
  const [currentTab, setCurrentTab] = useState(1)
  const [recentAddress, setRecentAddress] = useState([])
  const [myAddress, setMyAddress] = useState([])

  const renderReceive = () => {
    return (
      <YStack>
        {recentAddress.length ? recentAddress.map((item: any) => (
          <YStack key={item.address} bg="#272A31" rounded={10} px={10} py={14} mb={10} >
            <XStack justifyContent="space-between">
              <Text color="#fff" fontSize={14} fontWeight={700}>{item.name}</Text>
              <Text color="#fff" fontSize={14} fontWeight={700}>{item.address}</Text>
            </XStack>
          </YStack>
        )) : <YStack cursor="pointer" margin="auto" width={300} flex={1} alignContent='center' alignItems='center' mt={80}>
          <Pressable>
            <Image
              source={sbuy11Icon.src}
              style={{ width: 120, height: 120 }}
            />
          </Pressable>
          <Text fontSize={16} color="#fff" fontWeight="bold" mt="$4" text="center">无近期地址</Text>
        </YStack>}
      </YStack>
    )
  }
  const renderMy = () => {
    return (
      <YStack mt={20}>
        <Text fontSize={12} fontWeight={500} mb={10} color="$accent11">钱包1</Text>
        <XStack justifyContent='space-between' items="center">
          <XStack items="center">
            <Avatar circular size="$4">
              <Avatar.Image
                accessibilityLabel="Cam"
                src="https://images.unsplash.com/photo-1548142813-c348350df52b?&w=150&h=150&dpr=2&q=80"
              />
              <Avatar.Fallback backgroundColor="$blue10" />
            </Avatar>
            <Text fontSize={14} ml={10}>地址1</Text>
          </XStack>
          <YStack alignItems='flex-end'>
            <Text fontSize={14} fontWeight="bold">0.00000 BNB</Text>
            <Text fontSize={12} fontWeight={500}>0xD233...6aA5</Text>
          </YStack>
        </XStack>
      </YStack>
    )
  }

  return (
    <YStack bg="$background" height={800} >
      <XStack justifyContent='space-between' items="center" pr={16}>
        <NavBar title="选择接收人" onBack={() => router.back()} />
        <Image source={qrcodeIcon.src} width={16} height={16} />
      </XStack >
      <YStack px={16} position='relative' height={700}>
        <XStack alignItems='center' mb={20} mt={14} justifyContent="space-between">
          <XStack position='relative' width="100%" bg="#0A0B0D">
            <Image source={searchIcon.src} width={16} height={16} mr={10} position='absolute' top={14} left={10} />
            <Input placeholder="地址、ENS 或 cb.id" width="100%" pl={30} bg="#272A31" rounded={30} borderWidth={1} borderColor='#3C72F6' />
          </XStack>
        </XStack>
        <XStack mt={10} gap="$5" height={20}>
          <Pressable onPress={() => setCurrentTab(0)}>
            <View style={{ position: 'relative' }} >
              {currentTab === 0 ? <ActiveText>最近使用</ActiveText> : <Text color="#fff">最近使用</Text>}
              {currentTab === 0 && <Underline />}
            </View>
          </Pressable>
          <Pressable onPress={() => setCurrentTab(1)}>
            <View style={{ position: 'relative' }} >
              {currentTab === 1 ? <ActiveText>我的地址</ActiveText> : <Text color="#fff">我的地址</Text>}
              {currentTab === 1 && <Underline />}
            </View>
          </Pressable>
        </XStack>
        {currentTab === 0 ? renderReceive() : renderMy()}
      </YStack>
      {
        currentTab === 0 && <YStack px={16} py={24} position='absolute' bottom={0} left={0} right={0}>
          <ConfirmButton onPress={() => router.push('/wallet/chooseSend')}>
            <Text fontSize={14} fontWeight={700} color="$black1">下一步</Text>
          </ConfirmButton>
        </YStack>
      }
    </YStack >
  )
}