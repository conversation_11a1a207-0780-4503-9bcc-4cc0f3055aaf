import { Avatar, Button, H3, H5, <PERSON>v<PERSON><PERSON>, Paragraph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'
import enoticeIcon from '../../../assets/images/wallet/notice.png'
import settingIcon from '../../../assets/images/setting.png'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'

export function NoticeScreen() {
  const router = useRouter()
  const tabList = [
    '全部', '交换', '赚取', '社交媒体', '管理', '监听'
  ]
  const [currentTab, setCurrentTab] = useState(0)
  const dataList = [
    {
      id: 1,
      name: 'Aerodrome',
      desc: '交易资产',
      isSelected: true
    },
    {
      id: 2,
      name: 'Uniswap',
      desc: '交易资产',
      isSelected: false
    },
    {
      id: 3,
      name: 'SushiSwap',
      desc: '交易资产',
      isSelected: false
    },
  ]

  const onMnemonicClick = () => {
    router.push('/wallet/password')
  }

  return (
    <YStack bg="$background" px={16} height={800} >
      <XStack justifyContent='space-between' items="center">
        <NavBar title="通知" onBack={() => router.back()} />
        <Pressable onPress={() => router.push('/wallet/noticeSetting')} >
          <Image source={settingIcon.src} width={16} height={16} />
        </Pressable>
      </XStack>

      <XStack pl={16} justifyContent='center' mt={50}>
        <Image source={enoticeIcon.src} width={173} height={142} />
      </XStack>
      <YStack pl={16} justifyContent='center' mt={20}>
        <Text color="$white1" fontSize={16} fontWeight="bold" textAlign='center' mb={10}>欢迎!</Text>
        <Text color="$accent11" width={280} textAlign='center' margin="auto">探索加密货币世界以开始获取更新。</Text>
      </YStack>
    </YStack >

  )
}