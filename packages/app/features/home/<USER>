import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  H1,
  <PERSON><PERSON>,
  Separator,
  Sheet,
  useToastController,
  XStack,
  YStack,
  H2,
  WalletSheet,
} from '@my/ui'
import { Avatar, Image, Text, styled, XStack as XStackBase } from 'tamagui'
import { ChevronDown, ChevronUp } from '@tamagui/lucide-icons'
import { useCallback, useEffect, useState } from 'react'
import { Platform, View, Pressable, ActivityIndicator } from 'react-native'
import { useLink, useParams, usePathname } from 'solito/navigation'
import { useRouter } from 'solito/navigation'

import searchIcon from '../../../assets/images/search.png'
import copyIcon from '../../../assets/images/copy.png'
import settingIcon from '../../../assets/images/setting.png'
import fot1Icon from '../../../assets/images/fot-icon-1.png'
import fot2Icon from '../../../assets/images/fot-icon-2.png'
import fot3Icon from '../../../assets/images/fot-icon-3.png'
import fot4Icon from '../../../assets/images/fot-icon-4.png'
import fot5Icon from '../../../assets/images/fot-icon-5.png'
import fot1IconActive from '../../../assets/images/fot-icon-1-active.png'
import fot2IconActive from '../../../assets/images/fot-icon-2-active.png'
import fot3IconActive from '../../../assets/images/fot-icon-3-active.png'
import fot4IconActive from '../../../assets/images/fot-icon-4-active.png'
import fot5IconActive from '../../../assets/images/fot-icon-5-active.png'
import mainConnetIcon from '../../../assets/images/main-connect.png'

import { HomePage } from './homePage'
import { useWalletStore } from 'app/stores/walletStore'
import { useTranslation } from 'app/i18n'

const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2,
})

const Underline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF',
})

const FotIconList = [fot1Icon, fot2Icon, fot3Icon, fot4Icon, fot5Icon]
const FotIconListActive = [
  fot1IconActive,
  fot2IconActive,
  fot3IconActive,
  fot4IconActive,
  fot5IconActive,
]

export const FotIconContainer = styled(XStackBase, {
  maxWidth: 640,
  margin: 'auto',
  alignItems: 'center',
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  height: 80,
  backgroundColor: '#131518',
  paddingHorizontal: 20,
  cursor: 'pointer',
  zIndex: 100,
})

export const FooterNavBar = () => {
  const router = useRouter()
  const pathname = usePathname()
  const fotLinks: any = ['/', '/wallet/network', '/wallet/buy', '/wallet/exchange', '/user/home']

  return (
    <FotIconContainer justifyContent="space-between">
      {FotIconList.map((item, index) => (
        <Pressable
          key={index}
          onPress={() => {
            router.push(fotLinks[index])
          }}
        >
          <Image
            source={pathname === fotLinks[index] ? FotIconListActive[index].src : item.src}
            style={{ width: 40, height: 40 }}
          />
        </Pressable>
      ))}
    </FotIconContainer>
  )
}

export function HomeScreen({ pagesMode = false }: { pagesMode?: boolean }) {
  const linkTarget = pagesMode ? '/user' : '/user'
  const toast = useToastController()
  const { t } = useTranslation()
  const linkProps = useLink({
    href: `${linkTarget}/nate`,
  })
  const [currentTab, setCurrentTab] = useState(0)
  const [address, setAddress] = useState(true)
  const [currentAccount, setCurrentAccount] = useState<any>({})
  const walletStore = useWalletStore()
  const router = useRouter()

  const [isOpen, setIsOpen] = useState(false)
  const [selectedWalletId, setSelectedWalletId] = useState(0)
  const [isRefreshing, setIsRefreshing] = useState(false)

  useEffect(() => {
    walletStore.init()

    // 如果没有钱包数据，加载测试数据
    if (walletStore.walletList.length === 0) {
      console.log('没有钱包数据，加载测试数据')
      // const testWalletData = [
      //   {
      //     "mnemonic": "below neutral satoshi inhale hotel inhale humor forum visual citizen element seat",
      //     "walletId": "cb1bfc86-1d02-4f3f-b52a-dca1988297c2",
      //     "accounts": [
      //       {
      //         "walletId": "cb1bfc86-1d02-4f3f-b52a-dca1988297c2",
      //         "accountId": "61053e03-2dbb-4e39-ac12-173dc80da9a6",
      //         "name": "主钱包",
      //         "btc": {
      //           "uid": "97cba0ac-5708-44b7-be93-bebbb2939c94",
      //           "address": "**********************************",
      //           "privateKey": "97ca01b8eb25fc932f875df3acd2b3dc4b7f65090d575d4953f4432fad054bcb",
      //           "accountType": "btc"
      //         },
      //         "eth": {
      //           "uid": "c3aff85b-0f2a-40db-abf7-d14d8e6299a8",
      //           "address": "******************************************",
      //           "privateKey": "0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18c",
      //           "accountType": "eth"
      //         },
      //         "bsc": {
      //           "uid": "7b1e48d5-4c27-48c9-b0c2-70f4a7f4c10e",
      //           "address": "******************************************",
      //           "privateKey": "0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18c",
      //           "accountType": "bsc"
      //         },
      //         "solana": {
      //           "uid": "e1e06ec5-c947-4a21-b516-0743596c8064",
      //           "address": "C5ThiTSKBAhYufh6uqLwzp1SGqWSHtTuE7guRKyVcaXx",
      //           "privateKey": "71143f84a0b1a3beb45cd2882bc1f4a13692465e407aac824207fa170ba781c6a495124a84be84ca5b12032ab0b89950ce0fcc570ec5d545221193ea669cab5f",
      //           "accountType": "solana"
      //         }
      //       },
      //       {
      //         "walletId": "cb1bfc86-1d02-4f3f-b52a-dca1988297c2",
      //         "accountId": "61053e03-2dbb-4e39-ac12-173dc80da9a7",
      //         "name": "备用钱包",
      //         "btc": {
      //           "uid": "97cba0ac-5708-44b7-be93-bebbb2939c95",
      //           "address": "**********************************",
      //           "privateKey": "97ca01b8eb25fc932f875df3acd2b3dc4b7f65090d575d4953f4432fad054bcd",
      //           "accountType": "btc"
      //         },
      //         "eth": {
      //           "uid": "c3aff85b-0f2a-40db-abf7-d14d8e6299a9",
      //           "address": "******************************************",
      //           "privateKey": "0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18d",
      //           "accountType": "eth"
      //         },
      //         "bsc": {
      //           "uid": "7b1e48d5-4c27-48c9-b0c2-70f4a7f4c10f",
      //           "address": "******************************************",
      //           "privateKey": "0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18d",
      //           "accountType": "bsc"
      //         },
      //         "solana": {
      //           "uid": "e1e06ec5-c947-4a21-b516-0743596c8065",
      //           "address": "D5ThiTSKBAhYufh6uqLwzp1SGqWSHtTuE7guRKyVcaXy",
      //           "privateKey": "71143f84a0b1a3beb45cd2882bc1f4a13692465e407aac824207fa170ba781c6a495124a84be84ca5b12032ab0b89950ce0fcc570ec5d545221193ea669cab60",
      //           "accountType": "solana"
      //         }
      //       }
      //     ]
      //   }
      // ]

      // localStorage.setItem('WALLET_LIST', JSON.stringify(testWalletData))
      walletStore.init() // 重新初始化
    }
  }, [])

  useEffect(() => {
    if (walletStore.currentAccount && walletStore.currentAccount.accountId) {
      // 从 store 中获取当前账户，并设置显示名称
      const account = walletStore.currentAccount

      // 如果账户有自定义名称，使用自定义名称；否则使用默认格式
      let accountName = account.name
      if (!accountName) {
        // 查找账户在钱包列表中的索引来生成默认名称
        let accountIndex = 1
        for (const wallet of walletStore.walletList) {
          const foundIndex = wallet.accounts.findIndex((acc: any) => acc.accountId === account.accountId)
          if (foundIndex !== -1) {
            accountIndex = foundIndex + 1
            break
          }
        }
        accountName = (t('home.addressLabel') || '地址{number}').replace('{number}', String(accountIndex))
      }

      setCurrentAccount({
        ...account,
        accountName,
      })
      setSelectedWalletId(account.accountId as any)
    }
  }, [walletStore.currentAccount, walletStore.walletList, t])

  const handleAction = useCallback(
    async (action: string) => {
      if (action === 'search') {
        router.push('/wallet/search')
      }
      if (action === 'copy') {
        try {
          await navigator.clipboard.writeText(currentAccount.eth.address)
          toast.show(t('success.addressCopied') || '地址已复制到剪贴板', { duration: 2000 })
        } catch (err) {
          toast.show(t('home.copyFailed') || '复制失败，请手动复制', { duration: 2000 })
        }
      }
      if (action === 'setting') {
        router.push('/wallet/setting')
      }
    },
    [currentAccount]
  )

  // 手动刷新余额
  const handleRefreshBalance = useCallback(async () => {
    if (isRefreshing) return

    setIsRefreshing(true)
    try {
      await walletStore.fetchAllBalances()
      toast.show(t('success.balanceRefreshed') || '余额已刷新', { duration: 2000 })
    } catch (error) {
      toast.show(t('error.refreshFailed') || '刷新失败，请稍后重试', { duration: 2000 })
    } finally {
      setIsRefreshing(false)
    }
  }, [isRefreshing, walletStore, toast, t])



  return (
    <YStack height="100vh" bg="#0A0B0D" width={'100%'} maxW={640} margin="auto" overflow="hidden">
      <YStack flex={1} gap="$3" p="$4" overflow="scroll" pb={100}>
        <XStack alignItems="center" space="$2" justifyContent="space-between">
          <XStack
            alignItems="center"
            space="$2"
            onPress={() => {
              setIsOpen(true)
            }}
          >
            <Avatar circular size={24} mr={6}>
              <Avatar.Image
                src={`https://api.dicebear.com/7.x/identicon/svg?seed=${currentAccount.accountId}`}
                accessibilityLabel={currentAccount.accountId}
              />
              <Avatar.Fallback backgroundColor="$blue10" />
            </Avatar>
            <Text color="#8B8F9A" fontSize={14}>
              {currentAccount.accountName}
            </Text>
            <ChevronDown color="#8B8F9A" />
          </XStack>
          <XStack alignItems="center" gap="$3">
            <YStack onPress={() => handleAction('search')}>
              <Image source={searchIcon.src} style={{ width: 20, height: 20 }} />
            </YStack>
            <YStack onPress={() => handleAction('copy')}>
              <Image source={copyIcon.src} style={{ width: 20, height: 20, marginHorizontal: 8 }} />
            </YStack>
            <YStack onPress={() => handleAction('setting')}>
              <Image source={settingIcon.src} width={18} style={{ width: 18, height: 18 }} />
            </YStack>
          </XStack>
        </XStack>
        <XStack gap="$2" alignItems="center" justifyContent="space-between">
          <H2 textAlign="left" color="#fff">
            $ {walletStore.getCurrentAccountBalance().toFixed(4)}
          </H2>
          {/* <Pressable
            onPress={handleRefreshBalance}
            disabled={isRefreshing}
            style={{
              backgroundColor: '#282B32',
              paddingHorizontal: 12,
              paddingVertical: 6,
              borderRadius: 16,
              flexDirection: 'row',
              alignItems: 'center',
              opacity: isRefreshing ? 0.6 : 1
            }}
          >
            {isRefreshing ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text color="#fff" fontSize={12}>
                刷新余额
              </Text>
            )}
          </Pressable> */}
        </XStack>
        <HomePage />
      </YStack>
      <WalletSheet
        open={isOpen}
        onOpenChange={setIsOpen}
        wallets={walletStore.walletList}
        selectedId={selectedWalletId}
        onSelect={(wallet, index) => {
          if (wallet === 'addWallet') {
            router.push('/wallet/manager')
          } else {
            // 使用 store 的方法设置当前账户
            walletStore.setCurrentAccount(wallet)
            setSelectedWalletId(wallet.accountId)

            // 设置显示名称
            const accountName = wallet.name || (t('home.addressLabel') || '地址{number}').replace('{number}', String(Number(index) + 1))
            setCurrentAccount({
              ...wallet,
              accountName,
            })
          }
        }}
      />
      <FooterNavBar />
    </YStack>
  )
}
