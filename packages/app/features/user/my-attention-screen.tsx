import {
  Avatar,
  Button,
  H3,
  H5,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>graph,
  SwitchThemeButton,
  XStack,
  YStack,
  Input,
} from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled, Switch, Checkbox } from 'tamagui'
import { Check } from '@tamagui/lucide-icons'
import user1Icon from '../../../assets/images/user/user1.png'
import user2Icon from '../../../assets/images/user/user2.png'
import user3Icon from '../../../assets/images/user/user3.png'
import ethIcon from '../../../assets/images/user/eth.png'
import lineDownIcon from '../../../assets/images/user/lineDown.png'
import lineUpIcon from '../../../assets/images/user/lineUp.png'
import starIcon from '../../../assets/images/user/star.png'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { <PERSON><PERSON><PERSON>, ChevronRight } from '@tamagui/lucide-icons'
import mainConnetIcon from '../../../assets/images/main-connect.png'
import qrcodeIcon from '../../../assets/images/wallet/qrcode.png'

const Line = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 10,
})

export function MyAttentionScreen() {
  const router = useRouter()

  return (
    <YStack bg="$background" height={800}>
      <XStack justifyContent="space-between" items="center" pr={16}>
        <NavBar title="" onBack={() => router.back()} />
      </XStack>
      <YStack px={16} mt={20}>
        <XStack
          justifyContent="space-between"
          items="center"
          onPress={() => {
            router.push('/wallet/search')
          }}
        >
          <Text fontSize={24} fontWeight={800}>
            创建“我的关注”
          </Text>
          <Image source={user3Icon.src} width={32} height={32} />
        </XStack>
        <Text fontSize={14} color="$accent11" mt={10}>
          将项目添加到“我的关注”，即可获得重要价格变动的 提醒
        </Text>
      </YStack>
      <YStack px={16} mt={40}>
        <XStack
          justifyContent="space-between"
          items="center"
          bg="#141519"
          rounded={10}
          height={72}
          px={16}
          mb={10}
          onPress={() => router.push('/wallet/buyRise')}
        >
          <Image source={ethIcon.src} width={32} height={32} />
          <View width="50%">
            <Text fontSize={14}>Coinbase Wrap...</Text>
            <Text fontSize={12} color="$accent11">
              CBBTC
            </Text>
          </View>
          <XStack flexDirection="column" alignItems="flex-end">
            <Text fontSize={14}>US$102,3....</Text>
            <XStack items="center">
              <Image source={lineDownIcon.src} width={9} height={9} />
              <Text fontSize={12} color="#C7545E" ml={4}>
                1.20%
              </Text>
            </XStack>
          </XStack>
          <Image source={starIcon.src} width={14} height={14} />
        </XStack>
        <XStack
          justifyContent="space-between"
          items="center"
          bg="#141519"
          rounded={10}
          height={72}
          px={16}
          mb={10}
          onPress={() => router.push('/wallet/buyRise')}
        >
          <Image source={ethIcon.src} width={32} height={32} />
          <View width="50%">
            <Text fontSize={14}>Coinbase Wrap...</Text>
            <Text fontSize={12} color="$accent11">
              CBBTC
            </Text>
          </View>
          <XStack flexDirection="column" alignItems="flex-end">
            <Text fontSize={14}>US$102,3....</Text>
            <XStack items="center">
              <Image source={lineUpIcon.src} width={9} height={9} />
              <Text fontSize={12} color="#23AD7C" ml={4}>
                1.20%
              </Text>
            </XStack>
          </XStack>
          <Image source={starIcon.src} width={14} height={14} />
        </XStack>
      </YStack>
    </YStack>
  )
}
