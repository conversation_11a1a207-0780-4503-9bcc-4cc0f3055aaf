import { Avatar, Button, H3, H5, NavBar, Paragraph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled, Switch, Checkbox } from 'tamagui'
import { Check } from '@tamagui/lucide-icons'
import user3Icon from '../../../assets/images/user/user3.png'
import user4Icon from '../../../assets/images/user/user4.png'

const Line = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 10
})

export function SettingAccountScreen() {
  const router = useRouter()

  return (
    <YStack bg="$background" height={800} >
      <XStack justifyContent='space-between' items="center" pr={16}>
        <NavBar title="" onBack={() => router.back()} />
      </XStack >
      <YStack px={16} mt={20}>
        <XStack justifyContent='space-between' items="center">
          <Text fontSize={24} fontWeight={800}>选择您的用户名</Text>
          <Image source={user3Icon.src} width={32} height={32} />
        </XStack>
        <Text fontSize={14} color="$accent11" mt={10}>做出明智的选择!您每年只能更改一次。</Text>
        <Text mt={30} mb={10} fontSize={14} fontWeight={500}>用户名</Text>
        <View position='relative'>
          <Input width="100%" height={54} borderColor="#92929A" borderWidth={1} borderRadius={10} bg="transparent"
            fontWeight="bold" p={16} focusStyle={{ borderColor: 'transparent' }} >
          </Input>
          <Text position='absolute' right={16} top={16} color="$accent11" fontSize={14}>.cb.id</Text>
        </View>
        <Text mt={10} mb={30} fontSize={12} fontWeight={500}>由 ENS 提供技术支持</Text>
        <XStack alignItems="center" space="$2">
          <Image source={user4Icon.src} width={78} height={78} />
          <View >
            <Text fontSize={14} fontWeight="bold">使用您自己的 ENS</Text>
            <Text fontSize={12} fontWeight={500} color="$accent11" mt={5} mb={5}>您可以将您的 ENS 名称导入 Coinbase Wallet。</Text>
            <Text fontSize={14} fontWeight={500} color="#3C72F9">了解操作方式</Text>
          </View>
        </XStack>
        <XStack alignItems="center" space="$2" mt={16}>
          <Checkbox size="$4">
            <Checkbox.Indicator>
              <Check />
            </Checkbox.Indicator>
          </Checkbox>
          <Text fontSize={12} fontWeight={500}>我同意<Text color="#3C72F9">子域条款</Text></Text>
        </XStack>
        <Button my={40} rounded={30} onPress={() => { console.log(111) }}
          style={{
            // backgroundColor: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',
            background: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)'
          }}
        >
          <Text fontSize={14} fontWeight="bold" color="$black1">申请我的用户名</Text>
        </Button>
      </YStack>

    </YStack >
  )
}