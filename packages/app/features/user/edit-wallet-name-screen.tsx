import { Nav<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Button, Input } from '@my/ui'
import { useRouter, useSearchParams } from 'solito/navigation'
import { View, Text } from 'tamagui'
import { useState, useEffect } from 'react'
import { useWalletStore } from 'app/stores/walletStore'
import { useTranslation } from 'app/i18n'

export function EditWalletNameScreen() {
  const router = useRouter()
  const params = useSearchParams()
  const walletStore = useWalletStore()
  const { t } = useTranslation()

  // 从路由参数获取信息
  const walletId = params?.get('walletId')
  const accountId = params?.get('accountId')
  const currentName = params?.get('currentName') || ''

  const [walletName, setWalletName] = useState(currentName)
  const [isLoading, setIsLoading] = useState(false)

  // 保存钱包名称
  const handleSave = async () => {
    if (!walletName.trim() || !walletId || !accountId) {
      return
    }

    setIsLoading(true)
    try {
      // 使用store的方法更新钱包名称
      walletStore.updateWalletName(walletId, accountId, walletName.trim())

      // 如果更新的是当前账户，也更新全局当前账户
      if (walletStore.currentAccount.accountId === accountId) {
        const updatedAccount = {
          ...walletStore.currentAccount,
          name: walletName.trim(),
        }
        walletStore.setCurrentAccount(updatedAccount)
      }

      // 返回上一页
      router.back()
    } catch (error) {
      console.error('保存钱包名称失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <YStack bg="$background" flex={1}>
      <XStack justifyContent="space-between" alignItems="center">
        <NavBar title={t('walletManagement.editLabel') || '编辑标签'} onBack={() => router.back()} />
      </XStack>

      <YStack px={16} mt={20} flex={1}>
        <Text fontSize={14} color="$color11" mb={16}>
          {t('walletManagement.labelDescription') || '为您的地址提供标签，以便轻松识别。标签存储在本地，仅限您可以看到。'}
        </Text>

        <Text fontSize={16} fontWeight="600" color="white" mb={12}>
          {t('walletManagement.addressLabel') || '地址标签'}
        </Text>

        <Input
          value={walletName}
          onChangeText={setWalletName}
          placeholder={t('walletManagement.enterWalletName') || '输入钱包名称'}
          fontSize={16}
          height={56}
          borderRadius={12}
          borderWidth={1}
          borderColor="#333"
          backgroundColor="#1A1A1A"
          color="white"
          paddingHorizontal={16}
          maxLength={20}
        />

        <View flex={1} />

        {/* 保存按钮 - 固定在底部 */}
        <View
          paddingBottom={44}
          paddingTop={16}
        >
          <Button
            onPress={handleSave}
            disabled={!walletName.trim() || isLoading}
            height={48}
            borderRadius={24}
            backgroundColor={walletName.trim() ? '#4575FF' : '#333'}
            opacity={walletName.trim() ? 1 : 0.6}
          >
            <Text
              color="white"
              fontSize={16}
              fontWeight="bold"
            >
              {isLoading ? (t('walletManagement.saving') || '保存中...') : (t('walletManagement.save') || '保存')}
            </Text>
          </Button>
        </View>
      </YStack>
    </YStack>
  )
}
