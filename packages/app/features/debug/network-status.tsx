import { useState, useEffect } from 'react'
import { YStack, XStack, Text, Button, View } from '@my/ui'
import { networkManager, checkNetworkHealth, getCacheStatus } from 'app/utils/networkManager'
import { clearBalanceCache } from 'app/stores/utils'

export function NetworkStatusDebug() {
  const [networkStatus, setNetworkStatus] = useState<any>(null)
  const [healthStatus, setHealthStatus] = useState<any>(null)
  const [cacheStatus, setCacheStatus] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  const refreshStatus = async () => {
    setIsLoading(true)
    try {
      // 获取网络状态
      const network = networkManager.getNetworkStatus()
      setNetworkStatus(network)

      // 获取缓存状态
      const cache = getCacheStatus()
      setCacheStatus(cache)

      // 执行健康检查
      const health = await checkNetworkHealth()
      setHealthStatus(health)
    } catch (error) {
      console.error('Failed to refresh status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClearCache = () => {
    clearBalanceCache()
    refreshStatus()
  }

  const handleResetNetwork = () => {
    networkManager.reset()
    refreshStatus()
  }

  useEffect(() => {
    refreshStatus()
  }, [])

  return (
    <YStack p="$4" space="$4" bg="$background">
      <Text fontSize="$6" fontWeight="bold" color="$color">
        网络状态调试
      </Text>

      <XStack space="$2">
        <Button onPress={refreshStatus} disabled={isLoading}>
          {isLoading ? '刷新中...' : '刷新状态'}
        </Button>
        <Button onPress={handleClearCache} variant="outlined">
          清理缓存
        </Button>
        <Button onPress={handleResetNetwork} variant="outlined">
          重置网络
        </Button>
      </XStack>

      {/* 网络状态 */}
      <View bg="$gray2" p="$3" borderRadius="$4">
        <Text fontSize="$5" fontWeight="bold" mb="$2">
          网络状态
        </Text>
        <Text fontSize="$3" color="$gray10">
          失败节点数: {networkStatus?.totalFailedNodes || 0}
        </Text>
        {networkStatus?.failedNodes?.length > 0 && (
          <YStack mt="$2">
            <Text fontSize="$4" fontWeight="bold">失败节点:</Text>
            {networkStatus.failedNodes.map((node: string, index: number) => (
              <Text key={index} fontSize="$3" color="$red10">
                • {node}
              </Text>
            ))}
          </YStack>
        )}
      </View>

      {/* 健康检查 */}
      <View bg="$gray2" p="$3" borderRadius="$4">
        <Text fontSize="$5" fontWeight="bold" mb="$2">
          健康检查
        </Text>
        {healthStatus && Object.entries(healthStatus).map(([chain, status]: [string, any]) => (
          <XStack key={chain} justifyContent="space-between" alignItems="center" mb="$1">
            <Text fontSize="$4" fontWeight="bold" textTransform="uppercase">
              {chain}
            </Text>
            <XStack space="$2" alignItems="center">
              <View
                width={10}
                height={10}
                borderRadius={5}
                bg={status.status === 'healthy' ? '$green10' : '$red10'}
              />
              <Text fontSize="$3" color={status.status === 'healthy' ? '$green10' : '$red10'}>
                {status.status}
              </Text>
              {status.latency && (
                <Text fontSize="$3" color="$gray10">
                  ({status.latency}ms)
                </Text>
              )}
            </XStack>
          </XStack>
        ))}
      </View>

      {/* 缓存状态 */}
      <View bg="$gray2" p="$3" borderRadius="$4">
        <Text fontSize="$5" fontWeight="bold" mb="$2">
          缓存状态
        </Text>
        <Text fontSize="$3" color="$gray10">
          缓存条目: {cacheStatus?.cacheSize || 0}
        </Text>
        <Text fontSize="$3" color="$gray10">
          活跃请求: {cacheStatus?.activeRequests || 0}
        </Text>
        
        {cacheStatus?.cacheEntries?.length > 0 && (
          <YStack mt="$2" maxHeight={200}>
            <Text fontSize="$4" fontWeight="bold">缓存详情:</Text>
            {cacheStatus.cacheEntries.slice(0, 10).map((entry: any, index: number) => (
              <XStack key={index} justifyContent="space-between" mb="$1">
                <Text fontSize="$2" color="$gray10" flex={1}>
                  {entry.key.length > 30 ? `${entry.key.substring(0, 30)}...` : entry.key}
                </Text>
                <Text fontSize="$2" color="$gray10">
                  {Math.round(entry.age / 1000)}s
                </Text>
              </XStack>
            ))}
            {cacheStatus.cacheEntries.length > 10 && (
              <Text fontSize="$3" color="$gray10">
                ...还有 {cacheStatus.cacheEntries.length - 10} 个条目
              </Text>
            )}
          </YStack>
        )}
      </View>

      {/* 使用说明 */}
      <View bg="$blue2" p="$3" borderRadius="$4">
        <Text fontSize="$4" fontWeight="bold" mb="$2" color="$blue10">
          优化说明
        </Text>
        <Text fontSize="$3" color="$blue10">
          • 缓存有效期: 30秒
        </Text>
        <Text fontSize="$3" color="$blue10">
          • 请求超时: 5秒
        </Text>
        <Text fontSize="$3" color="$blue10">
          • 自动重试: 最多3次
        </Text>
        <Text fontSize="$3" color="$blue10">
          • 失败节点会自动切换到备用节点
        </Text>
        <Text fontSize="$3" color="$blue10">
          • 健康检查: 每5分钟自动执行
        </Text>
      </View>
    </YStack>
  )
}
