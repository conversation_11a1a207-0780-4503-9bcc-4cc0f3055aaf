import { create } from 'zustand'
import storage from 'app/utils/storage'

// 支持的语言类型
export type Language = 'zh' | 'en'

// 语言配置接口
interface LanguageConfig {
  code: Language
  name: string
  nativeName: string
}

// 支持的语言列表
export const SUPPORTED_LANGUAGES: LanguageConfig[] = [
  { code: 'zh', name: 'Chinese', nativeName: '简体中文' },
  { code: 'en', name: 'English', nativeName: 'English' },
]

// 翻译文本类型
export interface Translations {
  // 通用
  common: {
    confirm: string
    cancel: string
    save: string
    delete: string
    edit: string
    copy: string
    share: string
    loading: string
    error: string
    success: string
    back: string
    next: string
    done: string
    close: string
  }

  // 钱包相关
  wallet: {
    balance: string
    send: string
    receive: string
    transaction: string
    address: string
    amount: string
    gasPrice: string
    gasLimit: string
    networkFee: string
    totalBalance: string
    availableBalance: string
    sendTransaction: string
    receiveTokens: string
    transactionHistory: string
    copyAddress: string
    shareAddress: string
    insufficientBalance: string
    transactionFailed: string
    transactionSuccess: string
    pending: string
    confirmed: string
    failed: string
    yourAddress: string
    enterRecipientAddress: string
    max: string
    sending: string
    usingWallet: string
    network: string
  }

  // 导航和页面标题
  navigation: {
    home: string
    wallet: string
    settings: string
    security: string
    about: string
    language: string
    network: string
    backup: string
    import: string
    export: string
    display: string
  }

  // 表单和输入
  form: {
    enterAmount: string
    enterAddress: string
    selectNetwork: string
    selectLanguage: string
    password: string
    confirmPassword: string
    mnemonic: string
    privateKey: string
    walletName: string
    required: string
    invalid: string
    tooShort: string
    tooLong: string
    passwordMismatch: string
  }

  // 错误消息
  errors: {
    networkError: string
    invalidAddress: string
    invalidAmount: string
    insufficientFunds: string
    transactionFailed: string
    walletNotFound: string
    invalidMnemonic: string
    invalidPrivateKey: string
    passwordRequired: string
    confirmationFailed: string
    copyFailed: string
  }

  // 成功消息
  success: {
    transactionSent: string
    addressCopied: string
    walletCreated: string
    walletImported: string
    settingsSaved: string
    backupCompleted: string
    copySuccess: string
    balanceRefreshed: string
  }

  // 首页相关
  home: {
    rewards: string
    earnRewards: string
    addToWallet: string
    yearlyEarnings: string
    watchlist: string
    addressLabel: string
    copyFailed: string
    createWatchlist: string
    getPriceAlerts: string
    swapCount: string
    swapAction: string
    boughtPercent: string
    soldPercent: string
  }

  error: {
    refreshFailed: string
  }

  // 钱包管理
  walletManagement: {
    addAndManage: string
    backupWallet: string
    securityWarning: string
    recoveryPhrase: string
    copyToClipboard: string
    neverShare: string
    writeDown: string
    backupToiCloud: string
    importMnemonic: string
    importDescription: string
    enterMnemonic: string
    mnemonicPlaceholder: string
    yourAddress: string
    address: string
    shareAddress: string
    editLabel: string
    addressLabel: string
    labelDescription: string
    enterWalletName: string
    saving: string
    save: string
  }

  // 交易相关
  trading: {
    all: string
    exchange: string
    earn: string
    socialMedia: string
    manage: string
    listen: string
    tradeAssets: string
    buy: string
  }

  // 时间相关
  time: {
    today: string
    yesterday: string
    filter: string
  }

  // 设置相关
  settings: {
    languageNote: string
    languageSubtitle: string
    securitySubtitle: string
    notifications: string
    notificationsSubtitle: string
    aboutSubtitle: string
    version: string
    copyright: string
  }
}

// 多语言状态管理
interface I18nStore {
  currentLanguage: Language
  translations: Translations
  isLoading: boolean

  // 方法
  setLanguage: (language: Language) => Promise<void>
  loadLanguage: (language: Language) => Promise<void>
  t: (key: string) => string
  getCurrentLanguage: () => Language
  getSupportedLanguages: () => LanguageConfig[]
}

const STORAGE_KEY = 'APP_LANGUAGE'

// 获取嵌套对象的值
const getNestedValue = (obj: any, path: string): string => {
  return path.split('.').reduce((current, key) => current?.[key], obj) || path
}

export const useI18nStore = create<I18nStore>((set, get) => ({
  currentLanguage: 'zh',
  translations: {} as Translations,
  isLoading: false,

  setLanguage: async (language: Language) => {
    set({ isLoading: true })
    try {
      await get().loadLanguage(language)
      set({ currentLanguage: language })
      await storage.setItem(STORAGE_KEY, language)
    } catch (error) {
      console.error('Failed to set language:', error)
    } finally {
      set({ isLoading: false })
    }
  },

  loadLanguage: async (language: Language) => {
    try {
      // 动态导入语言文件
      const translations = await import(`./locales/${language}`)
      set({ translations: translations.default })
    } catch (error) {
      console.error(`Failed to load language ${language}:`, error)
      // 如果加载失败，使用默认中文
      if (language !== 'zh') {
        const defaultTranslations = await import('./locales/zh')
        set({ translations: defaultTranslations.default })
      }
    }
  },

  t: (key: string) => {
    const translations = get().translations
    return getNestedValue(translations, key)
  },

  getCurrentLanguage: () => get().currentLanguage,

  getSupportedLanguages: () => SUPPORTED_LANGUAGES,
}))

// 初始化多语言系统
export const initializeI18n = async () => {
  console.log('Initializing i18n system...')
  const store = useI18nStore.getState()

  try {
    // 从存储中获取保存的语言设置
    const savedLanguage = (await storage.getItem(STORAGE_KEY)) as Language
    const language = savedLanguage || 'zh'

    console.log('Loading language:', language)
    await store.loadLanguage(language)
    useI18nStore.setState({ currentLanguage: language })
    console.log('i18n initialized successfully with language:', language)
  } catch (error) {
    console.error('Failed to initialize i18n:', error)
    // 使用默认语言
    await store.loadLanguage('zh')
  }
}

// Hook for easy access to translation function
export const useTranslation = () => {
  const { t, currentLanguage, setLanguage, getSupportedLanguages } = useI18nStore()

  return {
    t,
    currentLanguage,
    setLanguage,
    getSupportedLanguages,
  }
}
