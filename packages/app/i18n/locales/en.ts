import { Translations } from '../index'

const en: Translations = {
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    copy: 'Copy',
    share: 'Share',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    back: 'Back',
    next: 'Next',
    done: 'Done',
    close: 'Close',
  },

  wallet: {
    balance: 'Balance',
    send: 'Send',
    receive: 'Receive',
    transaction: 'Transaction',
    address: 'Address',
    amount: 'Amount',
    gasPrice: 'Gas Price',
    gasLimit: 'Gas Limit',
    networkFee: 'Network Fee',
    totalBalance: 'Total Balance',
    availableBalance: 'Available Balance',
    sendTransaction: 'Send Transaction',
    receiveTokens: 'Receive Tokens',
    transactionHistory: 'Transaction History',
    copyAddress: 'Copy Address',
    shareAddress: 'Share',
    insufficientBalance: 'Insufficient Balance',
    transactionFailed: 'Transaction Failed',
    transactionSuccess: 'Transaction Success',
    pending: 'Pending',
    confirmed: 'Confirmed',
    failed: 'Failed',
    yourAddress: 'Your Address',
    enterRecipientAddress: 'Enter recipient address',
    max: 'Max',
    sending: 'Sending...',
    usingWallet: 'Using Wallet',
    network: 'Network',
  },

  navigation: {
    home: 'Home',
    wallet: 'Wallet',
    settings: 'Settings',
    security: 'Security',
    about: 'About',
    language: 'Language',
    network: 'Network',
    backup: 'Backup',
    import: 'Import',
    export: 'Export',
    display: 'Display',
  },

  form: {
    enterAmount: 'Enter Amount',
    enterAddress: 'Enter Address',
    selectNetwork: 'Select Network',
    selectLanguage: 'Select Language',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    mnemonic: 'Mnemonic',
    privateKey: 'Private Key',
    walletName: 'Wallet Name',
    required: 'Please fill in all required fields',
    invalid: 'Invalid',
    tooShort: 'Too Short',
    tooLong: 'Too Long',
    passwordMismatch: 'Password Mismatch',
  },

  errors: {
    networkError: 'Network Error',
    invalidAddress: 'Invalid Address',
    invalidAmount: 'Invalid Amount',
    insufficientFunds: 'Insufficient Funds',
    transactionFailed: 'Transaction Failed',
    walletNotFound: 'Wallet Not Found',
    invalidMnemonic: 'Invalid Mnemonic',
    invalidPrivateKey: 'Invalid Private Key',
    passwordRequired: 'Password Required',
    confirmationFailed: 'Confirmation Failed',
    copyFailed: 'Copy failed',
  },

  success: {
    transactionSent: 'Transaction Sent',
    addressCopied: 'Address copied to clipboard',
    walletCreated: 'Wallet Created',
    walletImported: 'Wallet Imported',
    settingsSaved: 'Settings Saved',
    backupCompleted: 'Backup Completed',
    copySuccess: 'Copy Success',
    balanceRefreshed: 'Balance Refreshed',
  },

  error: {
    refreshFailed: 'Refresh failed, please try again later',
  },

  // 首页相关
  home: {
    rewards: 'Rewards',
    earnRewards: 'Earn {rate}% rewards',
    addToWallet: 'Add {token} on {network} to your wallet',
    yearlyEarnings: 'Earn {rate}% annually',
    watchlist: 'Watchlist',
    addressLabel: 'Address {number}',
    copyFailed: 'Copy failed, please copy manually',
    createWatchlist: 'Create "My Watchlist"',
    getPriceAlerts: 'Get price alerts and stay informed',
    swapCount: 'swaps',
    swapAction: 'Swap',
    boughtPercent: 'Bought {percent}%',
    soldPercent: 'Sold {percent}%',
  },

  // 钱包管理
  walletManagement: {
    addAndManage: 'Add and manage wallets',
    backupWallet: 'Backup your wallet',
    securityWarning:
      'Never share these words. Anyone who knows them can steal all your cryptocurrency. Coinbase will never ask for this information.',
    recoveryPhrase:
      'The following 12 words are your wallet recovery phrase. This phrase allows you to recover your wallet if you lose your device. Back it up to iCloud (recommended) or write it down. Or use both methods.',
    copyToClipboard: 'Copy to clipboard',
    neverShare: 'Never share',
    writeDown: 'Write down',
    backupToiCloud: 'Backup to iCloud',
    importMnemonic: 'Import mnemonic',
    importDescription:
      'Enter your mnemonic phrase to add or restore your wallet. The imported mnemonic will be encrypted and securely stored on your device. For your asset security, your mnemonic will not be stored.',
    enterMnemonic: 'Enter mnemonic',
    mnemonicPlaceholder: 'Enter mnemonic words separated by spaces',
    yourAddress: 'Your',
    address: 'address',
    shareAddress: 'Share',
    editLabel: 'Edit Label',
    addressLabel: 'Address Label',
    labelDescription:
      'Provide a label for your address for easy identification. Labels are stored locally and only you can see them.',
    enterWalletName: 'Enter wallet name',
    saving: 'Saving...',
    save: 'Save',
  },

  // 交易相关
  trading: {
    all: 'All',
    exchange: 'Exchange',
    earn: 'Earn',
    socialMedia: 'Social Media',
    manage: 'Manage',
    listen: 'Listen',
    tradeAssets: 'Trade Assets',
    buy: 'Buy',
  },

  // 时间相关
  time: {
    today: 'Today',
    yesterday: 'Yesterday',
    filter: 'Filter',
  },

  settings: {
    languageNote:
      'Language settings will take effect on next app launch. Some features may require reload.',
    languageSubtitle: 'Choose app language',
    securitySubtitle: 'Password and security settings',
    notifications: 'Notifications',
    notificationsSubtitle: 'Manage notification settings',
    aboutSubtitle: 'Version info and help',
    version: 'Version',
    copyright: '© 2024 Coinbase Wallet',
  },
}

export default en
