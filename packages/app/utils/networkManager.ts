import { ETH_RPC, BSC_RPC, SOLANA_RPC, ETH_RPC_BACKUP, BSC_RPC_BACKUP, SOLANA_RPC_BACKUP } from './constants'

// 网络状态管理
class NetworkManager {
  private failedNodes = new Set<string>()
  private retryCount = new Map<string, number>()
  private maxRetries = 3
  private retryDelay = 1000 // 1秒

  // 获取可用的RPC节点
  getAvailableRpc(chain: 'eth' | 'bsc' | 'solana'): string {
    const rpcMap = {
      eth: [ETH_RPC, ETH_RPC_BACKUP],
      bsc: [BSC_RPC, BSC_RPC_BACKUP],
      solana: [SOLANA_RPC, SOLANA_RPC_BACKUP]
    }

    const rpcs = rpcMap[chain]
    for (const rpc of rpcs) {
      if (!this.failedNodes.has(rpc)) {
        return rpc
      }
    }

    // 如果所有节点都失败了，重置失败状态并返回主节点
    this.resetFailedNodes(chain)
    return rpcs[0]
  }

  // 标记节点失败
  markNodeFailed(rpc: string) {
    this.failedNodes.add(rpc)
    const count = this.retryCount.get(rpc) || 0
    this.retryCount.set(rpc, count + 1)

    // 设置定时器恢复节点状态
    setTimeout(() => {
      this.failedNodes.delete(rpc)
      this.retryCount.delete(rpc)
    }, this.retryDelay * Math.pow(2, count)) // 指数退避
  }

  // 重置特定链的失败节点
  private resetFailedNodes(chain: 'eth' | 'bsc' | 'solana') {
    const rpcMap = {
      eth: [ETH_RPC, ETH_RPC_BACKUP],
      bsc: [BSC_RPC, BSC_RPC_BACKUP],
      solana: [SOLANA_RPC, SOLANA_RPC_BACKUP]
    }

    rpcMap[chain].forEach(rpc => {
      this.failedNodes.delete(rpc)
      this.retryCount.delete(rpc)
    })
  }

  // 检查节点是否可用
  isNodeAvailable(rpc: string): boolean {
    return !this.failedNodes.has(rpc)
  }

  // 获取网络状态
  getNetworkStatus() {
    return {
      failedNodes: Array.from(this.failedNodes),
      retryCount: Object.fromEntries(this.retryCount),
      totalFailedNodes: this.failedNodes.size
    }
  }

  // 重置所有网络状态
  reset() {
    this.failedNodes.clear()
    this.retryCount.clear()
  }
}

// 单例实例
export const networkManager = new NetworkManager()

// 带重试的请求函数
export const requestWithRetry = async <T>(
  requestFn: () => Promise<T>,
  rpc: string,
  maxRetries = 3
): Promise<T> => {
  let lastError: Error

  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await requestFn()
      return result
    } catch (error) {
      lastError = error as Error
      console.warn(`Request failed (attempt ${i + 1}/${maxRetries}) for ${rpc}:`, error.message)
      
      if (i === maxRetries - 1) {
        // 最后一次重试失败，标记节点失败
        networkManager.markNodeFailed(rpc)
        break
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }

  throw lastError!
}

// 网络健康检查
export const checkNetworkHealth = async () => {
  const chains = ['eth', 'bsc', 'solana'] as const
  const results = {}

  for (const chain of chains) {
    const rpc = networkManager.getAvailableRpc(chain)
    try {
      const start = Date.now()
      
      if (chain === 'solana') {
        // Solana健康检查
        const response = await fetch(rpc, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 1,
            method: 'getHealth'
          })
        })
        await response.json()
      } else {
        // EVM链健康检查
        const response = await fetch(rpc, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 1,
            method: 'eth_blockNumber',
            params: []
          })
        })
        await response.json()
      }
      
      const latency = Date.now() - start
      results[chain] = { status: 'healthy', latency, rpc }
    } catch (error) {
      results[chain] = { status: 'unhealthy', error: error.message, rpc }
      networkManager.markNodeFailed(rpc)
    }
  }

  return results
}

// 定期健康检查
let healthCheckInterval: NodeJS.Timeout | null = null

export const startHealthCheck = (intervalMs = 60000) => {
  if (healthCheckInterval) {
    clearInterval(healthCheckInterval)
  }

  healthCheckInterval = setInterval(async () => {
    try {
      const health = await checkNetworkHealth()
      console.log('Network health check:', health)
    } catch (error) {
      console.error('Health check failed:', error)
    }
  }, intervalMs)
}

export const stopHealthCheck = () => {
  if (healthCheckInterval) {
    clearInterval(healthCheckInterval)
    healthCheckInterval = null
  }
}
