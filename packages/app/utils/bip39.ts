
import * as bip39 from 'bip39'
import { HDNodeWallet, uuidV4, randomBytes } from 'ethers'
import { HDKey } from '@scure/bip32'
import * as bitcoin from 'bitcoinjs-lib'
import { generateMnemonic as generateMnemonicBtc, mnemonicToSeed } from '@scure/bip39'
import { Keypair } from '@solana/web3.js'
import * as wif from 'wif'

export function generateUID() {
  return uuidV4(randomBytes(16))
}
export function generateMnemonic() {
  return bip39.generateMnemonic(128);
}

export const coinType = {
  btc: '0',
  eth: '60',
  bsc: '60',
  solana: '501'
}

export type CoinTypeKey = typeof coinType

export function generateWallet(mnemonic: string, coinType: String, index = 0) {
  const path = `m/44'/${coinType}'/0'/0/${index}`
  // const path = `m/44'/501'/0'/0'`
  console.log(path)
  const wallet = HDNodeWallet.fromPhrase(mnemonic, path);
  // const wallet = hdNode.derivePath(); // ETH/BSC 标准路径

  console.log("ETH/BSC 地址:", wallet.address);
  console.log("私钥:", wallet.privateKey);

  return {
    uid: generateUID(),
    mnemonic,
    address: wallet.address,
    privateKey: wallet.privateKey
  }
  
}

export function generateWalletEth(mnemonic: string, index = 0) {
  const account = generateWallet(mnemonic, coinType.eth, index)
  return {
    ...account,
    accountType: 'eth'
  }
}

export function generateWalletBsc(mnemonic: string, index = 0) {
  const account = generateWallet(mnemonic, coinType.bsc, index)
  return {
    ...account,
    accountType: 'bsc'
  }
}


export async function generateWalletBtc(mnemonic: string, index = 0) {
  // const mnemonic = generateMnemonicBtc(wordlist)

  const seed = await mnemonicToSeed(mnemonic)
  const root = HDKey.fromMasterSeed(seed)
  const path = `m/44'/0'/0'/0/${index}`
  const child = root.derive(path)
  // const network = bitcoin.networks.bitcoin
  const network = bitcoin.networks.testnet
  // @ts-ignore
  const { address } = bitcoin.payments.p2pkh({ pubkey: Buffer.from(child.publicKey), network: network })
  const wifKey = wif.encode({
    // version: 0x80, // 
    version: 0xEF, // 如果是 testnet 用 239
    privateKey: Buffer.from(child.privateKey!),
    compressed: true
  })

  return {
    uid: generateUID(),
    mnemonic,
    address,
    privateKey: Buffer.from(child.privateKey!).toString('hex'),
    wif: wifKey,
    accountType: 'btc'
    
  }
}


export async function generateWalletSolana(mnemonic: string, index = 0) {
  const seed = await mnemonicToSeed(mnemonic)
  const root = HDKey.fromMasterSeed(seed)
  const path = `m/44'/501'/0'/0'/${index}`
  const child = root.derive(path)
  // Solana 只用前32字节作为私钥
  const keypair = Keypair.fromSeed(child.privateKey!.subarray(0, 32))
  const address = keypair.publicKey.toBase58()
  
  return {
    uid: generateUID(),
    mnemonic,
    address,
    privateKey: Buffer.from(keypair.secretKey).toString('hex'),
    accountType: 'solana'
  }
}