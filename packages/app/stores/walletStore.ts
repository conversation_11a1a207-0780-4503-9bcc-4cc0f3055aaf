import {
  generateUID,
  generateWalletBsc,
  generateWalletBtc,
  generateWalletEth,
  generateWalletSolana,
} from 'app/utils/bip39'
import { PASSWORD_VAULT } from 'app/utils/constants'
import storage from 'app/utils/storage'
import { generateMnemonic } from 'bip39'
import { create } from 'zustand'
import { fetchAllBalances } from './utils'

type User = {
  id: string
  name: string
}

type Account = {
  walletId: string
  accountId: string
  name?: string
  btc?: any
  eth: any
  bsc: any
  solana: any
}

type WalletStore = {
  user: User | null
  lastestMnemonic: string
  walletList: any[]
  currentAccount: Account
  init: () => void
  getLocalWallet: () => any[]
  createNewVaultAndGetSeedPhrase: (passorwd?: string) => Promise<string>
  importVaultAndGetSeedPhrase: (passorwd?: string) => Promise<string>
  setVault: (vault: string) => Promise<void>
  getVault: () => Promise<string>

  addAccountWalletId: string
  setAddAccountWalletId: (walletId: string) => void
  createNextAccount: () => void
  fetchAllBalances: () => Promise<any>
  updateWalletBalances: (balances: any) => void
  updateWalletName: (walletId: string, accountId: string, name: string) => void
  setCurrentAccount: (account: Account) => void
  getTotalBalance: () => number
  getCurrentAccountBalance: () => number
}

export async function createNewVault(passorwd: string) {
  await storage.setItem(PASSWORD_VAULT, passorwd)
}

export async function createWallet(_mnemonic?: string) {
  const mnemonic = _mnemonic || generateMnemonic()
  const accountBtc = await generateWalletBtc(mnemonic, 0)
  const accountEth = await generateWalletEth(mnemonic, 0)
  const accountBsc = await generateWalletBsc(mnemonic, 0)
  const accountSolana = await generateWalletSolana(mnemonic, 0)
  const _walletId = generateUID()
  return {
    mnemonic,
    walletId: _walletId,
    accounts: [
      {
        walletId: _walletId,
        accountId: generateUID(),
        btc: accountBtc,
        eth: accountEth,
        bsc: accountBsc,
        solana: accountSolana,
      },
    ],
  }
}

export async function createWalletNext(mnemonic: string, index: number) {
  const accountEth = await generateWalletEth(mnemonic, index)
  const accountBsc = await generateWalletBsc(mnemonic, index)
  const accountSolana = await generateWalletSolana(mnemonic, 0)

  return {
    mnemonic,
    accounts: {
      accountId: generateUID(),
      eth: accountEth,
      bsc: accountBsc,
      solana: accountSolana,
    },
  }
}

export const useWalletStore = create<WalletStore>((set, get) => ({
  user: null,
  lastestMnemonic: '',
  walletList: [],
  currentAccount: {
    walletId: '',
    accountId: '',
    eth: null,
    bsc: null,
    solana: null,
  },
  addAccountWalletId: '',
  init: () => {
    // 初始化钱包
    get().getLocalWallet()
    // 暂时注释掉自动余额查询，避免频繁网络请求
    // setTimeout(() => {
    //   get().fetchAllBalances()
    // }, 2000) // 延迟2秒
  },
  getLocalWallet: () => {
    try {
      const _walletList = JSON.parse(localStorage.getItem('WALLET_LIST') || '[]')
      set({ walletList: _walletList })

      // 尝试恢复上次选中的账户
      const lastAccountId = localStorage.getItem('CURRENT_ACCOUNT_ID')
      let foundAccount = null

      if (lastAccountId && _walletList.length > 0) {
        // 查找上次选中的账户
        for (const wallet of _walletList) {
          const account = wallet.accounts.find((acc: any) => acc.accountId === lastAccountId)
          if (account) {
            foundAccount = account
            break
          }
        }
      }

      // 如果没有找到上次的账户，使用第一个账户
      if (!foundAccount && _walletList[0] && _walletList[0].accounts[0]) {
        foundAccount = _walletList[0].accounts[0]
      }

      if (foundAccount) {
        set({ currentAccount: foundAccount })
      }

      return _walletList
    } catch (error) {
      return []
    }
  },
  setVault: async (vault: string) => {
    await storage.setItem(PASSWORD_VAULT, vault)
  },
  getVault: async () => {
    return (await storage.getItem(PASSWORD_VAULT)) || ''
  },
  createNewVaultAndGetSeedPhrase: async (passorwd?: string) => {
    const _password = passorwd || (await get().getVault())
    if (_password) {
      await createNewVault(_password)
      const wallet = await createWallet()

      set({ lastestMnemonic: wallet.mnemonic })
      const _walletList = [...get().walletList, wallet]
      set({ walletList: _walletList })
      // 持久缓存
      storage.setItem('WALLET_LIST', JSON.stringify(_walletList))

      return wallet.mnemonic
    }
    return ''
  },
  importVaultAndGetSeedPhrase: async (mnemonic?: string) => {
    // 要先查找钱包list里有没有相关的注记词，如果有，则不导入
    const exists = get().walletList.some((wallet) => wallet.mnemonic === mnemonic)
    if (exists) {
      // 已存在，不导入
      return ''
    }
    const wallet = await createWallet(mnemonic)

    set({ lastestMnemonic: wallet.mnemonic })
    const _walletList = [...get().walletList, wallet]
    set({ walletList: _walletList })
    // 持久缓存
    storage.setItem('WALLET_LIST', JSON.stringify(_walletList))

    return wallet.mnemonic
  },
  setAddAccountWalletId: (walletId: string) => {
    set({ addAccountWalletId: walletId })
  },
  createNextAccount: async () => {
    const walletId = get().addAccountWalletId
    const walletList = get().walletList
    const walletIndex = walletList.findIndex((wallet) => wallet.walletId === walletId)
    const wallet = walletList[walletIndex]

    if (wallet) {
      const newAccount = await createWalletNext(wallet.mnemonic, wallet.accounts.length)
      wallet.accounts = [...wallet.accounts, newAccount.accounts]
      walletList[walletIndex] = { ...wallet }
      set({ walletList: walletList })
      storage.setItem('WALLET_LIST', JSON.stringify(walletList))
    }
  },
  fetchAllBalances: async () => {
    try {
      const walletList = get().walletList
      if (!walletList || walletList.length === 0) {
        console.log('没有钱包需要获取余额')
        return
      }

      console.log('开始获取钱包余额...')
      const balanceMap = await fetchAllBalances(walletList)
      console.log('Balance map:', balanceMap)

      // 更新余额到状态
      get().updateWalletBalances(balanceMap)

      // 持久化到本地存储
      storage.setItem('WALLET_LIST', JSON.stringify(get().walletList))
      console.log('余额更新完成')
    } catch (error) {
      console.error('获取余额失败:', error)
    }
  },
  updateWalletBalances: (balances) =>
    set((state) => {
      const updated = state.walletList.map((wallet) => ({
        ...wallet,
        accounts: wallet.accounts.map((account) => {
          const balanceData = balances[account.accountId]
          if (!balanceData) return account

          // 合并 balance 字段进每条链
          const updatedAccount = { ...account }
          for (const chain of ['eth', 'bsc', 'btc', 'solana'] as any[]) {
            if (balanceData[chain] && updatedAccount[chain]) {
              updatedAccount[chain] = {
                ...updatedAccount[chain],
                balance: balanceData[chain],
              }
            }
          }
          return updatedAccount
        }),
      }))
      return { walletList: updated }
    }),

  updateWalletName: (walletId: string, accountId: string, name: string) =>
    set((state) => {
      const updated = state.walletList.map((wallet) => {
        if (wallet.walletId === walletId) {
          return {
            ...wallet,
            accounts: wallet.accounts.map((account) => {
              if (account.accountId === accountId) {
                return {
                  ...account,
                  name: name.trim(),
                }
              }
              return account
            }),
          }
        }
        return wallet
      })

      // 如果是当前账户，也更新当前账户信息
      let updatedCurrentAccount = state.currentAccount
      if (state.currentAccount.accountId === accountId) {
        updatedCurrentAccount = {
          ...state.currentAccount,
          name: name.trim(),
        }
      }

      // 持久化到本地存储
      storage.setItem('WALLET_LIST', JSON.stringify(updated))

      return {
        walletList: updated,
        currentAccount: updatedCurrentAccount,
      }
    }),

  setCurrentAccount: (account: Account) =>
    set((state) => {
      // 持久化当前选中的账户ID到本地存储
      storage.setItem('CURRENT_ACCOUNT_ID', account.accountId)
      return {
        currentAccount: account,
      }
    }),
  getTotalBalance: () => {
    const walletList = get().walletList
    let total = 0
    walletList.forEach((wallet) => {
      wallet.accounts.forEach((account) => {
        const chains = ['eth', 'bsc', 'btc', 'solana']
        chains.forEach((chain) => {
          if (account[chain]?.balance) {
            total += parseFloat(account[chain].balance) || 0
          }
        })
      })
    })
    return total
  },
  getCurrentAccountBalance: () => {
    const currentAccount = get().currentAccount
    if (!currentAccount) return 0

    let total = 0
    const chains = ['eth', 'bsc', 'btc', 'solana']
    chains.forEach((chain) => {
      if (currentAccount[chain]?.balance) {
        total += parseFloat(currentAccount[chain].balance) || 0
      }
    })
    return total
  },
}))
