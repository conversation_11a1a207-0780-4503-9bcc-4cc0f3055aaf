import { create } from 'zustand'
import storage from 'app/utils/storage'
import { generateUID } from 'app/utils/bip39'

export interface Transaction {
  id: string
  txHash: string
  fromAddress: string
  toAddress: string
  amount: string
  chain: 'eth' | 'bsc' | 'btc' | 'solana'
  status: 'pending' | 'confirmed' | 'failed'
  type: 'send' | 'receive'
  timestamp: number
  gasUsed?: string
  gasPrice?: string
  blockNumber?: number
  error?: string
  symbol: string // 代币符号，如 ETH, BNB, SOL, BTC
}

interface TransactionStore {
  transactions: Transaction[]
  isLoading: boolean

  // 基础操作
  loadTransactions: () => Promise<void>
  addTransaction: (transaction: Omit<Transaction, 'id' | 'timestamp'>) => void
  updateTransaction: (id: string, updates: Partial<Transaction>) => void
  getTransactionsByAddress: (address: string) => Transaction[]
  getTransactionsByChain: (chain: string) => Transaction[]
  getTransactionsByAddresses: (addresses: string[]) => Transaction[]

  // 清理操作
  clearTransactions: () => void
  removeTransaction: (id: string) => void
}

const STORAGE_KEY = 'TRANSACTION_HISTORY'

export const useTransactionStore = create<TransactionStore>((set, get) => ({
  transactions: [],
  isLoading: false,

  loadTransactions: async () => {
    set({ isLoading: true })
    try {
      const stored = await storage.getItem(STORAGE_KEY)
      if (stored) {
        const transactions = JSON.parse(stored)
        set({ transactions })
      }
    } catch (error) {
      console.error('Failed to load transactions:', error)
    } finally {
      set({ isLoading: false })
    }
  },

  addTransaction: (transactionData) => {
    const transaction: Transaction = {
      ...transactionData,
      id: generateUID(),
      timestamp: Date.now(),
    }

    const transactions = [transaction, ...get().transactions]
    set({ transactions })

    // 持久化存储
    storage.setItem(STORAGE_KEY, JSON.stringify(transactions))
  },

  updateTransaction: (id, updates) => {
    const transactions = get().transactions.map((tx) => (tx.id === id ? { ...tx, ...updates } : tx))
    set({ transactions })

    // 持久化存储
    storage.setItem(STORAGE_KEY, JSON.stringify(transactions))
  },

  getTransactionsByAddress: (address) => {
    return get().transactions.filter(
      (tx) =>
        tx.fromAddress.toLowerCase() === address.toLowerCase() ||
        tx.toAddress.toLowerCase() === address.toLowerCase()
    )
  },

  getTransactionsByChain: (chain) => {
    return get().transactions.filter((tx) => tx.chain === chain)
  },

  getTransactionsByAddresses: (addresses) => {
    const allTransactions: Transaction[] = []
    const seenTransactionIds = new Set<string>()

    addresses.forEach((address) => {
      if (address) {
        const addressTransactions = get().transactions.filter(
          (tx) =>
            tx.fromAddress.toLowerCase() === address.toLowerCase() ||
            tx.toAddress.toLowerCase() === address.toLowerCase()
        )
        addressTransactions.forEach((tx) => {
          // 使用交易ID去重，避免重复添加同一个交易
          if (!seenTransactionIds.has(tx.id)) {
            seenTransactionIds.add(tx.id)
            allTransactions.push(tx)
          }
        })
      }
    })

    // 按时间戳排序，最新的在前面
    return allTransactions.sort((a, b) => b.timestamp - a.timestamp)
  },

  clearTransactions: () => {
    set({ transactions: [] })
    storage.removeItem(STORAGE_KEY)
  },

  removeTransaction: (id) => {
    const transactions = get().transactions.filter((tx) => tx.id !== id)
    set({ transactions })
    storage.setItem(STORAGE_KEY, JSON.stringify(transactions))
  },
}))

// 辅助函数：格式化交易显示
export const formatTransaction = (transaction: Transaction) => {
  const { amount, symbol, type, toAddress, fromAddress } = transaction

  return {
    ...transaction,
    displayAmount: `${type === 'send' ? '-' : '+'}${amount} ${symbol}`,
    displayAddress:
      type === 'send'
        ? `至 ${toAddress.slice(0, 6)}...${toAddress.slice(-4)}`
        : `来自 ${fromAddress.slice(0, 6)}...${fromAddress.slice(-4)}`,
    displayTime: new Date(transaction.timestamp).toLocaleString(),
  }
}

// 辅助函数：获取链的显示名称
export const getChainDisplayName = (chain: string) => {
  const chainNames = {
    eth: 'Ethereum',
    bsc: 'BSC',
    btc: 'Bitcoin',
    solana: 'Solana',
  }
  return chainNames[chain] || chain.toUpperCase()
}

// 辅助函数：获取交易状态的显示文本
export const getStatusDisplayText = (status: string) => {
  const statusTexts = {
    pending: '待确认',
    confirmed: '已确认',
    failed: '失败',
  }
  return statusTexts[status] || status
}

// 辅助函数：获取交易类型的显示文本
export const getTypeDisplayText = (type: string) => {
  const typeTexts = {
    send: '已发送',
    receive: '已接收',
  }
  return typeTexts[type] || type
}
