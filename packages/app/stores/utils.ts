import { ethers } from 'ethers'
import { Connection, PublicKey } from '@solana/web3.js'
import { BSC_RPC, ETH_RPC, SOLANA_RPC } from 'app/utils/constants'
import { networkManager, requestWithRetry } from 'app/utils/networkManager'

// 请求缓存和防抖
const balanceCache = new Map<string, { balance: string; timestamp: number }>()
const CACHE_DURATION = 30000 // 30秒缓存
const requestQueue = new Map<string, Promise<string>>()

// 带超时的请求函数
const fetchWithTimeout = async (url: string, timeout = 5000): Promise<Response> => {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeout)

  try {
    const response = await fetch(url, { signal: controller.signal })
    clearTimeout(timeoutId)
    return response
  } catch (error) {
    clearTimeout(timeoutId)
    throw error
  }
}

// EVM 公链通用 - 添加缓存和错误处理
const getEvmBalance = async (address: string, chain: 'eth' | 'bsc') => {
  const rpc = networkManager.getAvailableRpc(chain)
  const cacheKey = `${chain}-${address}`

  // 检查缓存
  const cached = balanceCache.get(cacheKey)
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.balance
  }

  // 检查是否已有相同请求在进行
  if (requestQueue.has(cacheKey)) {
    return requestQueue.get(cacheKey)!
  }

  const request = requestWithRetry(async () => {
    const provider = new ethers.JsonRpcProvider(rpc, undefined, {
      staticNetwork: true,
      batchMaxCount: 1,
    })

    // 设置超时
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), 5000)
    })

    const balancePromise = provider.getBalance(address)
    const bal = await Promise.race([balancePromise, timeoutPromise])
    const balance = ethers.formatEther(bal)

    // 缓存结果
    balanceCache.set(cacheKey, { balance, timestamp: Date.now() })
    return balance
  }, rpc)
    .catch((error) => {
      console.warn(`Failed to fetch ${chain.toUpperCase()} balance for ${address}:`, error.message)
      return '0'
    })
    .finally(() => {
      requestQueue.delete(cacheKey)
    })

  requestQueue.set(cacheKey, request)
  return request
}

const getSolBalance = async (address: string) => {
  const rpc = networkManager.getAvailableRpc('solana')
  const cacheKey = `solana-${address}`

  // 检查缓存
  const cached = balanceCache.get(cacheKey)
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.balance
  }

  // 检查是否已有相同请求在进行
  if (requestQueue.has(cacheKey)) {
    return requestQueue.get(cacheKey)!
  }

  const request = requestWithRetry(async () => {
    const connection = new Connection(rpc, {
      commitment: 'confirmed',
      confirmTransactionInitialTimeout: 5000,
    })

    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), 5000)
    })

    const balancePromise = connection.getBalance(new PublicKey(address))
    const lamports = await Promise.race([balancePromise, timeoutPromise])
    const balance = (lamports / 1e9).toFixed(6)

    // 缓存结果
    balanceCache.set(cacheKey, { balance, timestamp: Date.now() })
    return balance
  }, rpc)
    .catch((error) => {
      console.warn(`Failed to fetch Solana balance for ${address}:`, error.message)
      return '0'
    })
    .finally(() => {
      requestQueue.delete(cacheKey)
    })

  requestQueue.set(cacheKey, request)
  return request
}

// 可选：BTC 查询（注意你当前 BTC 地址是 EVM 格式）
const getBtcBalance = async (address: string) => {
  const cacheKey = `btc-${address}`

  // 检查缓存
  const cached = balanceCache.get(cacheKey)
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.balance
  }

  // 检查是否已有相同请求在进行
  if (requestQueue.has(cacheKey)) {
    return requestQueue.get(cacheKey)!
  }

  const request = (async () => {
    try {
      const res = await fetchWithTimeout(`https://blockstream.info/api/address/${address}`)
      if (!res.ok) throw new Error(`HTTP ${res.status}`)

      const data = await res.json()
      const btc = (data.chain_stats.funded_txo_sum - data.chain_stats.spent_txo_sum) / 1e8
      const balance = btc.toFixed(8)

      // 缓存结果
      balanceCache.set(cacheKey, { balance, timestamp: Date.now() })
      return balance
    } catch (error) {
      console.warn(`Failed to fetch BTC balance for ${address}:`, error.message)
      return '0'
    } finally {
      requestQueue.delete(cacheKey)
    }
  })()

  requestQueue.set(cacheKey, request)
  return request
}

const getBtcTestnetBalance = async (address: string) => {
  const cacheKey = `btc-testnet-${address}`

  // 检查缓存
  const cached = balanceCache.get(cacheKey)
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.balance
  }

  // 检查是否已有相同请求在进行
  if (requestQueue.has(cacheKey)) {
    return requestQueue.get(cacheKey)!
  }

  const request = (async () => {
    try {
      const res = await fetchWithTimeout(`https://blockstream.info/testnet/api/address/${address}`)
      if (!res.ok) throw new Error(`HTTP ${res.status}`)

      const data = await res.json()
      const btc = (data.chain_stats.funded_txo_sum - data.chain_stats.spent_txo_sum) / 1e8
      const balance = btc.toFixed(8)

      // 缓存结果
      balanceCache.set(cacheKey, { balance, timestamp: Date.now() })
      return balance
    } catch (error) {
      console.warn(`Failed to fetch BTC testnet balance for ${address}:`, error.message)
      return '0'
    } finally {
      requestQueue.delete(cacheKey)
    }
  })()

  requestQueue.set(cacheKey, request)
  return request
}

const getTestnetBalance = async (address: string) => {
  const cacheKey = `btc-mempool-${address}`

  // 检查缓存
  const cached = balanceCache.get(cacheKey)
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.balance
  }

  // 检查是否已有相同请求在进行
  if (requestQueue.has(cacheKey)) {
    return requestQueue.get(cacheKey)!
  }

  const request = (async () => {
    try {
      const res = await fetchWithTimeout(`https://mempool.space/testnet/api/address/${address}`)
      if (!res.ok) throw new Error(`HTTP ${res.status}`)

      const data = await res.json()
      const balance =
        (data.chain_stats.funded_txo_sum -
          data.chain_stats.spent_txo_sum +
          data.mempool_stats.funded_txo_sum -
          data.mempool_stats.spent_txo_sum) /
        1e8

      const balanceStr = balance.toFixed(8)

      // 缓存结果
      balanceCache.set(cacheKey, { balance: balanceStr, timestamp: Date.now() })
      return balanceStr
    } catch (error) {
      console.warn(`Failed to fetch BTC mempool balance for ${address}:`, error.message)
      return '0'
    } finally {
      requestQueue.delete(cacheKey)
    }
  })()

  requestQueue.set(cacheKey, request)
  return request
}

// 核心函数：从 walletList 中查询余额并更新 Zustand 状态
export const fetchAllBalances = async (walletList) => {
  const balanceMap = {}

  if (!walletList || walletList.length === 0) {
    return balanceMap
  }

  console.log('开始获取余额...')

  for (const wallet of walletList) {
    for (const account of wallet.accounts) {
      const accountId = account.accountId

      // 使用 Promise.allSettled 而不是 Promise.all，避免一个失败导致全部失败
      const results = await Promise.allSettled([
        account.eth?.address ? getEvmBalance(account.eth.address, 'eth') : Promise.resolve('0'),
        account.bsc?.address ? getEvmBalance(account.bsc.address, 'bsc') : Promise.resolve('0'),
        account.solana?.address ? getSolBalance(account.solana.address) : Promise.resolve('0'),
        account.btc?.address ? getTestnetBalance(account.btc.address) : Promise.resolve('0'),
      ])

      const [ethResult, bscResult, solResult, btcResult] = results

      balanceMap[accountId] = {
        eth: ethResult.status === 'fulfilled' ? ethResult.value : '0',
        bsc: bscResult.status === 'fulfilled' ? bscResult.value : '0',
        solana: solResult.status === 'fulfilled' ? solResult.value : '0',
        btc: btcResult.status === 'fulfilled' ? btcResult.value : '0',
      }

      // 记录失败的请求
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          const chains = ['ETH', 'BSC', 'Solana', 'BTC']
          console.warn(
            `${chains[index]} balance fetch failed for account ${accountId}:`,
            result.reason
          )
        }
      })
    }
  }

  console.log('余额获取完成:', balanceMap)
  return balanceMap
}

// 清理缓存的工具函数
export const clearBalanceCache = () => {
  balanceCache.clear()
  console.log('余额缓存已清理')
}

// 获取缓存状态的工具函数
export const getCacheStatus = () => {
  return {
    cacheSize: balanceCache.size,
    activeRequests: requestQueue.size,
    cacheEntries: Array.from(balanceCache.entries()).map(([key, value]) => ({
      key,
      age: Date.now() - value.timestamp,
      balance: value.balance,
    })),
  }
}
