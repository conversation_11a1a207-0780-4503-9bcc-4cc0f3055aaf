import { useColorScheme } from 'react-native'
import { useEffect } from 'react'
import {
  CustomToast,
  TamaguiProvider,
  type TamaguiProviderProps,
  ToastProvider,
  config,
  isWeb,
} from '@my/ui'
import { ToastViewport } from './ToastViewport'
import { initializeI18n } from 'app/i18n'
import { startHealthCheck } from 'app/utils/networkManager'

export function Provider({
  children,
  defaultTheme = 'dark',
  ...rest
}: Omit<TamaguiProviderProps, 'config'> & { defaultTheme?: string }) {
  const colorScheme = useColorScheme()
  const theme = defaultTheme || (colorScheme === 'dark' ? 'dark' : 'light')

  // 初始化多语言系统
  useEffect(() => {
    initializeI18n()

    // 暂时注释掉网络健康检查，避免频繁网络请求
    // startHealthCheck(300000)

    // return () => {
    //   // 组件卸载时停止健康检查
    //   import('app/utils/networkManager').then(({ stopHealthCheck }) => {
    //     stopHealthCheck()
    //   })
    // }
  }, [])

  return (
    <TamaguiProvider config={config} defaultTheme={theme} {...rest}>
      <ToastProvider swipeDirection="horizontal" duration={6000} native={isWeb ? [] : ['mobile']}>
        {children}
        <CustomToast />
        <ToastViewport />
      </ToastProvider>
    </TamaguiProvider>
  )
}
