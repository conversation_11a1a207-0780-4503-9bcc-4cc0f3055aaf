import { ethers } from 'ethers'
import {
  Connection,
  PublicKey,
  SystemProgram,
  Transaction,
  sendAndConfirmTransaction,
  Keypair,
} from '@solana/web3.js'
import { networkManager } from 'app/utils/networkManager'

export interface TransactionParams {
  fromAddress: string
  toAddress: string
  amount: string
  privateKey: string
  chain: 'eth' | 'bsc' | 'btc' | 'solana'
  gasPrice?: string
  gasLimit?: string
}

export interface TransactionResult {
  success: boolean
  txHash?: string
  error?: string
  gasUsed?: string
  blockNumber?: number
}

// 地址验证
const validateAddress = (address: string, chain: 'eth' | 'bsc' | 'btc' | 'solana'): boolean => {
  try {
    switch (chain) {
      case 'eth':
      case 'bsc':
        return ethers.isAddress(address)
      case 'solana':
        new PublicKey(address)
        return true
      case 'btc':
        // 简单的BTC地址验证
        return (
          /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(address) ||
          /^bc1[a-z0-9]{39,59}$/.test(address) ||
          /^[2mn][a-km-zA-HJ-NP-Z1-9]{33,34}$/.test(address)
        )
      default:
        return false
    }
  } catch {
    return false
  }
}

// 金额验证
const validateAmount = (amount: string): boolean => {
  try {
    const num = parseFloat(amount)
    return num > 0 && !isNaN(num) && isFinite(num)
  } catch {
    return false
  }
}

// EVM链交易发送 (ETH, BSC)
export const sendEvmTransaction = async (params: TransactionParams): Promise<TransactionResult> => {
  try {
    const { fromAddress, toAddress, amount, privateKey, chain, gasPrice, gasLimit } = params

    // 选择RPC - 确保是EVM链
    if (chain !== 'eth' && chain !== 'bsc') {
      return {
        success: false,
        error: `EVM交易不支持链类型: ${chain}`,
      }
    }

    const rpc = networkManager.getAvailableRpc(chain)
    const provider = new ethers.JsonRpcProvider(rpc, undefined, {
      staticNetwork: true,
      batchMaxCount: 1,
    })

    // 设置超时
    provider._getConnection().timeout = 10000 // 10秒超时

    // 验证私钥格式
    if (!privateKey.startsWith('0x') || privateKey.length !== 66) {
      return {
        success: false,
        error: '无效的私钥格式',
      }
    }

    // 创建钱包
    const wallet = new ethers.Wallet(privateKey, provider)

    // 验证from地址是否匹配
    if (wallet.address.toLowerCase() !== fromAddress.toLowerCase()) {
      return {
        success: false,
        error: '私钥与发送地址不匹配',
      }
    }

    // 检查余额
    const balance = await provider.getBalance(fromAddress)
    const amountWei = ethers.parseEther(amount)

    // 获取gas费用信息
    let feeData
    try {
      feeData = await provider.getFeeData()
    } catch (error) {
      console.warn('Failed to get fee data, using fallback:', error)
      feeData = {
        gasPrice: ethers.parseUnits('20', 'gwei'),
        maxFeePerGas: null,
        maxPriorityFeePerGas: null,
      }
    }

    const estimatedGasLimit = gasLimit ? BigInt(gasLimit) : 21000n
    const estimatedGasPrice = gasPrice
      ? ethers.parseUnits(gasPrice, 'gwei')
      : feeData.gasPrice || ethers.parseUnits('20', 'gwei')

    const estimatedGasCost = estimatedGasLimit * estimatedGasPrice
    const totalCost = amountWei + estimatedGasCost

    if (balance < totalCost) {
      const balanceEth = ethers.formatEther(balance)
      const totalCostEth = ethers.formatEther(totalCost)
      return {
        success: false,
        error: `余额不足: 当前余额 ${balanceEth} ${chain.toUpperCase()}, 需要 ${totalCostEth} ${chain.toUpperCase()}`,
      }
    }

    // 构建交易
    const transaction = {
      to: toAddress,
      value: amountWei,
      gasLimit: estimatedGasLimit,
      gasPrice: estimatedGasPrice,
    }

    // 发送交易
    console.log(`Sending ${chain.toUpperCase()} transaction:`, {
      from: fromAddress,
      to: toAddress,
      amount: `${amount} ${chain.toUpperCase()}`,
      gasLimit: estimatedGasLimit.toString(),
      gasPrice: ethers.formatUnits(estimatedGasPrice, 'gwei') + ' gwei',
    })

    const tx = await wallet.sendTransaction(transaction)
    console.log(`Transaction sent: ${tx.hash}`)

    // 等待确认 (最多等待30秒)
    const receipt = await tx.wait(1, 30000)

    if (!receipt) {
      return {
        success: false,
        error: '交易确认超时，请稍后查看交易状态',
      }
    }

    console.log(`Transaction confirmed: ${tx.hash}`, receipt)

    return {
      success: true,
      txHash: tx.hash,
      gasUsed: receipt.gasUsed?.toString(),
      blockNumber: receipt.blockNumber,
    }
  } catch (error: any) {
    console.error('EVM transaction error:', error)

    let errorMessage = '交易失败'

    // 解析特定错误
    if (error.message.includes('insufficient funds')) {
      errorMessage = '余额不足'
    } else if (error.message.includes('gas required exceeds allowance')) {
      errorMessage = 'Gas费用不足'
    } else if (error.message.includes('nonce too low')) {
      errorMessage = '交易序号错误，请稍后重试'
    } else if (error.message.includes('replacement transaction underpriced')) {
      errorMessage = 'Gas价格过低，请提高Gas价格'
    } else if (error.message.includes('timeout')) {
      errorMessage = '网络超时，请检查网络连接'
    } else if (error.message) {
      errorMessage = error.message
    }

    // 网络错误时标记节点失败
    if (error.message.includes('timeout') || error.message.includes('network')) {
      try {
        if (params.chain === 'eth' || params.chain === 'bsc') {
          const rpc = networkManager.getAvailableRpc(params.chain)
          networkManager.markNodeFailed(rpc)
        }
      } catch (e) {
        console.warn('Failed to mark node as failed:', e)
      }
    }

    return {
      success: false,
      error: errorMessage,
    }
  }
}

// Solana交易发送
export const sendSolanaTransaction = async (
  params: TransactionParams
): Promise<TransactionResult> => {
  try {
    const { fromAddress, toAddress, amount, privateKey } = params

    const rpc = networkManager.getAvailableRpc('solana')
    const connection = new Connection(rpc)

    // 从私钥创建Keypair
    const fromKeypair = Keypair.fromSecretKey(
      new Uint8Array(Buffer.from(privateKey.replace('0x', ''), 'hex'))
    )

    // 检查余额
    const balance = await connection.getBalance(new PublicKey(fromAddress))
    const amountLamports = parseFloat(amount) * 1e9 // SOL to lamports

    if (balance < amountLamports) {
      return {
        success: false,
        error: '余额不足',
      }
    }

    // 创建转账交易
    const transaction = new Transaction().add(
      SystemProgram.transfer({
        fromPubkey: new PublicKey(fromAddress),
        toPubkey: new PublicKey(toAddress),
        lamports: amountLamports,
      })
    )

    // 发送交易
    const signature = await sendAndConfirmTransaction(connection, transaction, [fromKeypair])

    return {
      success: true,
      txHash: signature,
    }
  } catch (error: any) {
    console.error('Solana transaction error:', error)
    return {
      success: false,
      error: error.message || '交易失败',
    }
  }
}

// BTC交易发送 (简化版本，实际需要更复杂的UTXO处理)
export const sendBtcTransaction = async (params: TransactionParams): Promise<TransactionResult> => {
  try {
    // 这里是简化的BTC交易逻辑
    // 实际实现需要处理UTXO、计算手续费等
    console.log('BTC transaction not fully implemented yet')

    return {
      success: false,
      error: 'BTC交易功能暂未完全实现',
    }
  } catch (error: any) {
    console.error('BTC transaction error:', error)
    return {
      success: false,
      error: error.message || '交易失败',
    }
  }
}

// 统一的交易发送接口
export const sendTransaction = async (params: TransactionParams): Promise<TransactionResult> => {
  // 基础验证
  if (!params.fromAddress || !params.toAddress || !params.amount || !params.privateKey) {
    return {
      success: false,
      error: '参数不完整：缺少必要的交易信息',
    }
  }

  // 地址格式验证
  if (!validateAddress(params.toAddress, params.chain)) {
    return {
      success: false,
      error: `无效的${params.chain.toUpperCase()}地址格式`,
    }
  }

  // 金额验证
  if (!validateAmount(params.amount)) {
    return {
      success: false,
      error: '无效的金额：金额必须大于0',
    }
  }

  // 防止自己给自己转账
  if (params.fromAddress.toLowerCase() === params.toAddress.toLowerCase()) {
    return {
      success: false,
      error: '不能给自己转账',
    }
  }

  try {
    switch (params.chain) {
      case 'eth':
      case 'bsc':
        return await sendEvmTransaction(params)
      case 'solana':
        return await sendSolanaTransaction(params)
      case 'btc':
        return await sendBtcTransaction(params)
      default:
        return {
          success: false,
          error: `不支持的链类型: ${params.chain}`,
        }
    }
  } catch (error: any) {
    console.error('Transaction failed:', error)
    return {
      success: false,
      error: error.message || '交易发送失败',
    }
  }
}

// 估算交易费用
export const estimateTransactionFee = async (
  params: Omit<TransactionParams, 'privateKey'>
): Promise<{
  estimatedFee: string
  gasPrice?: string
  gasLimit?: string
}> => {
  try {
    const { chain, fromAddress, toAddress, amount } = params

    if (chain === 'eth' || chain === 'bsc') {
      const rpc = networkManager.getAvailableRpc(chain)
      const provider = new ethers.JsonRpcProvider(rpc)

      const feeData = await provider.getFeeData()
      const gasLimit = 21000n
      const gasPrice = feeData.gasPrice || ethers.parseUnits('20', 'gwei')

      const estimatedFee = ethers.formatEther(gasPrice * gasLimit)

      return {
        estimatedFee,
        gasPrice: ethers.formatUnits(gasPrice, 'gwei'),
        gasLimit: gasLimit.toString(),
      }
    } else if (chain === 'solana') {
      // Solana交易费用通常是固定的
      return {
        estimatedFee: '0.000005', // 5000 lamports
      }
    } else {
      return {
        estimatedFee: '0',
      }
    }
  } catch (error) {
    console.error('Fee estimation error:', error)
    return {
      estimatedFee: '0',
    }
  }
}
