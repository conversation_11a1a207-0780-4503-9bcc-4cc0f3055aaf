{"$schema": "https://biomejs.dev/schemas/1.9.3/schema.json", "organizeImports": {"enabled": false}, "linter": {"enabled": true, "ignore": [".tamagui", "*/ios/**", ".vscode", "**/tamagui.css", "**/*.d.ts", "packages/vendor/**", "packages/**/vendor/**", "**/tsconfig.json", "**/dist/**", "**/package.json", ".codesandbox", "tsconfig.base.json"], "rules": {"correctness": {"useExhaustiveDependencies": "off", "noInnerDeclarations": "off", "noUnnecessaryContinue": "off", "noConstructorReturn": "off"}, "suspicious": {"noImplicitAnyLet": "off", "noConfusingVoidType": "off", "noEmptyInterface": "off", "noExplicitAny": "off", "noArrayIndexKey": "off", "noDoubleEquals": "off", "noConsoleLog": "error", "noAssignInExpressions": "off", "noRedeclare": "off"}, "style": {"noParameterAssign": "off", "noNonNullAssertion": "off", "noArguments": "off", "noUnusedTemplateLiteral": "off", "useDefaultParameterLast": "off", "useConst": "off", "useEnumInitializers": "off", "useTemplate": "off", "useSelfClosingElements": "off", "useImportType": "error"}, "security": {"noDangerouslySetInnerHtml": "off", "noDangerouslySetInnerHtmlWithChildren": "off"}, "performance": {"noDelete": "off", "noAccumulatingSpread": "off"}, "complexity": {"noForEach": "off", "noBannedTypes": "off", "noUselessFragments": "off", "useLiteralKeys": "off", "useSimplifiedLogicExpression": "off", "useOptionalChain": "off"}, "a11y": {"noSvgWithoutTitle": "off", "useMediaCaption": "off", "noHeaderScope": "off", "useAltText": "off", "useButtonType": "off"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "attributePosition": "multiline", "ignore": [".tamagui", "**/ios/**", ".vscode", "**/tamagui.css", "**/*.d.ts", "packages/vendor/**", "packages/**/vendor/**", "**/tsconfig.json", "**/dist/**", "**/package.json", ".codesandbox", "tsconfig.base.json"]}, "javascript": {"formatter": {"trailingCommas": "es5", "jsxQuoteStyle": "double", "semicolons": "asNeeded", "quoteStyle": "single"}}, "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}}