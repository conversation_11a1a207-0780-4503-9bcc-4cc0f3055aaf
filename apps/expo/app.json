{"expo": {"name": "yourprojectsname", "slug": "yourprojectsname", "scheme": "yourprojectsname", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.yourprojectsname.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.yourprojectsname.app"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router", "expo-font"], "experiments": {"typedRoutes": true}}}