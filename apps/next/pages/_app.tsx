import '@tamagui/core/reset.css'
import '@tamagui/font-inter/css/400.css'
import '@tamagui/font-inter/css/700.css'
import 'raf/polyfill'

import type React from 'react'
import Head from 'next/head'
import type { SolitoAppProps } from 'solito'
import { NextTamaguiProvider } from 'app/provider/NextTamaguiProvider'
import { config } from '@my/ui'
import Script from 'next/script'
import { useWalletStore } from 'app/stores/walletStore'
import { useEffect } from 'react'
import { useRouter } from 'solito/navigation'

if (process.env.NODE_ENV === 'production') {
  require('../public/tamagui.css')
}

function MyApp({ Component, pageProps }: SolitoAppProps) {
  const router = useRouter()
  const walletStore = useWalletStore()
  useEffect(() => {
    walletStore.getVault()
      .then(_vault => {
        // 如果没有密码
        if (!_vault) {
          router.push('/wallet/password')
        }
      })
  }, [])

  return (
    <>
      <Head>
        <title>Coinbase v2</title>
        <meta name="description" content="Coinbase v2" />
        <link rel="icon" href="/favicon.ico" />
        <style
          key="tamagui-new-css"
          dangerouslySetInnerHTML={{
            // the first time this runs you'll get the full CSS including all themes
            // after that, it will only return CSS generated since the last call
            __html: config.getNewCSS(),
          }}
        />

        <style
          key="tamagui-css"
          dangerouslySetInnerHTML={{
            __html: config.getCSS({
              // if you are using "outputCSS" option, you should use this "exclude"
              // if not, then you can leave the option out
              exclude: process.env.NODE_ENV === 'production' ? 'design-system' : null,
            }),
          }}
        />
        <Script id="t_unmounted" strategy="beforeInteractive">
          {`document.documentElement.classList.add('t_unmounted')`}
        </Script>
        {/* <script
          key="t_unmounted"
          dangerouslySetInnerHTML={{
            __html: `document.documentElement.classList.add('t_unmounted')`,
          }}
        /> */}
      </Head>
      <NextTamaguiProvider>
        <Component {...pageProps} />
      </NextTamaguiProvider>
    </>
  )
}

export default MyApp
