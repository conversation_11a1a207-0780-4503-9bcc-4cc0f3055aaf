{"name": "next-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "cd ../.. && yarn build && cd apps/next && next build", "start": "next start", "serve": "NODE_ENV=production next start --port 8151", "lint": "next lint", "test": "vitest"}, "dependencies": {"@tamagui/config": "^1.129.0", "@tamagui/next-theme": "^1.129.0", "app": "0.0.0", "next": "14.2.14", "raf": "^3.4.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-web": "^0.20.0", "tamagui": "^1.129.0", "vercel": "latest"}, "devDependencies": {"@tamagui/next-plugin": "^1.129.0", "@types/node": "^20.14.1", "eslint-config-next": "^14.2.3", "next-compose-plugins": "^2.2.1", "next-transpile-modules": "^10.0.1", "tree-kill": "^1.2.2", "vitest": "^2.1.1"}}