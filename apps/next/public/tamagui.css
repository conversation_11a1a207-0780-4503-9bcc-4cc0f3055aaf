._ovs-contain {overscroll-behavior:contain;}
  .is_Text .is_Text {display:inline-flex;}
  ._dsp_contents {display:contents;}
  :root {--c-radius-0:0px;--c-radius-1:3px;--c-radius-2:5px;--c-radius-3:7px;--c-radius-4:9px;--c-radius-5:10px;--c-radius-6:16px;--c-radius-7:19px;--c-radius-8:22px;--c-radius-9:26px;--c-radius-10:34px;--c-radius-11:42px;--c-radius-12:50px;--c-radius-true:9px;--c-zIndex-0:0;--c-zIndex-1:100;--c-zIndex-2:200;--c-zIndex-3:300;--c-zIndex-4:400;--c-zIndex-5:500;--c-space-0:0px;--c-space-1:2px;--c-space-2:7px;--c-space-3:13px;--c-space-4:18px;--c-space-5:24px;--c-space-6:32px;--c-space-7:39px;--c-space-8:46px;--c-space-9:53px;--c-space-10:60px;--c-space-11:74px;--c-space-12:88px;--c-space-13:102px;--c-space-14:116px;--c-space-15:130px;--c-space-16:144px;--c-space-17:144px;--c-space-18:158px;--c-space-19:172px;--c-space-20:186px;--c-space-0--25:0.5px;--c-space-0--5:1px;--c-space-0--75:1.5px;--c-space-1--5:4px;--c-space-2--5:10px;--c-space-3--5:16px;--c-space-true:18px;--c-space-4--5:21px;--c-space--0--25:-0.5px;--c-space--0--5:-1px;--c-space--0--75:-1.5px;--c-space--1:-2px;--c-space--1--5:-4px;--c-space--2:-7px;--c-space--2--5:-10px;--c-space--3:-13px;--c-space--3--5:-16px;--c-space--4:-18px;--c-space--true:-18px;--c-space--4--5:-21px;--c-space--5:-24px;--c-space--6:-32px;--c-space--7:-39px;--c-space--8:-46px;--c-space--9:-53px;--c-space--10:-60px;--c-space--11:-74px;--c-space--12:-88px;--c-space--13:-102px;--c-space--14:-116px;--c-space--15:-130px;--c-space--16:-144px;--c-space--17:-144px;--c-space--18:-158px;--c-space--19:-172px;--c-space--20:-186px;--c-size-0:0px;--c-size-1:20px;--c-size-2:28px;--c-size-3:36px;--c-size-4:44px;--c-size-5:52px;--c-size-6:64px;--c-size-7:74px;--c-size-8:84px;--c-size-9:94px;--c-size-10:104px;--c-size-11:124px;--c-size-12:144px;--c-size-13:164px;--c-size-14:184px;--c-size-15:204px;--c-size-16:224px;--c-size-17:224px;--c-size-18:244px;--c-size-19:264px;--c-size-20:284px;--c-size-0--25:2px;--c-size-0--5:4px;--c-size-0--75:8px;--c-size-1--5:24px;--c-size-2--5:32px;--c-size-3--5:40px;--c-size-true:44px;--c-size-4--5:48px}
:root .font_body, :root .t_lang-body-default .font_body {--f-family:Inter, -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;--f-lineHeight-1:23px;--f-lineHeight-2:24px;--f-lineHeight-3:25px;--f-lineHeight-4:27px;--f-lineHeight-5:30px;--f-lineHeight-6:32px;--f-lineHeight-7:34px;--f-lineHeight-8:38px;--f-lineHeight-9:46px;--f-lineHeight-10:66px;--f-lineHeight-11:77px;--f-lineHeight-12:85px;--f-lineHeight-13:97px;--f-lineHeight-14:121px;--f-lineHeight-15:148px;--f-lineHeight-16:172px;--f-lineHeight-true:27px;--f-weight-1:300;--f-weight-2:300;--f-weight-3:300;--f-weight-4:300;--f-weight-5:300;--f-weight-6:300;--f-weight-7:300;--f-weight-8:300;--f-weight-9:300;--f-weight-10:300;--f-weight-11:300;--f-weight-12:300;--f-weight-13:300;--f-weight-14:300;--f-weight-15:300;--f-weight-16:300;--f-weight-true:300;--f-letterSpacing-1:0px;--f-letterSpacing-2:0px;--f-letterSpacing-3:0px;--f-letterSpacing-4:0px;--f-letterSpacing-5:0px;--f-letterSpacing-6:0px;--f-letterSpacing-7:0px;--f-letterSpacing-8:0px;--f-letterSpacing-9:0px;--f-letterSpacing-10:0px;--f-letterSpacing-11:0px;--f-letterSpacing-12:0px;--f-letterSpacing-13:0px;--f-letterSpacing-14:0px;--f-letterSpacing-15:0px;--f-letterSpacing-16:0px;--f-letterSpacing-true:0px;--f-size-1:12px;--f-size-2:13px;--f-size-3:14px;--f-size-4:15px;--f-size-5:18px;--f-size-6:20px;--f-size-7:22px;--f-size-8:25px;--f-size-9:33px;--f-size-10:51px;--f-size-11:61px;--f-size-12:68px;--f-size-13:79px;--f-size-14:101px;--f-size-15:125px;--f-size-16:147px;--f-size-true:15px}
:root .font_heading, :root .t_lang-heading-default .font_heading {--f-family:Inter, -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;--f-lineHeight-1:21px;--f-lineHeight-2:22px;--f-lineHeight-3:23px;--f-lineHeight-4:24px;--f-lineHeight-5:26px;--f-lineHeight-6:25px;--f-lineHeight-7:30px;--f-lineHeight-8:33px;--f-lineHeight-9:40px;--f-lineHeight-10:56px;--f-lineHeight-11:65px;--f-lineHeight-12:72px;--f-lineHeight-13:82px;--f-lineHeight-14:102px;--f-lineHeight-15:124px;--f-lineHeight-16:144px;--f-lineHeight-true:24px;--f-weight-1:400;--f-weight-2:400;--f-weight-3:400;--f-weight-4:400;--f-weight-5:400;--f-weight-6:400;--f-weight-7:700;--f-weight-8:700;--f-weight-9:700;--f-weight-10:700;--f-weight-11:700;--f-weight-12:700;--f-weight-13:700;--f-weight-14:700;--f-weight-15:700;--f-weight-16:700;--f-weight-true:700;--f-letterSpacing-1:2px;--f-letterSpacing-2:2px;--f-letterSpacing-3:2px;--f-letterSpacing-4:2px;--f-letterSpacing-5:2px;--f-letterSpacing-6:1px;--f-letterSpacing-7:0px;--f-letterSpacing-8:-1px;--f-letterSpacing-9:-2px;--f-letterSpacing-10:-3px;--f-letterSpacing-11:-3px;--f-letterSpacing-12:-4px;--f-letterSpacing-13:-4px;--f-letterSpacing-14:-5px;--f-letterSpacing-15:-6px;--f-letterSpacing-16:-6px;--f-letterSpacing-true:-6px;--f-size-1:11px;--f-size-2:12px;--f-size-3:13px;--f-size-4:14px;--f-size-5:16px;--f-size-6:15px;--f-size-7:20px;--f-size-8:23px;--f-size-9:30px;--f-size-10:46px;--f-size-11:55px;--f-size-12:62px;--f-size-13:72px;--f-size-14:92px;--f-size-15:114px;--f-size-16:134px;--f-size-true:14px;--f-transform-1:uppercase;--f-transform-2:uppercase;--f-transform-3:uppercase;--f-transform-4:uppercase;--f-transform-5:uppercase;--f-transform-6:uppercase;--f-transform-7:none;--f-transform-8:none;--f-transform-9:none;--f-transform-10:none;--f-transform-11:none;--f-transform-12:none;--f-transform-13:none;--f-transform-14:none;--f-transform-15:none;--f-transform-16:none;--f-transform-true:none}
  :root.t_dark .t_light , :root.t_light, :root.t_light , .tm_xxt {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(0, 0%, 100%, 0);--background02:hsla(0, 0%, 100%, 0.2);--background04:hsla(0, 0%, 100%, 0.4);--background06:hsla(0, 0%, 100%, 0.6);--background08:hsla(0, 0%, 100%, 0.8);--color1:hsla(0, 0%, 100%, 1);--color2:hsla(0, 0%, 95%, 1);--color3:hsla(0, 0%, 93%, 1);--color4:hsla(0, 0%, 91%, 1);--color5:hsla(0, 0%, 88%, 1);--color6:hsla(0, 0%, 85%, 1);--color7:hsla(0, 0%, 82%, 1);--color8:hsla(0, 0%, 76%, 1);--color9:hsla(0, 0%, 56%, 1);--color10:hsla(0, 0%, 50%, 1);--color11:hsla(0, 0%, 42%, 1);--color12:hsla(0, 0%, 9%, 1);--color0:hsla(0, 0%, 9%, 0);--color02:hsla(0, 0%, 9%, 0.2);--color04:hsla(0, 0%, 9%, 0.4);--color06:hsla(0, 0%, 9%, 0.6);--color08:hsla(0, 0%, 9%, 0.8);--background:hsla(0, 0%, 100%, 1);--backgroundHover:hsla(0, 0%, 100%, 0.8);--backgroundPress:hsla(0, 0%, 95%, 1);--backgroundFocus:hsla(0, 0%, 95%, 1);--borderColor:hsla(0, 0%, 91%, 1);--borderColorHover:hsla(0, 0%, 93%, 1);--borderColorPress:hsla(0, 0%, 88%, 1);--borderColorFocus:hsla(0, 0%, 91%, 1);--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--colorTransparent:hsla(0, 0%, 9%, 0);--blue1:hsl(206, 100%, 99.2%);--blue2:hsl(210, 100%, 98.0%);--blue3:hsl(209, 100%, 96.5%);--blue4:hsl(210, 98.8%, 94.0%);--blue5:hsl(209, 95.0%, 90.1%);--blue6:hsl(209, 81.2%, 84.5%);--blue7:hsl(208, 77.5%, 76.9%);--blue8:hsl(206, 81.9%, 65.3%);--blue9:hsl(206, 100%, 50.0%);--blue10:hsl(208, 100%, 47.3%);--blue11:hsl(211, 100%, 43.2%);--blue12:hsl(211, 100%, 15.0%);--green1:hsl(136, 50.0%, 98.9%);--green2:hsl(138, 62.5%, 96.9%);--green3:hsl(139, 55.2%, 94.5%);--green4:hsl(140, 48.7%, 91.0%);--green5:hsl(141, 43.7%, 86.0%);--green6:hsl(143, 40.3%, 79.0%);--green7:hsl(146, 38.5%, 69.0%);--green8:hsl(151, 40.2%, 54.1%);--green9:hsl(151, 55.0%, 41.5%);--green10:hsl(152, 57.5%, 37.6%);--green11:hsl(153, 67.0%, 28.5%);--green12:hsl(155, 40.0%, 14.0%);--red1:hsl(359, 100%, 99.4%);--red2:hsl(359, 100%, 98.6%);--red3:hsl(360, 100%, 96.8%);--red4:hsl(360, 97.9%, 94.8%);--red5:hsl(360, 90.2%, 91.9%);--red6:hsl(360, 81.7%, 87.8%);--red7:hsl(359, 74.2%, 81.7%);--red8:hsl(359, 69.5%, 74.3%);--red9:hsl(358, 75.0%, 59.0%);--red10:hsl(358, 69.4%, 55.2%);--red11:hsl(358, 65.0%, 48.7%);--red12:hsl(354, 50.0%, 14.6%);--yellow1:hsl(60, 54.0%, 98.5%);--yellow2:hsl(52, 100%, 95.5%);--yellow3:hsl(55, 100%, 90.9%);--yellow4:hsl(54, 100%, 86.6%);--yellow5:hsl(52, 97.9%, 82.0%);--yellow6:hsl(50, 89.4%, 76.1%);--yellow7:hsl(47, 80.4%, 68.0%);--yellow8:hsl(48, 100%, 46.1%);--yellow9:hsl(53, 92.0%, 50.0%);--yellow10:hsl(50, 100%, 48.5%);--yellow11:hsl(42, 100%, 29.0%);--yellow12:hsl(40, 55.0%, 13.5%);--shadow1:rgba(0,0,0,0.04);--shadow2:rgba(0,0,0,0.08);--shadow3:rgba(0,0,0,0.16);--shadow4:rgba(0,0,0,0.24);--shadow5:rgba(0,0,0,0.32);--shadow6:rgba(0,0,0,0.4);--black1:#050505;--black2:#151515;--black3:#191919;--black4:#232323;--black5:#282828;--black6:#323232;--black7:#424242;--black8:#494949;--black9:#545454;--black10:#626262;--black11:#a5a5a5;--black12:#fff;--white1:#fff;--white2:#f2f2f2;--white3:hsl(0, 0%, 93%);--white4:hsl(0, 0%, 91%);--white5:hsl(0, 0%, 88%);--white6:hsl(0, 0%, 85%);--white7:hsl(0, 0%, 82%);--white8:hsl(0, 0%, 76%);--white9:hsl(0, 0%, 56%);--white10:hsl(0, 0%, 50%);--white11:hsl(0, 0%, 42%);--white12:hsl(0, 0%, 9%);--shadowColor:rgba(0,0,0,0.04);--accent1:hsla(0, 0%, 2%, 1);--accent2:hsla(0, 0%, 8%, 1);--accent3:hsla(0, 0%, 10%, 1);--accent4:hsla(0, 0%, 14%, 1);--accent5:hsla(0, 0%, 16%, 1);--accent6:hsla(0, 0%, 20%, 1);--accent7:hsla(0, 0%, 26%, 1);--accent8:hsla(0, 0%, 29%, 1);--accent9:hsla(0, 0%, 33%, 1);--accent10:hsla(0, 0%, 38%, 1);--accent11:hsla(0, 0%, 65%, 1);--accent12:hsla(0, 0%, 100%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    :root {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(0, 0%, 100%, 0);--background02:hsla(0, 0%, 100%, 0.2);--background04:hsla(0, 0%, 100%, 0.4);--background06:hsla(0, 0%, 100%, 0.6);--background08:hsla(0, 0%, 100%, 0.8);--color1:hsla(0, 0%, 100%, 1);--color2:hsla(0, 0%, 95%, 1);--color3:hsla(0, 0%, 93%, 1);--color4:hsla(0, 0%, 91%, 1);--color5:hsla(0, 0%, 88%, 1);--color6:hsla(0, 0%, 85%, 1);--color7:hsla(0, 0%, 82%, 1);--color8:hsla(0, 0%, 76%, 1);--color9:hsla(0, 0%, 56%, 1);--color10:hsla(0, 0%, 50%, 1);--color11:hsla(0, 0%, 42%, 1);--color12:hsla(0, 0%, 9%, 1);--color0:hsla(0, 0%, 9%, 0);--color02:hsla(0, 0%, 9%, 0.2);--color04:hsla(0, 0%, 9%, 0.4);--color06:hsla(0, 0%, 9%, 0.6);--color08:hsla(0, 0%, 9%, 0.8);--background:hsla(0, 0%, 100%, 1);--backgroundHover:hsla(0, 0%, 100%, 0.8);--backgroundPress:hsla(0, 0%, 95%, 1);--backgroundFocus:hsla(0, 0%, 95%, 1);--borderColor:hsla(0, 0%, 91%, 1);--borderColorHover:hsla(0, 0%, 93%, 1);--borderColorPress:hsla(0, 0%, 88%, 1);--borderColorFocus:hsla(0, 0%, 91%, 1);--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--colorTransparent:hsla(0, 0%, 9%, 0);--blue1:hsl(206, 100%, 99.2%);--blue2:hsl(210, 100%, 98.0%);--blue3:hsl(209, 100%, 96.5%);--blue4:hsl(210, 98.8%, 94.0%);--blue5:hsl(209, 95.0%, 90.1%);--blue6:hsl(209, 81.2%, 84.5%);--blue7:hsl(208, 77.5%, 76.9%);--blue8:hsl(206, 81.9%, 65.3%);--blue9:hsl(206, 100%, 50.0%);--blue10:hsl(208, 100%, 47.3%);--blue11:hsl(211, 100%, 43.2%);--blue12:hsl(211, 100%, 15.0%);--green1:hsl(136, 50.0%, 98.9%);--green2:hsl(138, 62.5%, 96.9%);--green3:hsl(139, 55.2%, 94.5%);--green4:hsl(140, 48.7%, 91.0%);--green5:hsl(141, 43.7%, 86.0%);--green6:hsl(143, 40.3%, 79.0%);--green7:hsl(146, 38.5%, 69.0%);--green8:hsl(151, 40.2%, 54.1%);--green9:hsl(151, 55.0%, 41.5%);--green10:hsl(152, 57.5%, 37.6%);--green11:hsl(153, 67.0%, 28.5%);--green12:hsl(155, 40.0%, 14.0%);--red1:hsl(359, 100%, 99.4%);--red2:hsl(359, 100%, 98.6%);--red3:hsl(360, 100%, 96.8%);--red4:hsl(360, 97.9%, 94.8%);--red5:hsl(360, 90.2%, 91.9%);--red6:hsl(360, 81.7%, 87.8%);--red7:hsl(359, 74.2%, 81.7%);--red8:hsl(359, 69.5%, 74.3%);--red9:hsl(358, 75.0%, 59.0%);--red10:hsl(358, 69.4%, 55.2%);--red11:hsl(358, 65.0%, 48.7%);--red12:hsl(354, 50.0%, 14.6%);--yellow1:hsl(60, 54.0%, 98.5%);--yellow2:hsl(52, 100%, 95.5%);--yellow3:hsl(55, 100%, 90.9%);--yellow4:hsl(54, 100%, 86.6%);--yellow5:hsl(52, 97.9%, 82.0%);--yellow6:hsl(50, 89.4%, 76.1%);--yellow7:hsl(47, 80.4%, 68.0%);--yellow8:hsl(48, 100%, 46.1%);--yellow9:hsl(53, 92.0%, 50.0%);--yellow10:hsl(50, 100%, 48.5%);--yellow11:hsl(42, 100%, 29.0%);--yellow12:hsl(40, 55.0%, 13.5%);--shadow1:rgba(0,0,0,0.04);--shadow2:rgba(0,0,0,0.08);--shadow3:rgba(0,0,0,0.16);--shadow4:rgba(0,0,0,0.24);--shadow5:rgba(0,0,0,0.32);--shadow6:rgba(0,0,0,0.4);--black1:#050505;--black2:#151515;--black3:#191919;--black4:#232323;--black5:#282828;--black6:#323232;--black7:#424242;--black8:#494949;--black9:#545454;--black10:#626262;--black11:#a5a5a5;--black12:#fff;--white1:#fff;--white2:#f2f2f2;--white3:hsl(0, 0%, 93%);--white4:hsl(0, 0%, 91%);--white5:hsl(0, 0%, 88%);--white6:hsl(0, 0%, 85%);--white7:hsl(0, 0%, 82%);--white8:hsl(0, 0%, 76%);--white9:hsl(0, 0%, 56%);--white10:hsl(0, 0%, 50%);--white11:hsl(0, 0%, 42%);--white12:hsl(0, 0%, 9%);--shadowColor:rgba(0,0,0,0.04);--accent1:hsla(0, 0%, 2%, 1);--accent2:hsla(0, 0%, 8%, 1);--accent3:hsla(0, 0%, 10%, 1);--accent4:hsla(0, 0%, 14%, 1);--accent5:hsla(0, 0%, 16%, 1);--accent6:hsla(0, 0%, 20%, 1);--accent7:hsla(0, 0%, 26%, 1);--accent8:hsla(0, 0%, 29%, 1);--accent9:hsla(0, 0%, 33%, 1);--accent10:hsla(0, 0%, 38%, 1);--accent11:hsla(0, 0%, 65%, 1);--accent12:hsla(0, 0%, 100%, 1);}
  }
.t_light ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark, :root.t_dark , :root.t_light .t_dark , .tm_xxt {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(0, 0%, 2%, 0);--background02:hsla(0, 0%, 2%, 0.2);--background04:hsla(0, 0%, 2%, 0.4);--background06:hsla(0, 0%, 2%, 0.6);--background08:hsla(0, 0%, 2%, 0.8);--color1:hsla(0, 0%, 2%, 1);--color2:hsla(0, 0%, 8%, 1);--color3:hsla(0, 0%, 10%, 1);--color4:hsla(0, 0%, 14%, 1);--color5:hsla(0, 0%, 16%, 1);--color6:hsla(0, 0%, 20%, 1);--color7:hsla(0, 0%, 26%, 1);--color8:hsla(0, 0%, 29%, 1);--color9:hsla(0, 0%, 33%, 1);--color10:hsla(0, 0%, 38%, 1);--color11:hsla(0, 0%, 65%, 1);--color12:hsla(0, 0%, 100%, 1);--color0:hsla(0, 0%, 100%, 0);--color02:hsla(0, 0%, 100%, 0.2);--color04:hsla(0, 0%, 100%, 0.4);--color06:hsla(0, 0%, 100%, 0.6);--color08:hsla(0, 0%, 100%, 0.8);--background:hsla(0, 0%, 2%, 1);--backgroundHover:hsla(0, 0%, 8%, 1);--backgroundPress:hsla(0, 0%, 2%, 0.8);--backgroundFocus:hsla(0, 0%, 2%, 0.8);--borderColor:hsla(0, 0%, 14%, 1);--borderColorHover:hsla(0, 0%, 16%, 1);--borderColorPress:hsla(0, 0%, 10%, 1);--borderColorFocus:hsla(0, 0%, 14%, 1);--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--colorTransparent:hsla(0, 0%, 100%, 0);--blue1:hsl(212, 35.0%, 9.2%);--blue2:hsl(216, 50.0%, 11.8%);--blue3:hsl(214, 59.4%, 15.3%);--blue4:hsl(214, 65.8%, 17.9%);--blue5:hsl(213, 71.2%, 20.2%);--blue6:hsl(212, 77.4%, 23.1%);--blue7:hsl(211, 85.1%, 27.4%);--blue8:hsl(211, 89.7%, 34.1%);--blue9:hsl(206, 100%, 50.0%);--blue10:hsl(209, 100%, 60.6%);--blue11:hsl(210, 100%, 66.1%);--blue12:hsl(206, 98.0%, 95.8%);--green1:hsl(146, 30.0%, 7.4%);--green2:hsl(155, 44.2%, 8.4%);--green3:hsl(155, 46.7%, 10.9%);--green4:hsl(154, 48.4%, 12.9%);--green5:hsl(154, 49.7%, 14.9%);--green6:hsl(154, 50.9%, 17.6%);--green7:hsl(153, 51.8%, 21.8%);--green8:hsl(151, 51.7%, 28.4%);--green9:hsl(151, 55.0%, 41.5%);--green10:hsl(151, 49.3%, 46.5%);--green11:hsl(151, 50.0%, 53.2%);--green12:hsl(137, 72.0%, 94.0%);--red1:hsl(353, 23.0%, 9.8%);--red2:hsl(357, 34.4%, 12.0%);--red3:hsl(356, 43.4%, 16.4%);--red4:hsl(356, 47.6%, 19.2%);--red5:hsl(356, 51.1%, 21.9%);--red6:hsl(356, 55.2%, 25.9%);--red7:hsl(357, 60.2%, 31.8%);--red8:hsl(358, 65.0%, 40.4%);--red9:hsl(358, 75.0%, 59.0%);--red10:hsl(358, 85.3%, 64.0%);--red11:hsl(358, 100%, 69.5%);--red12:hsl(351, 89.0%, 96.0%);--yellow1:hsl(45, 100%, 5.5%);--yellow2:hsl(46, 100%, 6.7%);--yellow3:hsl(45, 100%, 8.7%);--yellow4:hsl(45, 100%, 10.4%);--yellow5:hsl(47, 100%, 12.1%);--yellow6:hsl(49, 100%, 14.3%);--yellow7:hsl(49, 90.3%, 18.4%);--yellow8:hsl(50, 100%, 22.0%);--yellow9:hsl(53, 92.0%, 50.0%);--yellow10:hsl(54, 100%, 68.0%);--yellow11:hsl(48, 100%, 47.0%);--yellow12:hsl(53, 100%, 91.0%);--shadow1:rgba(0,0,0,0.2);--shadow2:rgba(0,0,0,0.3);--shadow3:rgba(0,0,0,0.4);--shadow4:rgba(0,0,0,0.5);--shadow5:rgba(0,0,0,0.6);--shadow6:rgba(0,0,0,0.7);--black1:#050505;--black2:#151515;--black3:#191919;--black4:#232323;--black5:#282828;--black6:#323232;--black7:#424242;--black8:#494949;--black9:#545454;--black10:#626262;--black11:#a5a5a5;--black12:#fff;--white1:#fff;--white2:#f2f2f2;--white3:hsl(0, 0%, 93%);--white4:hsl(0, 0%, 91%);--white5:hsl(0, 0%, 88%);--white6:hsl(0, 0%, 85%);--white7:hsl(0, 0%, 82%);--white8:hsl(0, 0%, 76%);--white9:hsl(0, 0%, 56%);--white10:hsl(0, 0%, 50%);--white11:hsl(0, 0%, 42%);--white12:hsl(0, 0%, 9%);--shadowColor:rgba(0,0,0,0.2);--accent1:hsla(0, 0%, 100%, 1);--accent2:hsla(0, 0%, 95%, 1);--accent3:hsla(0, 0%, 93%, 1);--accent4:hsla(0, 0%, 91%, 1);--accent5:hsla(0, 0%, 88%, 1);--accent6:hsla(0, 0%, 85%, 1);--accent7:hsla(0, 0%, 82%, 1);--accent8:hsla(0, 0%, 76%, 1);--accent9:hsla(0, 0%, 56%, 1);--accent10:hsla(0, 0%, 50%, 1);--accent11:hsla(0, 0%, 42%, 1);--accent12:hsla(0, 0%, 9%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    :root {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(0, 0%, 2%, 0);--background02:hsla(0, 0%, 2%, 0.2);--background04:hsla(0, 0%, 2%, 0.4);--background06:hsla(0, 0%, 2%, 0.6);--background08:hsla(0, 0%, 2%, 0.8);--color1:hsla(0, 0%, 2%, 1);--color2:hsla(0, 0%, 8%, 1);--color3:hsla(0, 0%, 10%, 1);--color4:hsla(0, 0%, 14%, 1);--color5:hsla(0, 0%, 16%, 1);--color6:hsla(0, 0%, 20%, 1);--color7:hsla(0, 0%, 26%, 1);--color8:hsla(0, 0%, 29%, 1);--color9:hsla(0, 0%, 33%, 1);--color10:hsla(0, 0%, 38%, 1);--color11:hsla(0, 0%, 65%, 1);--color12:hsla(0, 0%, 100%, 1);--color0:hsla(0, 0%, 100%, 0);--color02:hsla(0, 0%, 100%, 0.2);--color04:hsla(0, 0%, 100%, 0.4);--color06:hsla(0, 0%, 100%, 0.6);--color08:hsla(0, 0%, 100%, 0.8);--background:hsla(0, 0%, 2%, 1);--backgroundHover:hsla(0, 0%, 8%, 1);--backgroundPress:hsla(0, 0%, 2%, 0.8);--backgroundFocus:hsla(0, 0%, 2%, 0.8);--borderColor:hsla(0, 0%, 14%, 1);--borderColorHover:hsla(0, 0%, 16%, 1);--borderColorPress:hsla(0, 0%, 10%, 1);--borderColorFocus:hsla(0, 0%, 14%, 1);--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--colorTransparent:hsla(0, 0%, 100%, 0);--blue1:hsl(212, 35.0%, 9.2%);--blue2:hsl(216, 50.0%, 11.8%);--blue3:hsl(214, 59.4%, 15.3%);--blue4:hsl(214, 65.8%, 17.9%);--blue5:hsl(213, 71.2%, 20.2%);--blue6:hsl(212, 77.4%, 23.1%);--blue7:hsl(211, 85.1%, 27.4%);--blue8:hsl(211, 89.7%, 34.1%);--blue9:hsl(206, 100%, 50.0%);--blue10:hsl(209, 100%, 60.6%);--blue11:hsl(210, 100%, 66.1%);--blue12:hsl(206, 98.0%, 95.8%);--green1:hsl(146, 30.0%, 7.4%);--green2:hsl(155, 44.2%, 8.4%);--green3:hsl(155, 46.7%, 10.9%);--green4:hsl(154, 48.4%, 12.9%);--green5:hsl(154, 49.7%, 14.9%);--green6:hsl(154, 50.9%, 17.6%);--green7:hsl(153, 51.8%, 21.8%);--green8:hsl(151, 51.7%, 28.4%);--green9:hsl(151, 55.0%, 41.5%);--green10:hsl(151, 49.3%, 46.5%);--green11:hsl(151, 50.0%, 53.2%);--green12:hsl(137, 72.0%, 94.0%);--red1:hsl(353, 23.0%, 9.8%);--red2:hsl(357, 34.4%, 12.0%);--red3:hsl(356, 43.4%, 16.4%);--red4:hsl(356, 47.6%, 19.2%);--red5:hsl(356, 51.1%, 21.9%);--red6:hsl(356, 55.2%, 25.9%);--red7:hsl(357, 60.2%, 31.8%);--red8:hsl(358, 65.0%, 40.4%);--red9:hsl(358, 75.0%, 59.0%);--red10:hsl(358, 85.3%, 64.0%);--red11:hsl(358, 100%, 69.5%);--red12:hsl(351, 89.0%, 96.0%);--yellow1:hsl(45, 100%, 5.5%);--yellow2:hsl(46, 100%, 6.7%);--yellow3:hsl(45, 100%, 8.7%);--yellow4:hsl(45, 100%, 10.4%);--yellow5:hsl(47, 100%, 12.1%);--yellow6:hsl(49, 100%, 14.3%);--yellow7:hsl(49, 90.3%, 18.4%);--yellow8:hsl(50, 100%, 22.0%);--yellow9:hsl(53, 92.0%, 50.0%);--yellow10:hsl(54, 100%, 68.0%);--yellow11:hsl(48, 100%, 47.0%);--yellow12:hsl(53, 100%, 91.0%);--shadow1:rgba(0,0,0,0.2);--shadow2:rgba(0,0,0,0.3);--shadow3:rgba(0,0,0,0.4);--shadow4:rgba(0,0,0,0.5);--shadow5:rgba(0,0,0,0.6);--shadow6:rgba(0,0,0,0.7);--black1:#050505;--black2:#151515;--black3:#191919;--black4:#232323;--black5:#282828;--black6:#323232;--black7:#424242;--black8:#494949;--black9:#545454;--black10:#626262;--black11:#a5a5a5;--black12:#fff;--white1:#fff;--white2:#f2f2f2;--white3:hsl(0, 0%, 93%);--white4:hsl(0, 0%, 91%);--white5:hsl(0, 0%, 88%);--white6:hsl(0, 0%, 85%);--white7:hsl(0, 0%, 82%);--white8:hsl(0, 0%, 76%);--white9:hsl(0, 0%, 56%);--white10:hsl(0, 0%, 50%);--white11:hsl(0, 0%, 42%);--white12:hsl(0, 0%, 9%);--shadowColor:rgba(0,0,0,0.2);--accent1:hsla(0, 0%, 100%, 1);--accent2:hsla(0, 0%, 95%, 1);--accent3:hsla(0, 0%, 93%, 1);--accent4:hsla(0, 0%, 91%, 1);--accent5:hsla(0, 0%, 88%, 1);--accent6:hsla(0, 0%, 85%, 1);--accent7:hsla(0, 0%, 82%, 1);--accent8:hsla(0, 0%, 76%, 1);--accent9:hsla(0, 0%, 56%, 1);--accent10:hsla(0, 0%, 50%, 1);--accent11:hsla(0, 0%, 42%, 1);--accent12:hsla(0, 0%, 9%, 1);}
  }
.t_dark ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_accent, :root.t_light .t_accent, .tm_xxt {--accentBackground:hsla(0, 0%, 93%, 1);--accentColor:hsla(0, 0%, 50%, 1);--background0:hsla(0, 0%, 2%, 0);--background02:hsla(0, 0%, 2%, 0.2);--background04:hsla(0, 0%, 2%, 0.4);--background06:hsla(0, 0%, 2%, 0.6);--background08:hsla(0, 0%, 2%, 0.8);--color1:hsla(0, 0%, 2%, 1);--color2:hsla(0, 0%, 8%, 1);--color3:hsla(0, 0%, 10%, 1);--color4:hsla(0, 0%, 14%, 1);--color5:hsla(0, 0%, 16%, 1);--color6:hsla(0, 0%, 20%, 1);--color7:hsla(0, 0%, 26%, 1);--color8:hsla(0, 0%, 29%, 1);--color9:hsla(0, 0%, 33%, 1);--color10:hsla(0, 0%, 38%, 1);--color11:hsla(0, 0%, 65%, 1);--color12:hsla(0, 0%, 100%, 1);--color0:hsla(0, 0%, 100%, 0);--color02:hsla(0, 0%, 100%, 0.2);--color04:hsla(0, 0%, 100%, 0.4);--color06:hsla(0, 0%, 100%, 0.6);--color08:hsla(0, 0%, 100%, 0.8);--background:hsla(0, 0%, 2%, 1);--backgroundHover:hsla(0, 0%, 2%, 0.8);--backgroundPress:hsla(0, 0%, 8%, 1);--backgroundFocus:hsla(0, 0%, 8%, 1);--borderColor:hsla(0, 0%, 14%, 1);--borderColorHover:hsla(0, 0%, 10%, 1);--borderColorPress:hsla(0, 0%, 16%, 1);--borderColorFocus:hsla(0, 0%, 14%, 1);--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--colorTransparent:hsla(0, 0%, 100%, 0);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_accent {--accentBackground:hsla(0, 0%, 93%, 1);--accentColor:hsla(0, 0%, 50%, 1);--background0:hsla(0, 0%, 2%, 0);--background02:hsla(0, 0%, 2%, 0.2);--background04:hsla(0, 0%, 2%, 0.4);--background06:hsla(0, 0%, 2%, 0.6);--background08:hsla(0, 0%, 2%, 0.8);--color1:hsla(0, 0%, 2%, 1);--color2:hsla(0, 0%, 8%, 1);--color3:hsla(0, 0%, 10%, 1);--color4:hsla(0, 0%, 14%, 1);--color5:hsla(0, 0%, 16%, 1);--color6:hsla(0, 0%, 20%, 1);--color7:hsla(0, 0%, 26%, 1);--color8:hsla(0, 0%, 29%, 1);--color9:hsla(0, 0%, 33%, 1);--color10:hsla(0, 0%, 38%, 1);--color11:hsla(0, 0%, 65%, 1);--color12:hsla(0, 0%, 100%, 1);--color0:hsla(0, 0%, 100%, 0);--color02:hsla(0, 0%, 100%, 0.2);--color04:hsla(0, 0%, 100%, 0.4);--color06:hsla(0, 0%, 100%, 0.6);--color08:hsla(0, 0%, 100%, 0.8);--background:hsla(0, 0%, 2%, 1);--backgroundHover:hsla(0, 0%, 2%, 0.8);--backgroundPress:hsla(0, 0%, 8%, 1);--backgroundFocus:hsla(0, 0%, 8%, 1);--borderColor:hsla(0, 0%, 14%, 1);--borderColorHover:hsla(0, 0%, 10%, 1);--borderColorPress:hsla(0, 0%, 16%, 1);--borderColorFocus:hsla(0, 0%, 14%, 1);--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--colorTransparent:hsla(0, 0%, 100%, 0);}
  }
.t_light_accent ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_accent, :root.t_light .t_dark .t_accent, .tm_xxt {--accentBackground:hsla(0, 0%, 38%, 1);--accentColor:hsla(0, 0%, 10%, 1);--background0:hsla(0, 0%, 100%, 0);--background02:hsla(0, 0%, 100%, 0.2);--background04:hsla(0, 0%, 100%, 0.4);--background06:hsla(0, 0%, 100%, 0.6);--background08:hsla(0, 0%, 100%, 0.8);--color1:hsla(0, 0%, 100%, 1);--color2:hsla(0, 0%, 95%, 1);--color3:hsla(0, 0%, 93%, 1);--color4:hsla(0, 0%, 91%, 1);--color5:hsla(0, 0%, 88%, 1);--color6:hsla(0, 0%, 85%, 1);--color7:hsla(0, 0%, 82%, 1);--color8:hsla(0, 0%, 76%, 1);--color9:hsla(0, 0%, 56%, 1);--color10:hsla(0, 0%, 50%, 1);--color11:hsla(0, 0%, 42%, 1);--color12:hsla(0, 0%, 9%, 1);--color0:hsla(0, 0%, 9%, 0);--color02:hsla(0, 0%, 9%, 0.2);--color04:hsla(0, 0%, 9%, 0.4);--color06:hsla(0, 0%, 9%, 0.6);--color08:hsla(0, 0%, 9%, 0.8);--background:hsla(0, 0%, 100%, 1);--backgroundHover:hsla(0, 0%, 95%, 1);--backgroundPress:hsla(0, 0%, 100%, 0.8);--backgroundFocus:hsla(0, 0%, 100%, 0.8);--borderColor:hsla(0, 0%, 91%, 1);--borderColorHover:hsla(0, 0%, 88%, 1);--borderColorPress:hsla(0, 0%, 93%, 1);--borderColorFocus:hsla(0, 0%, 91%, 1);--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--colorTransparent:hsla(0, 0%, 9%, 0);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_accent {--accentBackground:hsla(0, 0%, 38%, 1);--accentColor:hsla(0, 0%, 10%, 1);--background0:hsla(0, 0%, 100%, 0);--background02:hsla(0, 0%, 100%, 0.2);--background04:hsla(0, 0%, 100%, 0.4);--background06:hsla(0, 0%, 100%, 0.6);--background08:hsla(0, 0%, 100%, 0.8);--color1:hsla(0, 0%, 100%, 1);--color2:hsla(0, 0%, 95%, 1);--color3:hsla(0, 0%, 93%, 1);--color4:hsla(0, 0%, 91%, 1);--color5:hsla(0, 0%, 88%, 1);--color6:hsla(0, 0%, 85%, 1);--color7:hsla(0, 0%, 82%, 1);--color8:hsla(0, 0%, 76%, 1);--color9:hsla(0, 0%, 56%, 1);--color10:hsla(0, 0%, 50%, 1);--color11:hsla(0, 0%, 42%, 1);--color12:hsla(0, 0%, 9%, 1);--color0:hsla(0, 0%, 9%, 0);--color02:hsla(0, 0%, 9%, 0.2);--color04:hsla(0, 0%, 9%, 0.4);--color06:hsla(0, 0%, 9%, 0.6);--color08:hsla(0, 0%, 9%, 0.8);--background:hsla(0, 0%, 100%, 1);--backgroundHover:hsla(0, 0%, 95%, 1);--backgroundPress:hsla(0, 0%, 100%, 0.8);--backgroundFocus:hsla(0, 0%, 100%, 0.8);--borderColor:hsla(0, 0%, 91%, 1);--borderColorHover:hsla(0, 0%, 88%, 1);--borderColorPress:hsla(0, 0%, 93%, 1);--borderColorFocus:hsla(0, 0%, 91%, 1);--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--colorTransparent:hsla(0, 0%, 9%, 0);}
  }
.t_dark_accent ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_black, :root.t_light .t_black, .tm_xxt {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(0, 0%, 2%, 0);--background02:hsla(0, 0%, 2%, 0.2);--background04:hsla(0, 0%, 2%, 0.4);--background06:hsla(0, 0%, 2%, 0.6);--background08:hsla(0, 0%, 2%, 0.8);--color1:hsla(0, 0%, 2%, 1);--color2:hsla(0, 0%, 8%, 1);--color3:hsla(0, 0%, 10%, 1);--color4:hsla(0, 0%, 14%, 1);--color5:hsla(0, 0%, 16%, 1);--color6:hsla(0, 0%, 20%, 1);--color7:hsla(0, 0%, 26%, 1);--color8:hsla(0, 0%, 29%, 1);--color9:hsla(0, 0%, 33%, 1);--color10:hsla(0, 0%, 38%, 1);--color11:hsla(0, 0%, 65%, 1);--color12:hsla(0, 0%, 100%, 1);--color0:hsla(0, 0%, 100%, 0);--color02:hsla(0, 0%, 100%, 0.2);--color04:hsla(0, 0%, 100%, 0.4);--color06:hsla(0, 0%, 100%, 0.6);--color08:hsla(0, 0%, 100%, 0.8);--background:hsla(0, 0%, 2%, 1);--backgroundHover:hsla(0, 0%, 2%, 0.8);--backgroundPress:hsla(0, 0%, 8%, 1);--backgroundFocus:hsla(0, 0%, 8%, 1);--borderColor:hsla(0, 0%, 14%, 1);--borderColorHover:hsla(0, 0%, 10%, 1);--borderColorPress:hsla(0, 0%, 16%, 1);--borderColorFocus:hsla(0, 0%, 14%, 1);--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--colorTransparent:hsla(0, 0%, 100%, 0);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_black {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(0, 0%, 2%, 0);--background02:hsla(0, 0%, 2%, 0.2);--background04:hsla(0, 0%, 2%, 0.4);--background06:hsla(0, 0%, 2%, 0.6);--background08:hsla(0, 0%, 2%, 0.8);--color1:hsla(0, 0%, 2%, 1);--color2:hsla(0, 0%, 8%, 1);--color3:hsla(0, 0%, 10%, 1);--color4:hsla(0, 0%, 14%, 1);--color5:hsla(0, 0%, 16%, 1);--color6:hsla(0, 0%, 20%, 1);--color7:hsla(0, 0%, 26%, 1);--color8:hsla(0, 0%, 29%, 1);--color9:hsla(0, 0%, 33%, 1);--color10:hsla(0, 0%, 38%, 1);--color11:hsla(0, 0%, 65%, 1);--color12:hsla(0, 0%, 100%, 1);--color0:hsla(0, 0%, 100%, 0);--color02:hsla(0, 0%, 100%, 0.2);--color04:hsla(0, 0%, 100%, 0.4);--color06:hsla(0, 0%, 100%, 0.6);--color08:hsla(0, 0%, 100%, 0.8);--background:hsla(0, 0%, 2%, 1);--backgroundHover:hsla(0, 0%, 2%, 0.8);--backgroundPress:hsla(0, 0%, 8%, 1);--backgroundFocus:hsla(0, 0%, 8%, 1);--borderColor:hsla(0, 0%, 14%, 1);--borderColorHover:hsla(0, 0%, 10%, 1);--borderColorPress:hsla(0, 0%, 16%, 1);--borderColorFocus:hsla(0, 0%, 14%, 1);--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--colorTransparent:hsla(0, 0%, 100%, 0);}
  }
.t_light_black ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_white, :root.t_light .t_white, .tm_xxt {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(0, 0%, 100%, 0);--background02:hsla(0, 0%, 100%, 0.2);--background04:hsla(0, 0%, 100%, 0.4);--background06:hsla(0, 0%, 100%, 0.6);--background08:hsla(0, 0%, 100%, 0.8);--color1:hsla(0, 0%, 100%, 1);--color2:hsla(0, 0%, 95%, 1);--color3:hsla(0, 0%, 93%, 1);--color4:hsla(0, 0%, 91%, 1);--color5:hsla(0, 0%, 88%, 1);--color6:hsla(0, 0%, 85%, 1);--color7:hsla(0, 0%, 82%, 1);--color8:hsla(0, 0%, 76%, 1);--color9:hsla(0, 0%, 56%, 1);--color10:hsla(0, 0%, 50%, 1);--color11:hsla(0, 0%, 42%, 1);--color12:hsla(0, 0%, 9%, 1);--color0:hsla(0, 0%, 9%, 0);--color02:hsla(0, 0%, 9%, 0.2);--color04:hsla(0, 0%, 9%, 0.4);--color06:hsla(0, 0%, 9%, 0.6);--color08:hsla(0, 0%, 9%, 0.8);--background:hsla(0, 0%, 100%, 1);--backgroundHover:hsla(0, 0%, 100%, 0.8);--backgroundPress:hsla(0, 0%, 95%, 1);--backgroundFocus:hsla(0, 0%, 95%, 1);--borderColor:hsla(0, 0%, 91%, 1);--borderColorHover:hsla(0, 0%, 93%, 1);--borderColorPress:hsla(0, 0%, 88%, 1);--borderColorFocus:hsla(0, 0%, 91%, 1);--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--colorTransparent:hsla(0, 0%, 9%, 0);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_white {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(0, 0%, 100%, 0);--background02:hsla(0, 0%, 100%, 0.2);--background04:hsla(0, 0%, 100%, 0.4);--background06:hsla(0, 0%, 100%, 0.6);--background08:hsla(0, 0%, 100%, 0.8);--color1:hsla(0, 0%, 100%, 1);--color2:hsla(0, 0%, 95%, 1);--color3:hsla(0, 0%, 93%, 1);--color4:hsla(0, 0%, 91%, 1);--color5:hsla(0, 0%, 88%, 1);--color6:hsla(0, 0%, 85%, 1);--color7:hsla(0, 0%, 82%, 1);--color8:hsla(0, 0%, 76%, 1);--color9:hsla(0, 0%, 56%, 1);--color10:hsla(0, 0%, 50%, 1);--color11:hsla(0, 0%, 42%, 1);--color12:hsla(0, 0%, 9%, 1);--color0:hsla(0, 0%, 9%, 0);--color02:hsla(0, 0%, 9%, 0.2);--color04:hsla(0, 0%, 9%, 0.4);--color06:hsla(0, 0%, 9%, 0.6);--color08:hsla(0, 0%, 9%, 0.8);--background:hsla(0, 0%, 100%, 1);--backgroundHover:hsla(0, 0%, 100%, 0.8);--backgroundPress:hsla(0, 0%, 95%, 1);--backgroundFocus:hsla(0, 0%, 95%, 1);--borderColor:hsla(0, 0%, 91%, 1);--borderColorHover:hsla(0, 0%, 93%, 1);--borderColorPress:hsla(0, 0%, 88%, 1);--borderColorFocus:hsla(0, 0%, 91%, 1);--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--colorTransparent:hsla(0, 0%, 9%, 0);}
  }
.t_light_white ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_blue, :root.t_light .t_blue, .tm_xxt {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(216, 100%, 99%, 0);--background02:hsla(216, 100%, 99%, 0.2);--background04:hsla(216, 100%, 99%, 0.4);--background06:hsla(216, 100%, 99%, 0.6);--background08:hsla(216, 100%, 99%, 0.8);--color1:hsla(210, 100%, 99%, 1);--color2:hsla(210, 100%, 98%, 1);--color3:hsla(210, 100%, 96%, 1);--color4:hsla(210, 100%, 94%, 1);--color5:hsla(209, 96%, 90%, 1);--color6:hsla(209, 82%, 85%, 1);--color7:hsla(208, 78%, 77%, 1);--color8:hsla(206, 82%, 65%, 1);--color9:hsla(206, 100%, 50%, 1);--color10:hsla(208, 100%, 47%, 1);--color11:hsla(211, 100%, 43%, 1);--color12:hsla(211, 100%, 15%, 1);--color0:hsla(211, 100%, 15%, 0);--color02:hsla(211, 100%, 15%, 0.2);--color04:hsla(211, 100%, 15%, 0.4);--color06:hsla(211, 100%, 15%, 0.6);--color08:hsla(211, 100%, 15%, 0.8);--background:hsla(210, 100%, 99%, 1);--backgroundHover:hsla(216, 100%, 99%, 0.8);--backgroundPress:hsla(210, 100%, 98%, 1);--backgroundFocus:hsla(210, 100%, 98%, 1);--borderColor:hsla(210, 100%, 94%, 1);--borderColorHover:hsla(210, 100%, 96%, 1);--borderColorPress:hsla(209, 96%, 90%, 1);--borderColorFocus:hsla(210, 100%, 94%, 1);--color:hsla(211, 100%, 15%, 1);--colorHover:hsla(211, 100%, 43%, 1);--colorPress:hsla(211, 100%, 15%, 1);--colorFocus:hsla(211, 100%, 43%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(211, 100%, 15%, 0.2);--colorTransparent:hsla(211, 100%, 15%, 0);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_blue {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(216, 100%, 99%, 0);--background02:hsla(216, 100%, 99%, 0.2);--background04:hsla(216, 100%, 99%, 0.4);--background06:hsla(216, 100%, 99%, 0.6);--background08:hsla(216, 100%, 99%, 0.8);--color1:hsla(210, 100%, 99%, 1);--color2:hsla(210, 100%, 98%, 1);--color3:hsla(210, 100%, 96%, 1);--color4:hsla(210, 100%, 94%, 1);--color5:hsla(209, 96%, 90%, 1);--color6:hsla(209, 82%, 85%, 1);--color7:hsla(208, 78%, 77%, 1);--color8:hsla(206, 82%, 65%, 1);--color9:hsla(206, 100%, 50%, 1);--color10:hsla(208, 100%, 47%, 1);--color11:hsla(211, 100%, 43%, 1);--color12:hsla(211, 100%, 15%, 1);--color0:hsla(211, 100%, 15%, 0);--color02:hsla(211, 100%, 15%, 0.2);--color04:hsla(211, 100%, 15%, 0.4);--color06:hsla(211, 100%, 15%, 0.6);--color08:hsla(211, 100%, 15%, 0.8);--background:hsla(210, 100%, 99%, 1);--backgroundHover:hsla(216, 100%, 99%, 0.8);--backgroundPress:hsla(210, 100%, 98%, 1);--backgroundFocus:hsla(210, 100%, 98%, 1);--borderColor:hsla(210, 100%, 94%, 1);--borderColorHover:hsla(210, 100%, 96%, 1);--borderColorPress:hsla(209, 96%, 90%, 1);--borderColorFocus:hsla(210, 100%, 94%, 1);--color:hsla(211, 100%, 15%, 1);--colorHover:hsla(211, 100%, 43%, 1);--colorPress:hsla(211, 100%, 15%, 1);--colorFocus:hsla(211, 100%, 43%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(211, 100%, 15%, 0.2);--colorTransparent:hsla(211, 100%, 15%, 0);}
  }
.t_light_blue ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_red, :root.t_light .t_red, .tm_xxt {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(0, 100%, 99%, 0);--background02:hsla(0, 100%, 99%, 0.2);--background04:hsla(0, 100%, 99%, 0.4);--background06:hsla(0, 100%, 99%, 0.6);--background08:hsla(0, 100%, 99%, 0.8);--color1:hsla(0, 100%, 99%, 1);--color2:hsla(0, 100%, 99%, 1);--color3:hsla(0, 100%, 97%, 1);--color4:hsla(0, 100%, 95%, 1);--color5:hsla(0, 90%, 92%, 1);--color6:hsla(0, 81%, 88%, 1);--color7:hsla(359, 74%, 82%, 1);--color8:hsla(359, 69%, 74%, 1);--color9:hsla(358, 75%, 59%, 1);--color10:hsla(358, 69%, 55%, 1);--color11:hsla(358, 65%, 49%, 1);--color12:hsla(355, 49%, 15%, 1);--color0:hsla(355, 48%, 15%, 0);--color02:hsla(355, 48%, 15%, 0.2);--color04:hsla(355, 48%, 15%, 0.4);--color06:hsla(355, 48%, 15%, 0.6);--color08:hsla(355, 48%, 15%, 0.8);--background:hsla(0, 100%, 99%, 1);--backgroundHover:hsla(0, 100%, 99%, 0.8);--backgroundPress:hsla(0, 100%, 99%, 1);--backgroundFocus:hsla(0, 100%, 99%, 1);--borderColor:hsla(0, 100%, 95%, 1);--borderColorHover:hsla(0, 100%, 97%, 1);--borderColorPress:hsla(0, 90%, 92%, 1);--borderColorFocus:hsla(0, 100%, 95%, 1);--color:hsla(355, 49%, 15%, 1);--colorHover:hsla(358, 65%, 49%, 1);--colorPress:hsla(355, 49%, 15%, 1);--colorFocus:hsla(358, 65%, 49%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(355, 48%, 15%, 0.2);--colorTransparent:hsla(355, 48%, 15%, 0);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_red {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(0, 100%, 99%, 0);--background02:hsla(0, 100%, 99%, 0.2);--background04:hsla(0, 100%, 99%, 0.4);--background06:hsla(0, 100%, 99%, 0.6);--background08:hsla(0, 100%, 99%, 0.8);--color1:hsla(0, 100%, 99%, 1);--color2:hsla(0, 100%, 99%, 1);--color3:hsla(0, 100%, 97%, 1);--color4:hsla(0, 100%, 95%, 1);--color5:hsla(0, 90%, 92%, 1);--color6:hsla(0, 81%, 88%, 1);--color7:hsla(359, 74%, 82%, 1);--color8:hsla(359, 69%, 74%, 1);--color9:hsla(358, 75%, 59%, 1);--color10:hsla(358, 69%, 55%, 1);--color11:hsla(358, 65%, 49%, 1);--color12:hsla(355, 49%, 15%, 1);--color0:hsla(355, 48%, 15%, 0);--color02:hsla(355, 48%, 15%, 0.2);--color04:hsla(355, 48%, 15%, 0.4);--color06:hsla(355, 48%, 15%, 0.6);--color08:hsla(355, 48%, 15%, 0.8);--background:hsla(0, 100%, 99%, 1);--backgroundHover:hsla(0, 100%, 99%, 0.8);--backgroundPress:hsla(0, 100%, 99%, 1);--backgroundFocus:hsla(0, 100%, 99%, 1);--borderColor:hsla(0, 100%, 95%, 1);--borderColorHover:hsla(0, 100%, 97%, 1);--borderColorPress:hsla(0, 90%, 92%, 1);--borderColorFocus:hsla(0, 100%, 95%, 1);--color:hsla(355, 49%, 15%, 1);--colorHover:hsla(358, 65%, 49%, 1);--colorPress:hsla(355, 49%, 15%, 1);--colorFocus:hsla(358, 65%, 49%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(355, 48%, 15%, 0.2);--colorTransparent:hsla(355, 48%, 15%, 0);}
  }
.t_light_red ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_yellow, :root.t_light .t_yellow, .tm_xxt {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(60, 45%, 98%, 0);--background02:hsla(60, 45%, 98%, 0.2);--background04:hsla(60, 45%, 98%, 0.4);--background06:hsla(60, 45%, 98%, 0.6);--background08:hsla(60, 45%, 98%, 0.8);--color1:hsla(60, 50%, 98%, 1);--color2:hsla(52, 100%, 95%, 1);--color3:hsla(55, 100%, 91%, 1);--color4:hsla(54, 100%, 87%, 1);--color5:hsla(52, 98%, 82%, 1);--color6:hsla(50, 90%, 76%, 1);--color7:hsla(47, 80%, 68%, 1);--color8:hsla(48, 100%, 46%, 1);--color9:hsla(53, 92%, 50%, 1);--color10:hsla(50, 100%, 48%, 1);--color11:hsla(42, 100%, 29%, 1);--color12:hsla(41, 56%, 13%, 1);--color0:hsla(41, 55%, 13%, 0);--color02:hsla(41, 55%, 13%, 0.2);--color04:hsla(41, 55%, 13%, 0.4);--color06:hsla(41, 55%, 13%, 0.6);--color08:hsla(41, 55%, 13%, 0.8);--background:hsla(60, 50%, 98%, 1);--backgroundHover:hsla(60, 45%, 98%, 0.8);--backgroundPress:hsla(52, 100%, 95%, 1);--backgroundFocus:hsla(52, 100%, 95%, 1);--borderColor:hsla(54, 100%, 87%, 1);--borderColorHover:hsla(55, 100%, 91%, 1);--borderColorPress:hsla(52, 98%, 82%, 1);--borderColorFocus:hsla(54, 100%, 87%, 1);--color:hsla(41, 56%, 13%, 1);--colorHover:hsla(42, 100%, 29%, 1);--colorPress:hsla(41, 56%, 13%, 1);--colorFocus:hsla(42, 100%, 29%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(41, 55%, 13%, 0.2);--colorTransparent:hsla(41, 55%, 13%, 0);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_yellow {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(60, 45%, 98%, 0);--background02:hsla(60, 45%, 98%, 0.2);--background04:hsla(60, 45%, 98%, 0.4);--background06:hsla(60, 45%, 98%, 0.6);--background08:hsla(60, 45%, 98%, 0.8);--color1:hsla(60, 50%, 98%, 1);--color2:hsla(52, 100%, 95%, 1);--color3:hsla(55, 100%, 91%, 1);--color4:hsla(54, 100%, 87%, 1);--color5:hsla(52, 98%, 82%, 1);--color6:hsla(50, 90%, 76%, 1);--color7:hsla(47, 80%, 68%, 1);--color8:hsla(48, 100%, 46%, 1);--color9:hsla(53, 92%, 50%, 1);--color10:hsla(50, 100%, 48%, 1);--color11:hsla(42, 100%, 29%, 1);--color12:hsla(41, 56%, 13%, 1);--color0:hsla(41, 55%, 13%, 0);--color02:hsla(41, 55%, 13%, 0.2);--color04:hsla(41, 55%, 13%, 0.4);--color06:hsla(41, 55%, 13%, 0.6);--color08:hsla(41, 55%, 13%, 0.8);--background:hsla(60, 50%, 98%, 1);--backgroundHover:hsla(60, 45%, 98%, 0.8);--backgroundPress:hsla(52, 100%, 95%, 1);--backgroundFocus:hsla(52, 100%, 95%, 1);--borderColor:hsla(54, 100%, 87%, 1);--borderColorHover:hsla(55, 100%, 91%, 1);--borderColorPress:hsla(52, 98%, 82%, 1);--borderColorFocus:hsla(54, 100%, 87%, 1);--color:hsla(41, 56%, 13%, 1);--colorHover:hsla(42, 100%, 29%, 1);--colorPress:hsla(41, 56%, 13%, 1);--colorFocus:hsla(42, 100%, 29%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(41, 55%, 13%, 0.2);--colorTransparent:hsla(41, 55%, 13%, 0);}
  }
.t_light_yellow ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_green, :root.t_light .t_green, .tm_xxt {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(140, 60%, 99%, 0);--background02:hsla(140, 60%, 99%, 0.2);--background04:hsla(140, 60%, 99%, 0.4);--background06:hsla(140, 60%, 99%, 0.6);--background08:hsla(140, 60%, 99%, 0.8);--color1:hsla(140, 60%, 99%, 1);--color2:hsla(138, 63%, 97%, 1);--color3:hsla(139, 57%, 95%, 1);--color4:hsla(139, 48%, 91%, 1);--color5:hsla(141, 44%, 86%, 1);--color6:hsla(142, 40%, 79%, 1);--color7:hsla(146, 38%, 69%, 1);--color8:hsla(151, 40%, 54%, 1);--color9:hsla(151, 55%, 42%, 1);--color10:hsla(152, 57%, 38%, 1);--color11:hsla(153, 67%, 28%, 1);--color12:hsla(155, 41%, 14%, 1);--color0:hsla(155, 41%, 14%, 0);--color02:hsla(155, 41%, 14%, 0.2);--color04:hsla(155, 41%, 14%, 0.4);--color06:hsla(155, 41%, 14%, 0.6);--color08:hsla(155, 41%, 14%, 0.8);--background:hsla(140, 60%, 99%, 1);--backgroundHover:hsla(140, 60%, 99%, 0.8);--backgroundPress:hsla(138, 63%, 97%, 1);--backgroundFocus:hsla(138, 63%, 97%, 1);--borderColor:hsla(139, 48%, 91%, 1);--borderColorHover:hsla(139, 57%, 95%, 1);--borderColorPress:hsla(141, 44%, 86%, 1);--borderColorFocus:hsla(139, 48%, 91%, 1);--color:hsla(155, 41%, 14%, 1);--colorHover:hsla(153, 67%, 28%, 1);--colorPress:hsla(155, 41%, 14%, 1);--colorFocus:hsla(153, 67%, 28%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(155, 41%, 14%, 0.2);--colorTransparent:hsla(155, 41%, 14%, 0);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_green {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(140, 60%, 99%, 0);--background02:hsla(140, 60%, 99%, 0.2);--background04:hsla(140, 60%, 99%, 0.4);--background06:hsla(140, 60%, 99%, 0.6);--background08:hsla(140, 60%, 99%, 0.8);--color1:hsla(140, 60%, 99%, 1);--color2:hsla(138, 63%, 97%, 1);--color3:hsla(139, 57%, 95%, 1);--color4:hsla(139, 48%, 91%, 1);--color5:hsla(141, 44%, 86%, 1);--color6:hsla(142, 40%, 79%, 1);--color7:hsla(146, 38%, 69%, 1);--color8:hsla(151, 40%, 54%, 1);--color9:hsla(151, 55%, 42%, 1);--color10:hsla(152, 57%, 38%, 1);--color11:hsla(153, 67%, 28%, 1);--color12:hsla(155, 41%, 14%, 1);--color0:hsla(155, 41%, 14%, 0);--color02:hsla(155, 41%, 14%, 0.2);--color04:hsla(155, 41%, 14%, 0.4);--color06:hsla(155, 41%, 14%, 0.6);--color08:hsla(155, 41%, 14%, 0.8);--background:hsla(140, 60%, 99%, 1);--backgroundHover:hsla(140, 60%, 99%, 0.8);--backgroundPress:hsla(138, 63%, 97%, 1);--backgroundFocus:hsla(138, 63%, 97%, 1);--borderColor:hsla(139, 48%, 91%, 1);--borderColorHover:hsla(139, 57%, 95%, 1);--borderColorPress:hsla(141, 44%, 86%, 1);--borderColorFocus:hsla(139, 48%, 91%, 1);--color:hsla(155, 41%, 14%, 1);--colorHover:hsla(153, 67%, 28%, 1);--colorPress:hsla(155, 41%, 14%, 1);--colorFocus:hsla(153, 67%, 28%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(155, 41%, 14%, 0.2);--colorTransparent:hsla(155, 41%, 14%, 0);}
  }
.t_light_green ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_black, :root.t_light .t_dark .t_black, .tm_xxt {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(0, 0%, 2%, 0);--background02:hsla(0, 0%, 2%, 0.2);--background04:hsla(0, 0%, 2%, 0.4);--background06:hsla(0, 0%, 2%, 0.6);--background08:hsla(0, 0%, 2%, 0.8);--color1:hsla(0, 0%, 2%, 1);--color2:hsla(0, 0%, 8%, 1);--color3:hsla(0, 0%, 10%, 1);--color4:hsla(0, 0%, 14%, 1);--color5:hsla(0, 0%, 16%, 1);--color6:hsla(0, 0%, 20%, 1);--color7:hsla(0, 0%, 26%, 1);--color8:hsla(0, 0%, 29%, 1);--color9:hsla(0, 0%, 33%, 1);--color10:hsla(0, 0%, 38%, 1);--color11:hsla(0, 0%, 65%, 1);--color12:hsla(0, 0%, 100%, 1);--color0:hsla(0, 0%, 100%, 0);--color02:hsla(0, 0%, 100%, 0.2);--color04:hsla(0, 0%, 100%, 0.4);--color06:hsla(0, 0%, 100%, 0.6);--color08:hsla(0, 0%, 100%, 0.8);--background:hsla(0, 0%, 2%, 1);--backgroundHover:hsla(0, 0%, 8%, 1);--backgroundPress:hsla(0, 0%, 2%, 0.8);--backgroundFocus:hsla(0, 0%, 2%, 0.8);--borderColor:hsla(0, 0%, 14%, 1);--borderColorHover:hsla(0, 0%, 16%, 1);--borderColorPress:hsla(0, 0%, 10%, 1);--borderColorFocus:hsla(0, 0%, 14%, 1);--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--colorTransparent:hsla(0, 0%, 100%, 0);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_black {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(0, 0%, 2%, 0);--background02:hsla(0, 0%, 2%, 0.2);--background04:hsla(0, 0%, 2%, 0.4);--background06:hsla(0, 0%, 2%, 0.6);--background08:hsla(0, 0%, 2%, 0.8);--color1:hsla(0, 0%, 2%, 1);--color2:hsla(0, 0%, 8%, 1);--color3:hsla(0, 0%, 10%, 1);--color4:hsla(0, 0%, 14%, 1);--color5:hsla(0, 0%, 16%, 1);--color6:hsla(0, 0%, 20%, 1);--color7:hsla(0, 0%, 26%, 1);--color8:hsla(0, 0%, 29%, 1);--color9:hsla(0, 0%, 33%, 1);--color10:hsla(0, 0%, 38%, 1);--color11:hsla(0, 0%, 65%, 1);--color12:hsla(0, 0%, 100%, 1);--color0:hsla(0, 0%, 100%, 0);--color02:hsla(0, 0%, 100%, 0.2);--color04:hsla(0, 0%, 100%, 0.4);--color06:hsla(0, 0%, 100%, 0.6);--color08:hsla(0, 0%, 100%, 0.8);--background:hsla(0, 0%, 2%, 1);--backgroundHover:hsla(0, 0%, 8%, 1);--backgroundPress:hsla(0, 0%, 2%, 0.8);--backgroundFocus:hsla(0, 0%, 2%, 0.8);--borderColor:hsla(0, 0%, 14%, 1);--borderColorHover:hsla(0, 0%, 16%, 1);--borderColorPress:hsla(0, 0%, 10%, 1);--borderColorFocus:hsla(0, 0%, 14%, 1);--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--colorTransparent:hsla(0, 0%, 100%, 0);}
  }
.t_dark_black ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_white, :root.t_light .t_dark .t_white, .tm_xxt {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(0, 0%, 100%, 0);--background02:hsla(0, 0%, 100%, 0.2);--background04:hsla(0, 0%, 100%, 0.4);--background06:hsla(0, 0%, 100%, 0.6);--background08:hsla(0, 0%, 100%, 0.8);--color1:hsla(0, 0%, 100%, 1);--color2:hsla(0, 0%, 95%, 1);--color3:hsla(0, 0%, 93%, 1);--color4:hsla(0, 0%, 91%, 1);--color5:hsla(0, 0%, 88%, 1);--color6:hsla(0, 0%, 85%, 1);--color7:hsla(0, 0%, 82%, 1);--color8:hsla(0, 0%, 76%, 1);--color9:hsla(0, 0%, 56%, 1);--color10:hsla(0, 0%, 50%, 1);--color11:hsla(0, 0%, 42%, 1);--color12:hsla(0, 0%, 9%, 1);--color0:hsla(0, 0%, 9%, 0);--color02:hsla(0, 0%, 9%, 0.2);--color04:hsla(0, 0%, 9%, 0.4);--color06:hsla(0, 0%, 9%, 0.6);--color08:hsla(0, 0%, 9%, 0.8);--background:hsla(0, 0%, 100%, 1);--backgroundHover:hsla(0, 0%, 95%, 1);--backgroundPress:hsla(0, 0%, 100%, 0.8);--backgroundFocus:hsla(0, 0%, 100%, 0.8);--borderColor:hsla(0, 0%, 91%, 1);--borderColorHover:hsla(0, 0%, 88%, 1);--borderColorPress:hsla(0, 0%, 93%, 1);--borderColorFocus:hsla(0, 0%, 91%, 1);--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--colorTransparent:hsla(0, 0%, 9%, 0);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_white {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(0, 0%, 100%, 0);--background02:hsla(0, 0%, 100%, 0.2);--background04:hsla(0, 0%, 100%, 0.4);--background06:hsla(0, 0%, 100%, 0.6);--background08:hsla(0, 0%, 100%, 0.8);--color1:hsla(0, 0%, 100%, 1);--color2:hsla(0, 0%, 95%, 1);--color3:hsla(0, 0%, 93%, 1);--color4:hsla(0, 0%, 91%, 1);--color5:hsla(0, 0%, 88%, 1);--color6:hsla(0, 0%, 85%, 1);--color7:hsla(0, 0%, 82%, 1);--color8:hsla(0, 0%, 76%, 1);--color9:hsla(0, 0%, 56%, 1);--color10:hsla(0, 0%, 50%, 1);--color11:hsla(0, 0%, 42%, 1);--color12:hsla(0, 0%, 9%, 1);--color0:hsla(0, 0%, 9%, 0);--color02:hsla(0, 0%, 9%, 0.2);--color04:hsla(0, 0%, 9%, 0.4);--color06:hsla(0, 0%, 9%, 0.6);--color08:hsla(0, 0%, 9%, 0.8);--background:hsla(0, 0%, 100%, 1);--backgroundHover:hsla(0, 0%, 95%, 1);--backgroundPress:hsla(0, 0%, 100%, 0.8);--backgroundFocus:hsla(0, 0%, 100%, 0.8);--borderColor:hsla(0, 0%, 91%, 1);--borderColorHover:hsla(0, 0%, 88%, 1);--borderColorPress:hsla(0, 0%, 93%, 1);--borderColorFocus:hsla(0, 0%, 91%, 1);--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--colorTransparent:hsla(0, 0%, 9%, 0);}
  }
.t_dark_white ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_blue, :root.t_light .t_dark .t_blue, .tm_xxt {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(214, 35%, 9%, 0);--background02:hsla(214, 35%, 9%, 0.2);--background04:hsla(214, 35%, 9%, 0.4);--background06:hsla(214, 35%, 9%, 0.6);--background08:hsla(214, 35%, 9%, 0.8);--color1:hsla(212, 36%, 9%, 1);--color2:hsla(216, 50%, 12%, 1);--color3:hsla(214, 59%, 15%, 1);--color4:hsla(214, 65%, 18%, 1);--color5:hsla(213, 71%, 20%, 1);--color6:hsla(212, 78%, 23%, 1);--color7:hsla(211, 86%, 27%, 1);--color8:hsla(211, 90%, 34%, 1);--color9:hsla(206, 100%, 50%, 1);--color10:hsla(209, 100%, 61%, 1);--color11:hsla(210, 100%, 66%, 1);--color12:hsla(206, 100%, 96%, 1);--color0:hsla(207, 100%, 96%, 0);--color02:hsla(207, 100%, 96%, 0.2);--color04:hsla(207, 100%, 96%, 0.4);--color06:hsla(207, 100%, 96%, 0.6);--color08:hsla(207, 100%, 96%, 0.8);--background:hsla(212, 36%, 9%, 1);--backgroundHover:hsla(216, 50%, 12%, 1);--backgroundPress:hsla(214, 35%, 9%, 0.8);--backgroundFocus:hsla(214, 35%, 9%, 0.8);--borderColor:hsla(214, 65%, 18%, 1);--borderColorHover:hsla(213, 71%, 20%, 1);--borderColorPress:hsla(214, 59%, 15%, 1);--borderColorFocus:hsla(214, 65%, 18%, 1);--color:hsla(206, 100%, 96%, 1);--colorHover:hsla(210, 100%, 66%, 1);--colorPress:hsla(206, 100%, 96%, 1);--colorFocus:hsla(210, 100%, 66%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(207, 100%, 96%, 0.2);--colorTransparent:hsla(207, 100%, 96%, 0);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_blue {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(214, 35%, 9%, 0);--background02:hsla(214, 35%, 9%, 0.2);--background04:hsla(214, 35%, 9%, 0.4);--background06:hsla(214, 35%, 9%, 0.6);--background08:hsla(214, 35%, 9%, 0.8);--color1:hsla(212, 36%, 9%, 1);--color2:hsla(216, 50%, 12%, 1);--color3:hsla(214, 59%, 15%, 1);--color4:hsla(214, 65%, 18%, 1);--color5:hsla(213, 71%, 20%, 1);--color6:hsla(212, 78%, 23%, 1);--color7:hsla(211, 86%, 27%, 1);--color8:hsla(211, 90%, 34%, 1);--color9:hsla(206, 100%, 50%, 1);--color10:hsla(209, 100%, 61%, 1);--color11:hsla(210, 100%, 66%, 1);--color12:hsla(206, 100%, 96%, 1);--color0:hsla(207, 100%, 96%, 0);--color02:hsla(207, 100%, 96%, 0.2);--color04:hsla(207, 100%, 96%, 0.4);--color06:hsla(207, 100%, 96%, 0.6);--color08:hsla(207, 100%, 96%, 0.8);--background:hsla(212, 36%, 9%, 1);--backgroundHover:hsla(216, 50%, 12%, 1);--backgroundPress:hsla(214, 35%, 9%, 0.8);--backgroundFocus:hsla(214, 35%, 9%, 0.8);--borderColor:hsla(214, 65%, 18%, 1);--borderColorHover:hsla(213, 71%, 20%, 1);--borderColorPress:hsla(214, 59%, 15%, 1);--borderColorFocus:hsla(214, 65%, 18%, 1);--color:hsla(206, 100%, 96%, 1);--colorHover:hsla(210, 100%, 66%, 1);--colorPress:hsla(206, 100%, 96%, 1);--colorFocus:hsla(210, 100%, 66%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(207, 100%, 96%, 0.2);--colorTransparent:hsla(207, 100%, 96%, 0);}
  }
.t_dark_blue ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_red, :root.t_light .t_dark .t_red, .tm_xxt {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(351, 25%, 10%, 0);--background02:hsla(351, 25%, 10%, 0.2);--background04:hsla(351, 25%, 10%, 0.4);--background06:hsla(351, 25%, 10%, 0.6);--background08:hsla(351, 25%, 10%, 0.8);--color1:hsla(350, 24%, 10%, 1);--color2:hsla(357, 34%, 12%, 1);--color3:hsla(357, 43%, 16%, 1);--color4:hsla(356, 47%, 19%, 1);--color5:hsla(356, 51%, 22%, 1);--color6:hsla(357, 55%, 26%, 1);--color7:hsla(357, 60%, 32%, 1);--color8:hsla(358, 65%, 40%, 1);--color9:hsla(358, 75%, 59%, 1);--color10:hsla(358, 86%, 64%, 1);--color11:hsla(358, 100%, 69%, 1);--color12:hsla(353, 90%, 96%, 1);--color0:hsla(353, 90%, 96%, 0);--color02:hsla(353, 90%, 96%, 0.2);--color04:hsla(353, 90%, 96%, 0.4);--color06:hsla(353, 90%, 96%, 0.6);--color08:hsla(353, 90%, 96%, 0.8);--background:hsla(350, 24%, 10%, 1);--backgroundHover:hsla(357, 34%, 12%, 1);--backgroundPress:hsla(351, 25%, 10%, 0.8);--backgroundFocus:hsla(351, 25%, 10%, 0.8);--borderColor:hsla(356, 47%, 19%, 1);--borderColorHover:hsla(356, 51%, 22%, 1);--borderColorPress:hsla(357, 43%, 16%, 1);--borderColorFocus:hsla(356, 47%, 19%, 1);--color:hsla(353, 90%, 96%, 1);--colorHover:hsla(358, 100%, 69%, 1);--colorPress:hsla(353, 90%, 96%, 1);--colorFocus:hsla(358, 100%, 69%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(353, 90%, 96%, 0.2);--colorTransparent:hsla(353, 90%, 96%, 0);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_red {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(351, 25%, 10%, 0);--background02:hsla(351, 25%, 10%, 0.2);--background04:hsla(351, 25%, 10%, 0.4);--background06:hsla(351, 25%, 10%, 0.6);--background08:hsla(351, 25%, 10%, 0.8);--color1:hsla(350, 24%, 10%, 1);--color2:hsla(357, 34%, 12%, 1);--color3:hsla(357, 43%, 16%, 1);--color4:hsla(356, 47%, 19%, 1);--color5:hsla(356, 51%, 22%, 1);--color6:hsla(357, 55%, 26%, 1);--color7:hsla(357, 60%, 32%, 1);--color8:hsla(358, 65%, 40%, 1);--color9:hsla(358, 75%, 59%, 1);--color10:hsla(358, 86%, 64%, 1);--color11:hsla(358, 100%, 69%, 1);--color12:hsla(353, 90%, 96%, 1);--color0:hsla(353, 90%, 96%, 0);--color02:hsla(353, 90%, 96%, 0.2);--color04:hsla(353, 90%, 96%, 0.4);--color06:hsla(353, 90%, 96%, 0.6);--color08:hsla(353, 90%, 96%, 0.8);--background:hsla(350, 24%, 10%, 1);--backgroundHover:hsla(357, 34%, 12%, 1);--backgroundPress:hsla(351, 25%, 10%, 0.8);--backgroundFocus:hsla(351, 25%, 10%, 0.8);--borderColor:hsla(356, 47%, 19%, 1);--borderColorHover:hsla(356, 51%, 22%, 1);--borderColorPress:hsla(357, 43%, 16%, 1);--borderColorFocus:hsla(356, 47%, 19%, 1);--color:hsla(353, 90%, 96%, 1);--colorHover:hsla(358, 100%, 69%, 1);--colorPress:hsla(353, 90%, 96%, 1);--colorFocus:hsla(358, 100%, 69%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(353, 90%, 96%, 0.2);--colorTransparent:hsla(353, 90%, 96%, 0);}
  }
.t_dark_red ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_yellow, :root.t_light .t_dark .t_yellow, .tm_xxt {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(46, 100%, 5%, 0);--background02:hsla(46, 100%, 5%, 0.2);--background04:hsla(46, 100%, 5%, 0.4);--background06:hsla(46, 100%, 5%, 0.6);--background08:hsla(46, 100%, 5%, 0.8);--color1:hsla(45, 100%, 5%, 1);--color2:hsla(46, 100%, 7%, 1);--color3:hsla(45, 100%, 9%, 1);--color4:hsla(45, 100%, 10%, 1);--color5:hsla(46, 100%, 12%, 1);--color6:hsla(49, 100%, 14%, 1);--color7:hsla(49, 89%, 18%, 1);--color8:hsla(50, 100%, 22%, 1);--color9:hsla(53, 92%, 50%, 1);--color10:hsla(54, 100%, 68%, 1);--color11:hsla(48, 100%, 47%, 1);--color12:hsla(53, 100%, 91%, 1);--color0:hsla(53, 100%, 91%, 0);--color02:hsla(53, 100%, 91%, 0.2);--color04:hsla(53, 100%, 91%, 0.4);--color06:hsla(53, 100%, 91%, 0.6);--color08:hsla(53, 100%, 91%, 0.8);--background:hsla(45, 100%, 5%, 1);--backgroundHover:hsla(46, 100%, 7%, 1);--backgroundPress:hsla(46, 100%, 5%, 0.8);--backgroundFocus:hsla(46, 100%, 5%, 0.8);--borderColor:hsla(45, 100%, 10%, 1);--borderColorHover:hsla(46, 100%, 12%, 1);--borderColorPress:hsla(45, 100%, 9%, 1);--borderColorFocus:hsla(45, 100%, 10%, 1);--color:hsla(53, 100%, 91%, 1);--colorHover:hsla(48, 100%, 47%, 1);--colorPress:hsla(53, 100%, 91%, 1);--colorFocus:hsla(48, 100%, 47%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(53, 100%, 91%, 0.2);--colorTransparent:hsla(53, 100%, 91%, 0);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_yellow {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(46, 100%, 5%, 0);--background02:hsla(46, 100%, 5%, 0.2);--background04:hsla(46, 100%, 5%, 0.4);--background06:hsla(46, 100%, 5%, 0.6);--background08:hsla(46, 100%, 5%, 0.8);--color1:hsla(45, 100%, 5%, 1);--color2:hsla(46, 100%, 7%, 1);--color3:hsla(45, 100%, 9%, 1);--color4:hsla(45, 100%, 10%, 1);--color5:hsla(46, 100%, 12%, 1);--color6:hsla(49, 100%, 14%, 1);--color7:hsla(49, 89%, 18%, 1);--color8:hsla(50, 100%, 22%, 1);--color9:hsla(53, 92%, 50%, 1);--color10:hsla(54, 100%, 68%, 1);--color11:hsla(48, 100%, 47%, 1);--color12:hsla(53, 100%, 91%, 1);--color0:hsla(53, 100%, 91%, 0);--color02:hsla(53, 100%, 91%, 0.2);--color04:hsla(53, 100%, 91%, 0.4);--color06:hsla(53, 100%, 91%, 0.6);--color08:hsla(53, 100%, 91%, 0.8);--background:hsla(45, 100%, 5%, 1);--backgroundHover:hsla(46, 100%, 7%, 1);--backgroundPress:hsla(46, 100%, 5%, 0.8);--backgroundFocus:hsla(46, 100%, 5%, 0.8);--borderColor:hsla(45, 100%, 10%, 1);--borderColorHover:hsla(46, 100%, 12%, 1);--borderColorPress:hsla(45, 100%, 9%, 1);--borderColorFocus:hsla(45, 100%, 10%, 1);--color:hsla(53, 100%, 91%, 1);--colorHover:hsla(48, 100%, 47%, 1);--colorPress:hsla(53, 100%, 91%, 1);--colorFocus:hsla(48, 100%, 47%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(53, 100%, 91%, 0.2);--colorTransparent:hsla(53, 100%, 91%, 0);}
  }
.t_dark_yellow ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_green, :root.t_light .t_dark .t_green, .tm_xxt {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(145, 33%, 7%, 0);--background02:hsla(145, 33%, 7%, 0.2);--background04:hsla(145, 33%, 7%, 0.4);--background06:hsla(145, 33%, 7%, 0.6);--background08:hsla(145, 33%, 7%, 0.8);--color1:hsla(145, 32%, 7%, 1);--color2:hsla(155, 44%, 8%, 1);--color3:hsla(155, 46%, 11%, 1);--color4:hsla(154, 48%, 13%, 1);--color5:hsla(155, 50%, 15%, 1);--color6:hsla(154, 51%, 18%, 1);--color7:hsla(153, 51%, 22%, 1);--color8:hsla(151, 52%, 28%, 1);--color9:hsla(151, 55%, 42%, 1);--color10:hsla(151, 49%, 46%, 1);--color11:hsla(151, 50%, 53%, 1);--color12:hsla(136, 73%, 94%, 1);--color0:hsla(134, 73%, 94%, 0);--color02:hsla(134, 73%, 94%, 0.2);--color04:hsla(134, 73%, 94%, 0.4);--color06:hsla(134, 73%, 94%, 0.6);--color08:hsla(134, 73%, 94%, 0.8);--background:hsla(145, 32%, 7%, 1);--backgroundHover:hsla(155, 44%, 8%, 1);--backgroundPress:hsla(145, 33%, 7%, 0.8);--backgroundFocus:hsla(145, 33%, 7%, 0.8);--borderColor:hsla(154, 48%, 13%, 1);--borderColorHover:hsla(155, 50%, 15%, 1);--borderColorPress:hsla(155, 46%, 11%, 1);--borderColorFocus:hsla(154, 48%, 13%, 1);--color:hsla(136, 73%, 94%, 1);--colorHover:hsla(151, 50%, 53%, 1);--colorPress:hsla(136, 73%, 94%, 1);--colorFocus:hsla(151, 50%, 53%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(134, 73%, 94%, 0.2);--colorTransparent:hsla(134, 73%, 94%, 0);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_green {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(145, 33%, 7%, 0);--background02:hsla(145, 33%, 7%, 0.2);--background04:hsla(145, 33%, 7%, 0.4);--background06:hsla(145, 33%, 7%, 0.6);--background08:hsla(145, 33%, 7%, 0.8);--color1:hsla(145, 32%, 7%, 1);--color2:hsla(155, 44%, 8%, 1);--color3:hsla(155, 46%, 11%, 1);--color4:hsla(154, 48%, 13%, 1);--color5:hsla(155, 50%, 15%, 1);--color6:hsla(154, 51%, 18%, 1);--color7:hsla(153, 51%, 22%, 1);--color8:hsla(151, 52%, 28%, 1);--color9:hsla(151, 55%, 42%, 1);--color10:hsla(151, 49%, 46%, 1);--color11:hsla(151, 50%, 53%, 1);--color12:hsla(136, 73%, 94%, 1);--color0:hsla(134, 73%, 94%, 0);--color02:hsla(134, 73%, 94%, 0.2);--color04:hsla(134, 73%, 94%, 0.4);--color06:hsla(134, 73%, 94%, 0.6);--color08:hsla(134, 73%, 94%, 0.8);--background:hsla(145, 32%, 7%, 1);--backgroundHover:hsla(155, 44%, 8%, 1);--backgroundPress:hsla(145, 33%, 7%, 0.8);--backgroundFocus:hsla(145, 33%, 7%, 0.8);--borderColor:hsla(154, 48%, 13%, 1);--borderColorHover:hsla(155, 50%, 15%, 1);--borderColorPress:hsla(155, 46%, 11%, 1);--borderColorFocus:hsla(154, 48%, 13%, 1);--color:hsla(136, 73%, 94%, 1);--colorHover:hsla(151, 50%, 53%, 1);--colorPress:hsla(136, 73%, 94%, 1);--colorFocus:hsla(151, 50%, 53%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(134, 73%, 94%, 0.2);--colorTransparent:hsla(134, 73%, 94%, 0);}
  }
.t_dark_green ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_Card, :root.t_dark .t_light .t_Input, :root.t_dark .t_light .t_ListItem, :root.t_dark .t_light .t_Progress, :root.t_dark .t_light .t_SelectTrigger, :root.t_dark .t_light .t_SliderTrack, :root.t_dark .t_light .t_TextArea, :root.t_dark .t_light .t_TooltipArrow, :root.t_dark .t_light .t_white_Card, :root.t_dark .t_light .t_white_Input, :root.t_dark .t_light .t_white_ListItem, :root.t_dark .t_light .t_white_Progress, :root.t_dark .t_light .t_white_SelectTrigger, :root.t_dark .t_light .t_white_SliderTrack, :root.t_dark .t_light .t_white_TextArea, :root.t_dark .t_light .t_white_TooltipArrow, :root.t_light .t_Card, :root.t_light .t_Input, :root.t_light .t_ListItem, :root.t_light .t_Progress, :root.t_light .t_SelectTrigger, :root.t_light .t_SliderTrack, :root.t_light .t_TextArea, :root.t_light .t_TooltipArrow, :root.t_light .t_white_Card, :root.t_light .t_white_Input, :root.t_light .t_white_ListItem, :root.t_light .t_white_Progress, :root.t_light .t_white_SelectTrigger, :root.t_light .t_white_SliderTrack, :root.t_light .t_white_TextArea, :root.t_light .t_white_TooltipArrow, .tm_xxt {--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--background:hsla(0, 0%, 95%, 1);--backgroundHover:hsla(0, 0%, 100%, 1);--backgroundPress:hsla(0, 0%, 93%, 1);--backgroundFocus:hsla(0, 0%, 93%, 1);--borderColor:hsla(0, 0%, 88%, 1);--borderColorHover:hsla(0, 0%, 91%, 1);--borderColorFocus:hsla(0, 0%, 88%, 1);--borderColorPress:hsla(0, 0%, 85%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_Card, .t_Input, .t_ListItem, .t_Progress, .t_SelectTrigger, .t_SliderTrack, .t_TextArea, .t_TooltipArrow, .t_white_Card, .t_white_Input, .t_white_ListItem, .t_white_Progress, .t_white_SelectTrigger, .t_white_SliderTrack, .t_white_TextArea, .t_white_TooltipArrow {--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--background:hsla(0, 0%, 95%, 1);--backgroundHover:hsla(0, 0%, 100%, 1);--backgroundPress:hsla(0, 0%, 93%, 1);--backgroundFocus:hsla(0, 0%, 93%, 1);--borderColor:hsla(0, 0%, 88%, 1);--borderColorHover:hsla(0, 0%, 91%, 1);--borderColorFocus:hsla(0, 0%, 88%, 1);--borderColorPress:hsla(0, 0%, 85%, 1);}
  }
:root.t_dark .t_light .t_Button, :root.t_dark .t_light .t_SliderTrackActive, :root.t_dark .t_light .t_white_Button, :root.t_dark .t_light .t_white_SliderTrackActive, :root.t_light .t_Button, :root.t_light .t_SliderTrackActive, :root.t_light .t_white_Button, :root.t_light .t_white_SliderTrackActive, .tm_xxt {--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--background:hsla(0, 0%, 91%, 1);--backgroundHover:hsla(0, 0%, 93%, 1);--backgroundPress:hsla(0, 0%, 88%, 1);--backgroundFocus:hsla(0, 0%, 88%, 1);--borderColor:hsla(0, 0%, 82%, 1);--borderColorHover:hsla(0, 0%, 85%, 1);--borderColorFocus:hsla(0, 0%, 82%, 1);--borderColorPress:hsla(0, 0%, 76%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_Button, .t_SliderTrackActive, .t_white_Button, .t_white_SliderTrackActive {--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--background:hsla(0, 0%, 91%, 1);--backgroundHover:hsla(0, 0%, 93%, 1);--backgroundPress:hsla(0, 0%, 88%, 1);--backgroundFocus:hsla(0, 0%, 88%, 1);--borderColor:hsla(0, 0%, 82%, 1);--borderColorHover:hsla(0, 0%, 85%, 1);--borderColorFocus:hsla(0, 0%, 82%, 1);--borderColorPress:hsla(0, 0%, 76%, 1);}
  }
:root.t_dark .t_light .t_Checkbox, :root.t_dark .t_light .t_RadioGroupItem, :root.t_dark .t_light .t_Switch, :root.t_dark .t_light .t_TooltipContent, :root.t_dark .t_light .t_white_Checkbox, :root.t_dark .t_light .t_white_RadioGroupItem, :root.t_dark .t_light .t_white_Switch, :root.t_dark .t_light .t_white_TooltipContent, :root.t_light .t_Checkbox, :root.t_light .t_RadioGroupItem, :root.t_light .t_Switch, :root.t_light .t_TooltipContent, :root.t_light .t_white_Checkbox, :root.t_light .t_white_RadioGroupItem, :root.t_light .t_white_Switch, :root.t_light .t_white_TooltipContent, .tm_xxt {--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--background:hsla(0, 0%, 93%, 1);--backgroundHover:hsla(0, 0%, 95%, 1);--backgroundPress:hsla(0, 0%, 91%, 1);--backgroundFocus:hsla(0, 0%, 91%, 1);--borderColor:hsla(0, 0%, 85%, 1);--borderColorHover:hsla(0, 0%, 88%, 1);--borderColorFocus:hsla(0, 0%, 85%, 1);--borderColorPress:hsla(0, 0%, 82%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_Checkbox, .t_RadioGroupItem, .t_Switch, .t_TooltipContent, .t_white_Checkbox, .t_white_RadioGroupItem, .t_white_Switch, .t_white_TooltipContent {--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--background:hsla(0, 0%, 93%, 1);--backgroundHover:hsla(0, 0%, 95%, 1);--backgroundPress:hsla(0, 0%, 91%, 1);--backgroundFocus:hsla(0, 0%, 91%, 1);--borderColor:hsla(0, 0%, 85%, 1);--borderColorHover:hsla(0, 0%, 88%, 1);--borderColorFocus:hsla(0, 0%, 85%, 1);--borderColorPress:hsla(0, 0%, 82%, 1);}
  }
:root.t_dark .t_light .t_ProgressIndicator, :root.t_dark .t_light .t_SliderThumb, :root.t_dark .t_light .t_SwitchThumb, :root.t_dark .t_light .t_Tooltip, :root.t_dark .t_light .t_white_ProgressIndicator, :root.t_dark .t_light .t_white_SliderThumb, :root.t_dark .t_light .t_white_SwitchThumb, :root.t_dark .t_light .t_white_Tooltip, :root.t_light .t_ProgressIndicator, :root.t_light .t_SliderThumb, :root.t_light .t_SwitchThumb, :root.t_light .t_Tooltip, :root.t_light .t_white_ProgressIndicator, :root.t_light .t_white_SliderThumb, :root.t_light .t_white_SwitchThumb, :root.t_light .t_white_Tooltip, .tm_xxt {--accentBackground:hsla(0, 0%, 38%, 1);--accentColor:hsla(0, 0%, 10%, 1);--background0:hsla(0, 0%, 9%, 0);--background02:hsla(0, 0%, 9%, 0.2);--background04:hsla(0, 0%, 9%, 0.4);--background06:hsla(0, 0%, 9%, 0.6);--background08:hsla(0, 0%, 9%, 0.8);--color1:hsla(0, 0%, 9%, 1);--color2:hsla(0, 0%, 42%, 1);--color3:hsla(0, 0%, 50%, 1);--color4:hsla(0, 0%, 56%, 1);--color5:hsla(0, 0%, 76%, 1);--color6:hsla(0, 0%, 82%, 1);--color7:hsla(0, 0%, 85%, 1);--color8:hsla(0, 0%, 88%, 1);--color9:hsla(0, 0%, 91%, 1);--color10:hsla(0, 0%, 93%, 1);--color11:hsla(0, 0%, 95%, 1);--color12:hsla(0, 0%, 100%, 1);--color0:hsla(0, 0%, 100%, 0);--color02:hsla(0, 0%, 100%, 0.2);--color04:hsla(0, 0%, 100%, 0.4);--color06:hsla(0, 0%, 100%, 0.6);--color08:hsla(0, 0%, 100%, 0.8);--background:hsla(0, 0%, 9%, 1);--backgroundHover:hsla(0, 0%, 9%, 0.8);--backgroundPress:hsla(0, 0%, 42%, 1);--backgroundFocus:hsla(0, 0%, 42%, 1);--borderColor:hsla(0, 0%, 56%, 1);--borderColorHover:hsla(0, 0%, 50%, 1);--borderColorPress:hsla(0, 0%, 76%, 1);--borderColorFocus:hsla(0, 0%, 56%, 1);--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 95%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 95%, 1);--placeholderColor:hsla(0, 0%, 91%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--colorTransparent:hsla(0, 0%, 100%, 0);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_ProgressIndicator, .t_SliderThumb, .t_SwitchThumb, .t_Tooltip, .t_white_ProgressIndicator, .t_white_SliderThumb, .t_white_SwitchThumb, .t_white_Tooltip {--accentBackground:hsla(0, 0%, 38%, 1);--accentColor:hsla(0, 0%, 10%, 1);--background0:hsla(0, 0%, 9%, 0);--background02:hsla(0, 0%, 9%, 0.2);--background04:hsla(0, 0%, 9%, 0.4);--background06:hsla(0, 0%, 9%, 0.6);--background08:hsla(0, 0%, 9%, 0.8);--color1:hsla(0, 0%, 9%, 1);--color2:hsla(0, 0%, 42%, 1);--color3:hsla(0, 0%, 50%, 1);--color4:hsla(0, 0%, 56%, 1);--color5:hsla(0, 0%, 76%, 1);--color6:hsla(0, 0%, 82%, 1);--color7:hsla(0, 0%, 85%, 1);--color8:hsla(0, 0%, 88%, 1);--color9:hsla(0, 0%, 91%, 1);--color10:hsla(0, 0%, 93%, 1);--color11:hsla(0, 0%, 95%, 1);--color12:hsla(0, 0%, 100%, 1);--color0:hsla(0, 0%, 100%, 0);--color02:hsla(0, 0%, 100%, 0.2);--color04:hsla(0, 0%, 100%, 0.4);--color06:hsla(0, 0%, 100%, 0.6);--color08:hsla(0, 0%, 100%, 0.8);--background:hsla(0, 0%, 9%, 1);--backgroundHover:hsla(0, 0%, 9%, 0.8);--backgroundPress:hsla(0, 0%, 42%, 1);--backgroundFocus:hsla(0, 0%, 42%, 1);--borderColor:hsla(0, 0%, 56%, 1);--borderColorHover:hsla(0, 0%, 50%, 1);--borderColorPress:hsla(0, 0%, 76%, 1);--borderColorFocus:hsla(0, 0%, 56%, 1);--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 95%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 95%, 1);--placeholderColor:hsla(0, 0%, 91%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--colorTransparent:hsla(0, 0%, 100%, 0);}
  }
.t_light_SwitchThumb ::selection, .t_light_SliderThumb ::selection, .t_light_Tooltip ::selection, .t_light_ProgressIndicator ::selection, .t_light_white_SwitchThumb ::selection, .t_light_white_SliderThumb ::selection, .t_light_white_Tooltip ::selection, .t_light_white_ProgressIndicator ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_Card, :root.t_dark .t_Input, :root.t_dark .t_ListItem, :root.t_dark .t_Progress, :root.t_dark .t_SelectTrigger, :root.t_dark .t_SliderTrack, :root.t_dark .t_TextArea, :root.t_dark .t_TooltipArrow, :root.t_dark .t_black_Card, :root.t_dark .t_black_Input, :root.t_dark .t_black_ListItem, :root.t_dark .t_black_Progress, :root.t_dark .t_black_SelectTrigger, :root.t_dark .t_black_SliderTrack, :root.t_dark .t_black_TextArea, :root.t_dark .t_black_TooltipArrow, :root.t_light .t_dark .t_Card, :root.t_light .t_dark .t_Input, :root.t_light .t_dark .t_ListItem, :root.t_light .t_dark .t_Progress, :root.t_light .t_dark .t_SelectTrigger, :root.t_light .t_dark .t_SliderTrack, :root.t_light .t_dark .t_TextArea, :root.t_light .t_dark .t_TooltipArrow, :root.t_light .t_dark .t_black_Card, :root.t_light .t_dark .t_black_Input, :root.t_light .t_dark .t_black_ListItem, :root.t_light .t_dark .t_black_Progress, :root.t_light .t_dark .t_black_SelectTrigger, :root.t_light .t_dark .t_black_SliderTrack, :root.t_light .t_dark .t_black_TextArea, :root.t_light .t_dark .t_black_TooltipArrow, .tm_xxt {--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--background:hsla(0, 0%, 8%, 1);--backgroundHover:hsla(0, 0%, 10%, 1);--backgroundPress:hsla(0, 0%, 2%, 1);--backgroundFocus:hsla(0, 0%, 2%, 1);--borderColor:hsla(0, 0%, 16%, 1);--borderColorHover:hsla(0, 0%, 20%, 1);--borderColorFocus:hsla(0, 0%, 16%, 1);--borderColorPress:hsla(0, 0%, 14%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_Card, .t_Input, .t_ListItem, .t_Progress, .t_SelectTrigger, .t_SliderTrack, .t_TextArea, .t_TooltipArrow, .t_black_Card, .t_black_Input, .t_black_ListItem, .t_black_Progress, .t_black_SelectTrigger, .t_black_SliderTrack, .t_black_TextArea, .t_black_TooltipArrow {--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--background:hsla(0, 0%, 8%, 1);--backgroundHover:hsla(0, 0%, 10%, 1);--backgroundPress:hsla(0, 0%, 2%, 1);--backgroundFocus:hsla(0, 0%, 2%, 1);--borderColor:hsla(0, 0%, 16%, 1);--borderColorHover:hsla(0, 0%, 20%, 1);--borderColorFocus:hsla(0, 0%, 16%, 1);--borderColorPress:hsla(0, 0%, 14%, 1);}
  }
:root.t_dark .t_Button, :root.t_dark .t_SliderTrackActive, :root.t_dark .t_black_Button, :root.t_dark .t_black_SliderTrackActive, :root.t_light .t_dark .t_Button, :root.t_light .t_dark .t_SliderTrackActive, :root.t_light .t_dark .t_black_Button, :root.t_light .t_dark .t_black_SliderTrackActive, .tm_xxt {--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--background:hsla(0, 0%, 14%, 1);--backgroundHover:hsla(0, 0%, 16%, 1);--backgroundPress:hsla(0, 0%, 10%, 1);--backgroundFocus:hsla(0, 0%, 10%, 1);--borderColor:hsla(0, 0%, 26%, 1);--borderColorHover:hsla(0, 0%, 29%, 1);--borderColorFocus:hsla(0, 0%, 26%, 1);--borderColorPress:hsla(0, 0%, 20%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_Button, .t_SliderTrackActive, .t_black_Button, .t_black_SliderTrackActive {--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--background:hsla(0, 0%, 14%, 1);--backgroundHover:hsla(0, 0%, 16%, 1);--backgroundPress:hsla(0, 0%, 10%, 1);--backgroundFocus:hsla(0, 0%, 10%, 1);--borderColor:hsla(0, 0%, 26%, 1);--borderColorHover:hsla(0, 0%, 29%, 1);--borderColorFocus:hsla(0, 0%, 26%, 1);--borderColorPress:hsla(0, 0%, 20%, 1);}
  }
:root.t_dark .t_Checkbox, :root.t_dark .t_RadioGroupItem, :root.t_dark .t_Switch, :root.t_dark .t_TooltipContent, :root.t_dark .t_black_Checkbox, :root.t_dark .t_black_RadioGroupItem, :root.t_dark .t_black_Switch, :root.t_dark .t_black_TooltipContent, :root.t_light .t_dark .t_Checkbox, :root.t_light .t_dark .t_RadioGroupItem, :root.t_light .t_dark .t_Switch, :root.t_light .t_dark .t_TooltipContent, :root.t_light .t_dark .t_black_Checkbox, :root.t_light .t_dark .t_black_RadioGroupItem, :root.t_light .t_dark .t_black_Switch, :root.t_light .t_dark .t_black_TooltipContent, .tm_xxt {--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--background:hsla(0, 0%, 10%, 1);--backgroundHover:hsla(0, 0%, 14%, 1);--backgroundPress:hsla(0, 0%, 8%, 1);--backgroundFocus:hsla(0, 0%, 8%, 1);--borderColor:hsla(0, 0%, 20%, 1);--borderColorHover:hsla(0, 0%, 26%, 1);--borderColorFocus:hsla(0, 0%, 20%, 1);--borderColorPress:hsla(0, 0%, 16%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_Checkbox, .t_RadioGroupItem, .t_Switch, .t_TooltipContent, .t_black_Checkbox, .t_black_RadioGroupItem, .t_black_Switch, .t_black_TooltipContent {--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--background:hsla(0, 0%, 10%, 1);--backgroundHover:hsla(0, 0%, 14%, 1);--backgroundPress:hsla(0, 0%, 8%, 1);--backgroundFocus:hsla(0, 0%, 8%, 1);--borderColor:hsla(0, 0%, 20%, 1);--borderColorHover:hsla(0, 0%, 26%, 1);--borderColorFocus:hsla(0, 0%, 20%, 1);--borderColorPress:hsla(0, 0%, 16%, 1);}
  }
:root.t_dark .t_ProgressIndicator, :root.t_dark .t_SliderThumb, :root.t_dark .t_SwitchThumb, :root.t_dark .t_Tooltip, :root.t_dark .t_black_ProgressIndicator, :root.t_dark .t_black_SliderThumb, :root.t_dark .t_black_SwitchThumb, :root.t_dark .t_black_Tooltip, :root.t_light .t_dark .t_ProgressIndicator, :root.t_light .t_dark .t_SliderThumb, :root.t_light .t_dark .t_SwitchThumb, :root.t_light .t_dark .t_Tooltip, :root.t_light .t_dark .t_black_ProgressIndicator, :root.t_light .t_dark .t_black_SliderThumb, :root.t_light .t_dark .t_black_SwitchThumb, :root.t_light .t_dark .t_black_Tooltip, .tm_xxt {--accentBackground:hsla(0, 0%, 93%, 1);--accentColor:hsla(0, 0%, 50%, 1);--background0:hsla(0, 0%, 100%, 0);--background02:hsla(0, 0%, 100%, 0.2);--background04:hsla(0, 0%, 100%, 0.4);--background06:hsla(0, 0%, 100%, 0.6);--background08:hsla(0, 0%, 100%, 0.8);--color1:hsla(0, 0%, 100%, 1);--color2:hsla(0, 0%, 65%, 1);--color3:hsla(0, 0%, 38%, 1);--color4:hsla(0, 0%, 33%, 1);--color5:hsla(0, 0%, 29%, 1);--color6:hsla(0, 0%, 26%, 1);--color7:hsla(0, 0%, 20%, 1);--color8:hsla(0, 0%, 16%, 1);--color9:hsla(0, 0%, 14%, 1);--color10:hsla(0, 0%, 10%, 1);--color11:hsla(0, 0%, 8%, 1);--color12:hsla(0, 0%, 2%, 1);--color0:hsla(0, 0%, 2%, 0);--color02:hsla(0, 0%, 2%, 0.2);--color04:hsla(0, 0%, 2%, 0.4);--color06:hsla(0, 0%, 2%, 0.6);--color08:hsla(0, 0%, 2%, 0.8);--background:hsla(0, 0%, 100%, 1);--backgroundHover:hsla(0, 0%, 65%, 1);--backgroundPress:hsla(0, 0%, 100%, 0.8);--backgroundFocus:hsla(0, 0%, 100%, 0.8);--borderColor:hsla(0, 0%, 33%, 1);--borderColorHover:hsla(0, 0%, 29%, 1);--borderColorPress:hsla(0, 0%, 38%, 1);--borderColorFocus:hsla(0, 0%, 33%, 1);--color:hsla(0, 0%, 2%, 1);--colorHover:hsla(0, 0%, 8%, 1);--colorPress:hsla(0, 0%, 2%, 1);--colorFocus:hsla(0, 0%, 8%, 1);--placeholderColor:hsla(0, 0%, 14%, 1);--outlineColor:hsla(0, 0%, 2%, 0.2);--colorTransparent:hsla(0, 0%, 2%, 0);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_ProgressIndicator, .t_SliderThumb, .t_SwitchThumb, .t_Tooltip, .t_black_ProgressIndicator, .t_black_SliderThumb, .t_black_SwitchThumb, .t_black_Tooltip {--accentBackground:hsla(0, 0%, 93%, 1);--accentColor:hsla(0, 0%, 50%, 1);--background0:hsla(0, 0%, 100%, 0);--background02:hsla(0, 0%, 100%, 0.2);--background04:hsla(0, 0%, 100%, 0.4);--background06:hsla(0, 0%, 100%, 0.6);--background08:hsla(0, 0%, 100%, 0.8);--color1:hsla(0, 0%, 100%, 1);--color2:hsla(0, 0%, 65%, 1);--color3:hsla(0, 0%, 38%, 1);--color4:hsla(0, 0%, 33%, 1);--color5:hsla(0, 0%, 29%, 1);--color6:hsla(0, 0%, 26%, 1);--color7:hsla(0, 0%, 20%, 1);--color8:hsla(0, 0%, 16%, 1);--color9:hsla(0, 0%, 14%, 1);--color10:hsla(0, 0%, 10%, 1);--color11:hsla(0, 0%, 8%, 1);--color12:hsla(0, 0%, 2%, 1);--color0:hsla(0, 0%, 2%, 0);--color02:hsla(0, 0%, 2%, 0.2);--color04:hsla(0, 0%, 2%, 0.4);--color06:hsla(0, 0%, 2%, 0.6);--color08:hsla(0, 0%, 2%, 0.8);--background:hsla(0, 0%, 100%, 1);--backgroundHover:hsla(0, 0%, 65%, 1);--backgroundPress:hsla(0, 0%, 100%, 0.8);--backgroundFocus:hsla(0, 0%, 100%, 0.8);--borderColor:hsla(0, 0%, 33%, 1);--borderColorHover:hsla(0, 0%, 29%, 1);--borderColorPress:hsla(0, 0%, 38%, 1);--borderColorFocus:hsla(0, 0%, 33%, 1);--color:hsla(0, 0%, 2%, 1);--colorHover:hsla(0, 0%, 8%, 1);--colorPress:hsla(0, 0%, 2%, 1);--colorFocus:hsla(0, 0%, 8%, 1);--placeholderColor:hsla(0, 0%, 14%, 1);--outlineColor:hsla(0, 0%, 2%, 0.2);--colorTransparent:hsla(0, 0%, 2%, 0);}
  }
.t_dark_SwitchThumb ::selection, .t_dark_SliderThumb ::selection, .t_dark_Tooltip ::selection, .t_dark_ProgressIndicator ::selection, .t_dark_black_SwitchThumb ::selection, .t_dark_black_SliderThumb ::selection, .t_dark_black_Tooltip ::selection, .t_dark_black_ProgressIndicator ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_accent_Card, :root.t_dark .t_light .t_accent_Input, :root.t_dark .t_light .t_accent_ListItem, :root.t_dark .t_light .t_accent_Progress, :root.t_dark .t_light .t_accent_SelectTrigger, :root.t_dark .t_light .t_accent_SliderTrack, :root.t_dark .t_light .t_accent_TextArea, :root.t_dark .t_light .t_accent_TooltipArrow, :root.t_dark .t_light .t_black_Card, :root.t_dark .t_light .t_black_Input, :root.t_dark .t_light .t_black_ListItem, :root.t_dark .t_light .t_black_Progress, :root.t_dark .t_light .t_black_SelectTrigger, :root.t_dark .t_light .t_black_SliderTrack, :root.t_dark .t_light .t_black_TextArea, :root.t_dark .t_light .t_black_TooltipArrow, :root.t_light .t_accent_Card, :root.t_light .t_accent_Input, :root.t_light .t_accent_ListItem, :root.t_light .t_accent_Progress, :root.t_light .t_accent_SelectTrigger, :root.t_light .t_accent_SliderTrack, :root.t_light .t_accent_TextArea, :root.t_light .t_accent_TooltipArrow, :root.t_light .t_black_Card, :root.t_light .t_black_Input, :root.t_light .t_black_ListItem, :root.t_light .t_black_Progress, :root.t_light .t_black_SelectTrigger, :root.t_light .t_black_SliderTrack, :root.t_light .t_black_TextArea, :root.t_light .t_black_TooltipArrow, .tm_xxt {--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--background:hsla(0, 0%, 8%, 1);--backgroundHover:hsla(0, 0%, 2%, 1);--backgroundPress:hsla(0, 0%, 10%, 1);--backgroundFocus:hsla(0, 0%, 10%, 1);--borderColor:hsla(0, 0%, 16%, 1);--borderColorHover:hsla(0, 0%, 14%, 1);--borderColorFocus:hsla(0, 0%, 16%, 1);--borderColorPress:hsla(0, 0%, 20%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_accent_Card, .t_accent_Input, .t_accent_ListItem, .t_accent_Progress, .t_accent_SelectTrigger, .t_accent_SliderTrack, .t_accent_TextArea, .t_accent_TooltipArrow, .t_black_Card, .t_black_Input, .t_black_ListItem, .t_black_Progress, .t_black_SelectTrigger, .t_black_SliderTrack, .t_black_TextArea, .t_black_TooltipArrow {--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--background:hsla(0, 0%, 8%, 1);--backgroundHover:hsla(0, 0%, 2%, 1);--backgroundPress:hsla(0, 0%, 10%, 1);--backgroundFocus:hsla(0, 0%, 10%, 1);--borderColor:hsla(0, 0%, 16%, 1);--borderColorHover:hsla(0, 0%, 14%, 1);--borderColorFocus:hsla(0, 0%, 16%, 1);--borderColorPress:hsla(0, 0%, 20%, 1);}
  }
:root.t_dark .t_light .t_accent_Button, :root.t_dark .t_light .t_accent_SliderTrackActive, :root.t_dark .t_light .t_black_Button, :root.t_dark .t_light .t_black_SliderTrackActive, :root.t_light .t_accent_Button, :root.t_light .t_accent_SliderTrackActive, :root.t_light .t_black_Button, :root.t_light .t_black_SliderTrackActive, .tm_xxt {--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--background:hsla(0, 0%, 14%, 1);--backgroundHover:hsla(0, 0%, 10%, 1);--backgroundPress:hsla(0, 0%, 16%, 1);--backgroundFocus:hsla(0, 0%, 16%, 1);--borderColor:hsla(0, 0%, 26%, 1);--borderColorHover:hsla(0, 0%, 20%, 1);--borderColorFocus:hsla(0, 0%, 26%, 1);--borderColorPress:hsla(0, 0%, 29%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_accent_Button, .t_accent_SliderTrackActive, .t_black_Button, .t_black_SliderTrackActive {--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--background:hsla(0, 0%, 14%, 1);--backgroundHover:hsla(0, 0%, 10%, 1);--backgroundPress:hsla(0, 0%, 16%, 1);--backgroundFocus:hsla(0, 0%, 16%, 1);--borderColor:hsla(0, 0%, 26%, 1);--borderColorHover:hsla(0, 0%, 20%, 1);--borderColorFocus:hsla(0, 0%, 26%, 1);--borderColorPress:hsla(0, 0%, 29%, 1);}
  }
:root.t_dark .t_light .t_accent_Checkbox, :root.t_dark .t_light .t_accent_RadioGroupItem, :root.t_dark .t_light .t_accent_Switch, :root.t_dark .t_light .t_accent_TooltipContent, :root.t_dark .t_light .t_black_Checkbox, :root.t_dark .t_light .t_black_RadioGroupItem, :root.t_dark .t_light .t_black_Switch, :root.t_dark .t_light .t_black_TooltipContent, :root.t_light .t_accent_Checkbox, :root.t_light .t_accent_RadioGroupItem, :root.t_light .t_accent_Switch, :root.t_light .t_accent_TooltipContent, :root.t_light .t_black_Checkbox, :root.t_light .t_black_RadioGroupItem, :root.t_light .t_black_Switch, :root.t_light .t_black_TooltipContent, .tm_xxt {--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--background:hsla(0, 0%, 10%, 1);--backgroundHover:hsla(0, 0%, 8%, 1);--backgroundPress:hsla(0, 0%, 14%, 1);--backgroundFocus:hsla(0, 0%, 14%, 1);--borderColor:hsla(0, 0%, 20%, 1);--borderColorHover:hsla(0, 0%, 16%, 1);--borderColorFocus:hsla(0, 0%, 20%, 1);--borderColorPress:hsla(0, 0%, 26%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_accent_Checkbox, .t_accent_RadioGroupItem, .t_accent_Switch, .t_accent_TooltipContent, .t_black_Checkbox, .t_black_RadioGroupItem, .t_black_Switch, .t_black_TooltipContent {--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 65%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 65%, 1);--placeholderColor:hsla(0, 0%, 33%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--background:hsla(0, 0%, 10%, 1);--backgroundHover:hsla(0, 0%, 8%, 1);--backgroundPress:hsla(0, 0%, 14%, 1);--backgroundFocus:hsla(0, 0%, 14%, 1);--borderColor:hsla(0, 0%, 20%, 1);--borderColorHover:hsla(0, 0%, 16%, 1);--borderColorFocus:hsla(0, 0%, 20%, 1);--borderColorPress:hsla(0, 0%, 26%, 1);}
  }
:root.t_dark .t_light .t_accent_ProgressIndicator, :root.t_dark .t_light .t_accent_SliderThumb, :root.t_dark .t_light .t_accent_SwitchThumb, :root.t_dark .t_light .t_accent_Tooltip, :root.t_light .t_accent_ProgressIndicator, :root.t_light .t_accent_SliderThumb, :root.t_light .t_accent_SwitchThumb, :root.t_light .t_accent_Tooltip, .tm_xxt {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(0, 0%, 100%, 0);--background02:hsla(0, 0%, 100%, 0.2);--background04:hsla(0, 0%, 100%, 0.4);--background06:hsla(0, 0%, 100%, 0.6);--background08:hsla(0, 0%, 100%, 0.8);--color1:hsla(0, 0%, 100%, 1);--color2:hsla(0, 0%, 65%, 1);--color3:hsla(0, 0%, 38%, 1);--color4:hsla(0, 0%, 33%, 1);--color5:hsla(0, 0%, 29%, 1);--color6:hsla(0, 0%, 26%, 1);--color7:hsla(0, 0%, 20%, 1);--color8:hsla(0, 0%, 16%, 1);--color9:hsla(0, 0%, 14%, 1);--color10:hsla(0, 0%, 10%, 1);--color11:hsla(0, 0%, 8%, 1);--color12:hsla(0, 0%, 2%, 1);--color0:hsla(0, 0%, 2%, 0);--color02:hsla(0, 0%, 2%, 0.2);--color04:hsla(0, 0%, 2%, 0.4);--color06:hsla(0, 0%, 2%, 0.6);--color08:hsla(0, 0%, 2%, 0.8);--background:hsla(0, 0%, 100%, 1);--backgroundHover:hsla(0, 0%, 100%, 0.8);--backgroundPress:hsla(0, 0%, 65%, 1);--backgroundFocus:hsla(0, 0%, 65%, 1);--borderColor:hsla(0, 0%, 33%, 1);--borderColorHover:hsla(0, 0%, 38%, 1);--borderColorPress:hsla(0, 0%, 29%, 1);--borderColorFocus:hsla(0, 0%, 33%, 1);--color:hsla(0, 0%, 2%, 1);--colorHover:hsla(0, 0%, 8%, 1);--colorPress:hsla(0, 0%, 2%, 1);--colorFocus:hsla(0, 0%, 8%, 1);--placeholderColor:hsla(0, 0%, 14%, 1);--outlineColor:hsla(0, 0%, 2%, 0.2);--colorTransparent:hsla(0, 0%, 2%, 0);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_accent_ProgressIndicator, .t_accent_SliderThumb, .t_accent_SwitchThumb, .t_accent_Tooltip {--accentBackground:hsla(0, 0%, 50%, 1);--accentColor:hsla(0, 0%, 93%, 1);--background0:hsla(0, 0%, 100%, 0);--background02:hsla(0, 0%, 100%, 0.2);--background04:hsla(0, 0%, 100%, 0.4);--background06:hsla(0, 0%, 100%, 0.6);--background08:hsla(0, 0%, 100%, 0.8);--color1:hsla(0, 0%, 100%, 1);--color2:hsla(0, 0%, 65%, 1);--color3:hsla(0, 0%, 38%, 1);--color4:hsla(0, 0%, 33%, 1);--color5:hsla(0, 0%, 29%, 1);--color6:hsla(0, 0%, 26%, 1);--color7:hsla(0, 0%, 20%, 1);--color8:hsla(0, 0%, 16%, 1);--color9:hsla(0, 0%, 14%, 1);--color10:hsla(0, 0%, 10%, 1);--color11:hsla(0, 0%, 8%, 1);--color12:hsla(0, 0%, 2%, 1);--color0:hsla(0, 0%, 2%, 0);--color02:hsla(0, 0%, 2%, 0.2);--color04:hsla(0, 0%, 2%, 0.4);--color06:hsla(0, 0%, 2%, 0.6);--color08:hsla(0, 0%, 2%, 0.8);--background:hsla(0, 0%, 100%, 1);--backgroundHover:hsla(0, 0%, 100%, 0.8);--backgroundPress:hsla(0, 0%, 65%, 1);--backgroundFocus:hsla(0, 0%, 65%, 1);--borderColor:hsla(0, 0%, 33%, 1);--borderColorHover:hsla(0, 0%, 38%, 1);--borderColorPress:hsla(0, 0%, 29%, 1);--borderColorFocus:hsla(0, 0%, 33%, 1);--color:hsla(0, 0%, 2%, 1);--colorHover:hsla(0, 0%, 8%, 1);--colorPress:hsla(0, 0%, 2%, 1);--colorFocus:hsla(0, 0%, 8%, 1);--placeholderColor:hsla(0, 0%, 14%, 1);--outlineColor:hsla(0, 0%, 2%, 0.2);--colorTransparent:hsla(0, 0%, 2%, 0);}
  }
.t_light_accent_SwitchThumb ::selection, .t_light_accent_SliderThumb ::selection, .t_light_accent_Tooltip ::selection, .t_light_accent_ProgressIndicator ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_accent_Card, :root.t_dark .t_accent_Input, :root.t_dark .t_accent_ListItem, :root.t_dark .t_accent_Progress, :root.t_dark .t_accent_SelectTrigger, :root.t_dark .t_accent_SliderTrack, :root.t_dark .t_accent_TextArea, :root.t_dark .t_accent_TooltipArrow, :root.t_dark .t_white_Card, :root.t_dark .t_white_Input, :root.t_dark .t_white_ListItem, :root.t_dark .t_white_Progress, :root.t_dark .t_white_SelectTrigger, :root.t_dark .t_white_SliderTrack, :root.t_dark .t_white_TextArea, :root.t_dark .t_white_TooltipArrow, :root.t_light .t_dark .t_accent_Card, :root.t_light .t_dark .t_accent_Input, :root.t_light .t_dark .t_accent_ListItem, :root.t_light .t_dark .t_accent_Progress, :root.t_light .t_dark .t_accent_SelectTrigger, :root.t_light .t_dark .t_accent_SliderTrack, :root.t_light .t_dark .t_accent_TextArea, :root.t_light .t_dark .t_accent_TooltipArrow, :root.t_light .t_dark .t_white_Card, :root.t_light .t_dark .t_white_Input, :root.t_light .t_dark .t_white_ListItem, :root.t_light .t_dark .t_white_Progress, :root.t_light .t_dark .t_white_SelectTrigger, :root.t_light .t_dark .t_white_SliderTrack, :root.t_light .t_dark .t_white_TextArea, :root.t_light .t_dark .t_white_TooltipArrow, .tm_xxt {--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--background:hsla(0, 0%, 95%, 1);--backgroundHover:hsla(0, 0%, 93%, 1);--backgroundPress:hsla(0, 0%, 100%, 1);--backgroundFocus:hsla(0, 0%, 100%, 1);--borderColor:hsla(0, 0%, 88%, 1);--borderColorHover:hsla(0, 0%, 85%, 1);--borderColorFocus:hsla(0, 0%, 88%, 1);--borderColorPress:hsla(0, 0%, 91%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_accent_Card, .t_accent_Input, .t_accent_ListItem, .t_accent_Progress, .t_accent_SelectTrigger, .t_accent_SliderTrack, .t_accent_TextArea, .t_accent_TooltipArrow, .t_white_Card, .t_white_Input, .t_white_ListItem, .t_white_Progress, .t_white_SelectTrigger, .t_white_SliderTrack, .t_white_TextArea, .t_white_TooltipArrow {--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--background:hsla(0, 0%, 95%, 1);--backgroundHover:hsla(0, 0%, 93%, 1);--backgroundPress:hsla(0, 0%, 100%, 1);--backgroundFocus:hsla(0, 0%, 100%, 1);--borderColor:hsla(0, 0%, 88%, 1);--borderColorHover:hsla(0, 0%, 85%, 1);--borderColorFocus:hsla(0, 0%, 88%, 1);--borderColorPress:hsla(0, 0%, 91%, 1);}
  }
:root.t_dark .t_accent_Button, :root.t_dark .t_accent_SliderTrackActive, :root.t_dark .t_white_Button, :root.t_dark .t_white_SliderTrackActive, :root.t_light .t_dark .t_accent_Button, :root.t_light .t_dark .t_accent_SliderTrackActive, :root.t_light .t_dark .t_white_Button, :root.t_light .t_dark .t_white_SliderTrackActive, .tm_xxt {--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--background:hsla(0, 0%, 91%, 1);--backgroundHover:hsla(0, 0%, 88%, 1);--backgroundPress:hsla(0, 0%, 93%, 1);--backgroundFocus:hsla(0, 0%, 93%, 1);--borderColor:hsla(0, 0%, 82%, 1);--borderColorHover:hsla(0, 0%, 76%, 1);--borderColorFocus:hsla(0, 0%, 82%, 1);--borderColorPress:hsla(0, 0%, 85%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_accent_Button, .t_accent_SliderTrackActive, .t_white_Button, .t_white_SliderTrackActive {--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--background:hsla(0, 0%, 91%, 1);--backgroundHover:hsla(0, 0%, 88%, 1);--backgroundPress:hsla(0, 0%, 93%, 1);--backgroundFocus:hsla(0, 0%, 93%, 1);--borderColor:hsla(0, 0%, 82%, 1);--borderColorHover:hsla(0, 0%, 76%, 1);--borderColorFocus:hsla(0, 0%, 82%, 1);--borderColorPress:hsla(0, 0%, 85%, 1);}
  }
:root.t_dark .t_accent_Checkbox, :root.t_dark .t_accent_RadioGroupItem, :root.t_dark .t_accent_Switch, :root.t_dark .t_accent_TooltipContent, :root.t_dark .t_white_Checkbox, :root.t_dark .t_white_RadioGroupItem, :root.t_dark .t_white_Switch, :root.t_dark .t_white_TooltipContent, :root.t_light .t_dark .t_accent_Checkbox, :root.t_light .t_dark .t_accent_RadioGroupItem, :root.t_light .t_dark .t_accent_Switch, :root.t_light .t_dark .t_accent_TooltipContent, :root.t_light .t_dark .t_white_Checkbox, :root.t_light .t_dark .t_white_RadioGroupItem, :root.t_light .t_dark .t_white_Switch, :root.t_light .t_dark .t_white_TooltipContent, .tm_xxt {--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--background:hsla(0, 0%, 93%, 1);--backgroundHover:hsla(0, 0%, 91%, 1);--backgroundPress:hsla(0, 0%, 95%, 1);--backgroundFocus:hsla(0, 0%, 95%, 1);--borderColor:hsla(0, 0%, 85%, 1);--borderColorHover:hsla(0, 0%, 82%, 1);--borderColorFocus:hsla(0, 0%, 85%, 1);--borderColorPress:hsla(0, 0%, 88%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_accent_Checkbox, .t_accent_RadioGroupItem, .t_accent_Switch, .t_accent_TooltipContent, .t_white_Checkbox, .t_white_RadioGroupItem, .t_white_Switch, .t_white_TooltipContent {--color:hsla(0, 0%, 9%, 1);--colorHover:hsla(0, 0%, 42%, 1);--colorPress:hsla(0, 0%, 9%, 1);--colorFocus:hsla(0, 0%, 42%, 1);--placeholderColor:hsla(0, 0%, 56%, 1);--outlineColor:hsla(0, 0%, 9%, 0.2);--background:hsla(0, 0%, 93%, 1);--backgroundHover:hsla(0, 0%, 91%, 1);--backgroundPress:hsla(0, 0%, 95%, 1);--backgroundFocus:hsla(0, 0%, 95%, 1);--borderColor:hsla(0, 0%, 85%, 1);--borderColorHover:hsla(0, 0%, 82%, 1);--borderColorFocus:hsla(0, 0%, 85%, 1);--borderColorPress:hsla(0, 0%, 88%, 1);}
  }
:root.t_dark .t_accent_ProgressIndicator, :root.t_dark .t_accent_SliderThumb, :root.t_dark .t_accent_SwitchThumb, :root.t_dark .t_accent_Tooltip, :root.t_light .t_dark .t_accent_ProgressIndicator, :root.t_light .t_dark .t_accent_SliderThumb, :root.t_light .t_dark .t_accent_SwitchThumb, :root.t_light .t_dark .t_accent_Tooltip, .tm_xxt {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(0, 0%, 9%, 0);--background02:hsla(0, 0%, 9%, 0.2);--background04:hsla(0, 0%, 9%, 0.4);--background06:hsla(0, 0%, 9%, 0.6);--background08:hsla(0, 0%, 9%, 0.8);--color1:hsla(0, 0%, 9%, 1);--color2:hsla(0, 0%, 42%, 1);--color3:hsla(0, 0%, 50%, 1);--color4:hsla(0, 0%, 56%, 1);--color5:hsla(0, 0%, 76%, 1);--color6:hsla(0, 0%, 82%, 1);--color7:hsla(0, 0%, 85%, 1);--color8:hsla(0, 0%, 88%, 1);--color9:hsla(0, 0%, 91%, 1);--color10:hsla(0, 0%, 93%, 1);--color11:hsla(0, 0%, 95%, 1);--color12:hsla(0, 0%, 100%, 1);--color0:hsla(0, 0%, 100%, 0);--color02:hsla(0, 0%, 100%, 0.2);--color04:hsla(0, 0%, 100%, 0.4);--color06:hsla(0, 0%, 100%, 0.6);--color08:hsla(0, 0%, 100%, 0.8);--background:hsla(0, 0%, 9%, 1);--backgroundHover:hsla(0, 0%, 42%, 1);--backgroundPress:hsla(0, 0%, 9%, 0.8);--backgroundFocus:hsla(0, 0%, 9%, 0.8);--borderColor:hsla(0, 0%, 56%, 1);--borderColorHover:hsla(0, 0%, 76%, 1);--borderColorPress:hsla(0, 0%, 50%, 1);--borderColorFocus:hsla(0, 0%, 56%, 1);--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 95%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 95%, 1);--placeholderColor:hsla(0, 0%, 91%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--colorTransparent:hsla(0, 0%, 100%, 0);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_accent_ProgressIndicator, .t_accent_SliderThumb, .t_accent_SwitchThumb, .t_accent_Tooltip {--accentBackground:hsla(0, 0%, 10%, 1);--accentColor:hsla(0, 0%, 38%, 1);--background0:hsla(0, 0%, 9%, 0);--background02:hsla(0, 0%, 9%, 0.2);--background04:hsla(0, 0%, 9%, 0.4);--background06:hsla(0, 0%, 9%, 0.6);--background08:hsla(0, 0%, 9%, 0.8);--color1:hsla(0, 0%, 9%, 1);--color2:hsla(0, 0%, 42%, 1);--color3:hsla(0, 0%, 50%, 1);--color4:hsla(0, 0%, 56%, 1);--color5:hsla(0, 0%, 76%, 1);--color6:hsla(0, 0%, 82%, 1);--color7:hsla(0, 0%, 85%, 1);--color8:hsla(0, 0%, 88%, 1);--color9:hsla(0, 0%, 91%, 1);--color10:hsla(0, 0%, 93%, 1);--color11:hsla(0, 0%, 95%, 1);--color12:hsla(0, 0%, 100%, 1);--color0:hsla(0, 0%, 100%, 0);--color02:hsla(0, 0%, 100%, 0.2);--color04:hsla(0, 0%, 100%, 0.4);--color06:hsla(0, 0%, 100%, 0.6);--color08:hsla(0, 0%, 100%, 0.8);--background:hsla(0, 0%, 9%, 1);--backgroundHover:hsla(0, 0%, 42%, 1);--backgroundPress:hsla(0, 0%, 9%, 0.8);--backgroundFocus:hsla(0, 0%, 9%, 0.8);--borderColor:hsla(0, 0%, 56%, 1);--borderColorHover:hsla(0, 0%, 76%, 1);--borderColorPress:hsla(0, 0%, 50%, 1);--borderColorFocus:hsla(0, 0%, 56%, 1);--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 95%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 95%, 1);--placeholderColor:hsla(0, 0%, 91%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--colorTransparent:hsla(0, 0%, 100%, 0);}
  }
.t_dark_accent_SwitchThumb ::selection, .t_dark_accent_SliderThumb ::selection, .t_dark_accent_Tooltip ::selection, .t_dark_accent_ProgressIndicator ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_black_ProgressIndicator, :root.t_dark .t_light .t_black_SliderThumb, :root.t_dark .t_light .t_black_SwitchThumb, :root.t_dark .t_light .t_black_Tooltip, :root.t_light .t_black_ProgressIndicator, :root.t_light .t_black_SliderThumb, :root.t_light .t_black_SwitchThumb, :root.t_light .t_black_Tooltip, .tm_xxt {--accentBackground:hsla(0, 0%, 38%, 1);--accentColor:hsla(0, 0%, 10%, 1);--background0:hsla(0, 0%, 100%, 0);--background02:hsla(0, 0%, 100%, 0.2);--background04:hsla(0, 0%, 100%, 0.4);--background06:hsla(0, 0%, 100%, 0.6);--background08:hsla(0, 0%, 100%, 0.8);--color1:hsla(0, 0%, 100%, 1);--color2:hsla(0, 0%, 65%, 1);--color3:hsla(0, 0%, 38%, 1);--color4:hsla(0, 0%, 33%, 1);--color5:hsla(0, 0%, 29%, 1);--color6:hsla(0, 0%, 26%, 1);--color7:hsla(0, 0%, 20%, 1);--color8:hsla(0, 0%, 16%, 1);--color9:hsla(0, 0%, 14%, 1);--color10:hsla(0, 0%, 10%, 1);--color11:hsla(0, 0%, 8%, 1);--color12:hsla(0, 0%, 2%, 1);--color0:hsla(0, 0%, 2%, 0);--color02:hsla(0, 0%, 2%, 0.2);--color04:hsla(0, 0%, 2%, 0.4);--color06:hsla(0, 0%, 2%, 0.6);--color08:hsla(0, 0%, 2%, 0.8);--background:hsla(0, 0%, 100%, 1);--backgroundHover:hsla(0, 0%, 100%, 0.8);--backgroundPress:hsla(0, 0%, 65%, 1);--backgroundFocus:hsla(0, 0%, 65%, 1);--borderColor:hsla(0, 0%, 33%, 1);--borderColorHover:hsla(0, 0%, 38%, 1);--borderColorPress:hsla(0, 0%, 29%, 1);--borderColorFocus:hsla(0, 0%, 33%, 1);--color:hsla(0, 0%, 2%, 1);--colorHover:hsla(0, 0%, 8%, 1);--colorPress:hsla(0, 0%, 2%, 1);--colorFocus:hsla(0, 0%, 8%, 1);--placeholderColor:hsla(0, 0%, 14%, 1);--outlineColor:hsla(0, 0%, 2%, 0.2);--colorTransparent:hsla(0, 0%, 2%, 0);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_black_ProgressIndicator, .t_black_SliderThumb, .t_black_SwitchThumb, .t_black_Tooltip {--accentBackground:hsla(0, 0%, 38%, 1);--accentColor:hsla(0, 0%, 10%, 1);--background0:hsla(0, 0%, 100%, 0);--background02:hsla(0, 0%, 100%, 0.2);--background04:hsla(0, 0%, 100%, 0.4);--background06:hsla(0, 0%, 100%, 0.6);--background08:hsla(0, 0%, 100%, 0.8);--color1:hsla(0, 0%, 100%, 1);--color2:hsla(0, 0%, 65%, 1);--color3:hsla(0, 0%, 38%, 1);--color4:hsla(0, 0%, 33%, 1);--color5:hsla(0, 0%, 29%, 1);--color6:hsla(0, 0%, 26%, 1);--color7:hsla(0, 0%, 20%, 1);--color8:hsla(0, 0%, 16%, 1);--color9:hsla(0, 0%, 14%, 1);--color10:hsla(0, 0%, 10%, 1);--color11:hsla(0, 0%, 8%, 1);--color12:hsla(0, 0%, 2%, 1);--color0:hsla(0, 0%, 2%, 0);--color02:hsla(0, 0%, 2%, 0.2);--color04:hsla(0, 0%, 2%, 0.4);--color06:hsla(0, 0%, 2%, 0.6);--color08:hsla(0, 0%, 2%, 0.8);--background:hsla(0, 0%, 100%, 1);--backgroundHover:hsla(0, 0%, 100%, 0.8);--backgroundPress:hsla(0, 0%, 65%, 1);--backgroundFocus:hsla(0, 0%, 65%, 1);--borderColor:hsla(0, 0%, 33%, 1);--borderColorHover:hsla(0, 0%, 38%, 1);--borderColorPress:hsla(0, 0%, 29%, 1);--borderColorFocus:hsla(0, 0%, 33%, 1);--color:hsla(0, 0%, 2%, 1);--colorHover:hsla(0, 0%, 8%, 1);--colorPress:hsla(0, 0%, 2%, 1);--colorFocus:hsla(0, 0%, 8%, 1);--placeholderColor:hsla(0, 0%, 14%, 1);--outlineColor:hsla(0, 0%, 2%, 0.2);--colorTransparent:hsla(0, 0%, 2%, 0);}
  }
.t_light_black_SwitchThumb ::selection, .t_light_black_SliderThumb ::selection, .t_light_black_Tooltip ::selection, .t_light_black_ProgressIndicator ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_blue_Card, :root.t_dark .t_light .t_blue_Input, :root.t_dark .t_light .t_blue_ListItem, :root.t_dark .t_light .t_blue_Progress, :root.t_dark .t_light .t_blue_SelectTrigger, :root.t_dark .t_light .t_blue_SliderTrack, :root.t_dark .t_light .t_blue_TextArea, :root.t_dark .t_light .t_blue_TooltipArrow, :root.t_light .t_blue_Card, :root.t_light .t_blue_Input, :root.t_light .t_blue_ListItem, :root.t_light .t_blue_Progress, :root.t_light .t_blue_SelectTrigger, :root.t_light .t_blue_SliderTrack, :root.t_light .t_blue_TextArea, :root.t_light .t_blue_TooltipArrow, .tm_xxt {--color:hsla(211, 100%, 15%, 1);--colorHover:hsla(211, 100%, 43%, 1);--colorPress:hsla(211, 100%, 15%, 1);--colorFocus:hsla(211, 100%, 43%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(211, 100%, 15%, 0.2);--background:hsla(210, 100%, 98%, 1);--backgroundHover:hsla(210, 100%, 99%, 1);--backgroundPress:hsla(210, 100%, 96%, 1);--backgroundFocus:hsla(210, 100%, 96%, 1);--borderColor:hsla(209, 96%, 90%, 1);--borderColorHover:hsla(210, 100%, 94%, 1);--borderColorFocus:hsla(209, 96%, 90%, 1);--borderColorPress:hsla(209, 82%, 85%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_blue_Card, .t_blue_Input, .t_blue_ListItem, .t_blue_Progress, .t_blue_SelectTrigger, .t_blue_SliderTrack, .t_blue_TextArea, .t_blue_TooltipArrow {--color:hsla(211, 100%, 15%, 1);--colorHover:hsla(211, 100%, 43%, 1);--colorPress:hsla(211, 100%, 15%, 1);--colorFocus:hsla(211, 100%, 43%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(211, 100%, 15%, 0.2);--background:hsla(210, 100%, 98%, 1);--backgroundHover:hsla(210, 100%, 99%, 1);--backgroundPress:hsla(210, 100%, 96%, 1);--backgroundFocus:hsla(210, 100%, 96%, 1);--borderColor:hsla(209, 96%, 90%, 1);--borderColorHover:hsla(210, 100%, 94%, 1);--borderColorFocus:hsla(209, 96%, 90%, 1);--borderColorPress:hsla(209, 82%, 85%, 1);}
  }
:root.t_dark .t_light .t_blue_Button, :root.t_dark .t_light .t_blue_SliderTrackActive, :root.t_light .t_blue_Button, :root.t_light .t_blue_SliderTrackActive, .tm_xxt {--color:hsla(211, 100%, 15%, 1);--colorHover:hsla(211, 100%, 43%, 1);--colorPress:hsla(211, 100%, 15%, 1);--colorFocus:hsla(211, 100%, 43%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(211, 100%, 15%, 0.2);--background:hsla(210, 100%, 94%, 1);--backgroundHover:hsla(210, 100%, 96%, 1);--backgroundPress:hsla(209, 96%, 90%, 1);--backgroundFocus:hsla(209, 96%, 90%, 1);--borderColor:hsla(208, 78%, 77%, 1);--borderColorHover:hsla(209, 82%, 85%, 1);--borderColorFocus:hsla(208, 78%, 77%, 1);--borderColorPress:hsla(206, 82%, 65%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_blue_Button, .t_blue_SliderTrackActive {--color:hsla(211, 100%, 15%, 1);--colorHover:hsla(211, 100%, 43%, 1);--colorPress:hsla(211, 100%, 15%, 1);--colorFocus:hsla(211, 100%, 43%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(211, 100%, 15%, 0.2);--background:hsla(210, 100%, 94%, 1);--backgroundHover:hsla(210, 100%, 96%, 1);--backgroundPress:hsla(209, 96%, 90%, 1);--backgroundFocus:hsla(209, 96%, 90%, 1);--borderColor:hsla(208, 78%, 77%, 1);--borderColorHover:hsla(209, 82%, 85%, 1);--borderColorFocus:hsla(208, 78%, 77%, 1);--borderColorPress:hsla(206, 82%, 65%, 1);}
  }
:root.t_dark .t_light .t_blue_Checkbox, :root.t_dark .t_light .t_blue_RadioGroupItem, :root.t_dark .t_light .t_blue_Switch, :root.t_dark .t_light .t_blue_TooltipContent, :root.t_light .t_blue_Checkbox, :root.t_light .t_blue_RadioGroupItem, :root.t_light .t_blue_Switch, :root.t_light .t_blue_TooltipContent, .tm_xxt {--color:hsla(211, 100%, 15%, 1);--colorHover:hsla(211, 100%, 43%, 1);--colorPress:hsla(211, 100%, 15%, 1);--colorFocus:hsla(211, 100%, 43%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(211, 100%, 15%, 0.2);--background:hsla(210, 100%, 96%, 1);--backgroundHover:hsla(210, 100%, 98%, 1);--backgroundPress:hsla(210, 100%, 94%, 1);--backgroundFocus:hsla(210, 100%, 94%, 1);--borderColor:hsla(209, 82%, 85%, 1);--borderColorHover:hsla(209, 96%, 90%, 1);--borderColorFocus:hsla(209, 82%, 85%, 1);--borderColorPress:hsla(208, 78%, 77%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_blue_Checkbox, .t_blue_RadioGroupItem, .t_blue_Switch, .t_blue_TooltipContent {--color:hsla(211, 100%, 15%, 1);--colorHover:hsla(211, 100%, 43%, 1);--colorPress:hsla(211, 100%, 15%, 1);--colorFocus:hsla(211, 100%, 43%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(211, 100%, 15%, 0.2);--background:hsla(210, 100%, 96%, 1);--backgroundHover:hsla(210, 100%, 98%, 1);--backgroundPress:hsla(210, 100%, 94%, 1);--backgroundFocus:hsla(210, 100%, 94%, 1);--borderColor:hsla(209, 82%, 85%, 1);--borderColorHover:hsla(209, 96%, 90%, 1);--borderColorFocus:hsla(209, 82%, 85%, 1);--borderColorPress:hsla(208, 78%, 77%, 1);}
  }
:root.t_dark .t_light .t_blue_ProgressIndicator, :root.t_dark .t_light .t_blue_SliderThumb, :root.t_dark .t_light .t_blue_SwitchThumb, :root.t_dark .t_light .t_blue_Tooltip, :root.t_light .t_blue_ProgressIndicator, :root.t_light .t_blue_SliderThumb, :root.t_light .t_blue_SwitchThumb, :root.t_light .t_blue_Tooltip, .tm_xxt {--accentBackground:hsla(0, 0%, 38%, 1);--accentColor:hsla(0, 0%, 10%, 1);--background0:hsla(211, 100%, 15%, 0);--background02:hsla(211, 100%, 15%, 0.2);--background04:hsla(211, 100%, 15%, 0.4);--background06:hsla(211, 100%, 15%, 0.6);--background08:hsla(211, 100%, 15%, 0.8);--color1:hsla(211, 100%, 15%, 1);--color2:hsla(211, 100%, 43%, 1);--color3:hsla(208, 100%, 47%, 1);--color4:hsla(206, 100%, 50%, 1);--color5:hsla(206, 82%, 65%, 1);--color6:hsla(208, 78%, 77%, 1);--color7:hsla(209, 82%, 85%, 1);--color8:hsla(209, 96%, 90%, 1);--color9:hsla(210, 100%, 94%, 1);--color10:hsla(210, 100%, 96%, 1);--color11:hsla(210, 100%, 98%, 1);--color12:hsla(210, 100%, 99%, 1);--color0:hsla(216, 100%, 99%, 0);--color02:hsla(216, 100%, 99%, 0.2);--color04:hsla(216, 100%, 99%, 0.4);--color06:hsla(216, 100%, 99%, 0.6);--color08:hsla(216, 100%, 99%, 0.8);--background:hsla(211, 100%, 15%, 1);--backgroundHover:hsla(211, 100%, 15%, 0.8);--backgroundPress:hsla(211, 100%, 43%, 1);--backgroundFocus:hsla(211, 100%, 43%, 1);--borderColor:hsla(206, 100%, 50%, 1);--borderColorHover:hsla(208, 100%, 47%, 1);--borderColorPress:hsla(206, 82%, 65%, 1);--borderColorFocus:hsla(206, 100%, 50%, 1);--color:hsla(210, 100%, 99%, 1);--colorHover:hsla(210, 100%, 98%, 1);--colorPress:hsla(210, 100%, 99%, 1);--colorFocus:hsla(210, 100%, 98%, 1);--placeholderColor:hsla(210, 100%, 94%, 1);--outlineColor:hsla(216, 100%, 99%, 0.2);--colorTransparent:hsla(216, 100%, 99%, 0);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_blue_ProgressIndicator, .t_blue_SliderThumb, .t_blue_SwitchThumb, .t_blue_Tooltip {--accentBackground:hsla(0, 0%, 38%, 1);--accentColor:hsla(0, 0%, 10%, 1);--background0:hsla(211, 100%, 15%, 0);--background02:hsla(211, 100%, 15%, 0.2);--background04:hsla(211, 100%, 15%, 0.4);--background06:hsla(211, 100%, 15%, 0.6);--background08:hsla(211, 100%, 15%, 0.8);--color1:hsla(211, 100%, 15%, 1);--color2:hsla(211, 100%, 43%, 1);--color3:hsla(208, 100%, 47%, 1);--color4:hsla(206, 100%, 50%, 1);--color5:hsla(206, 82%, 65%, 1);--color6:hsla(208, 78%, 77%, 1);--color7:hsla(209, 82%, 85%, 1);--color8:hsla(209, 96%, 90%, 1);--color9:hsla(210, 100%, 94%, 1);--color10:hsla(210, 100%, 96%, 1);--color11:hsla(210, 100%, 98%, 1);--color12:hsla(210, 100%, 99%, 1);--color0:hsla(216, 100%, 99%, 0);--color02:hsla(216, 100%, 99%, 0.2);--color04:hsla(216, 100%, 99%, 0.4);--color06:hsla(216, 100%, 99%, 0.6);--color08:hsla(216, 100%, 99%, 0.8);--background:hsla(211, 100%, 15%, 1);--backgroundHover:hsla(211, 100%, 15%, 0.8);--backgroundPress:hsla(211, 100%, 43%, 1);--backgroundFocus:hsla(211, 100%, 43%, 1);--borderColor:hsla(206, 100%, 50%, 1);--borderColorHover:hsla(208, 100%, 47%, 1);--borderColorPress:hsla(206, 82%, 65%, 1);--borderColorFocus:hsla(206, 100%, 50%, 1);--color:hsla(210, 100%, 99%, 1);--colorHover:hsla(210, 100%, 98%, 1);--colorPress:hsla(210, 100%, 99%, 1);--colorFocus:hsla(210, 100%, 98%, 1);--placeholderColor:hsla(210, 100%, 94%, 1);--outlineColor:hsla(216, 100%, 99%, 0.2);--colorTransparent:hsla(216, 100%, 99%, 0);}
  }
.t_light_blue_SwitchThumb ::selection, .t_light_blue_SliderThumb ::selection, .t_light_blue_Tooltip ::selection, .t_light_blue_ProgressIndicator ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_red_Card, :root.t_dark .t_light .t_red_Input, :root.t_dark .t_light .t_red_ListItem, :root.t_dark .t_light .t_red_Progress, :root.t_dark .t_light .t_red_SelectTrigger, :root.t_dark .t_light .t_red_SliderTrack, :root.t_dark .t_light .t_red_TextArea, :root.t_dark .t_light .t_red_TooltipArrow, :root.t_light .t_red_Card, :root.t_light .t_red_Input, :root.t_light .t_red_ListItem, :root.t_light .t_red_Progress, :root.t_light .t_red_SelectTrigger, :root.t_light .t_red_SliderTrack, :root.t_light .t_red_TextArea, :root.t_light .t_red_TooltipArrow, .tm_xxt {--color:hsla(355, 49%, 15%, 1);--colorHover:hsla(358, 65%, 49%, 1);--colorPress:hsla(355, 49%, 15%, 1);--colorFocus:hsla(358, 65%, 49%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(355, 48%, 15%, 0.2);--background:hsla(0, 100%, 99%, 1);--backgroundHover:hsla(0, 100%, 99%, 1);--backgroundPress:hsla(0, 100%, 97%, 1);--backgroundFocus:hsla(0, 100%, 97%, 1);--borderColor:hsla(0, 90%, 92%, 1);--borderColorHover:hsla(0, 100%, 95%, 1);--borderColorFocus:hsla(0, 90%, 92%, 1);--borderColorPress:hsla(0, 81%, 88%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_red_Card, .t_red_Input, .t_red_ListItem, .t_red_Progress, .t_red_SelectTrigger, .t_red_SliderTrack, .t_red_TextArea, .t_red_TooltipArrow {--color:hsla(355, 49%, 15%, 1);--colorHover:hsla(358, 65%, 49%, 1);--colorPress:hsla(355, 49%, 15%, 1);--colorFocus:hsla(358, 65%, 49%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(355, 48%, 15%, 0.2);--background:hsla(0, 100%, 99%, 1);--backgroundHover:hsla(0, 100%, 99%, 1);--backgroundPress:hsla(0, 100%, 97%, 1);--backgroundFocus:hsla(0, 100%, 97%, 1);--borderColor:hsla(0, 90%, 92%, 1);--borderColorHover:hsla(0, 100%, 95%, 1);--borderColorFocus:hsla(0, 90%, 92%, 1);--borderColorPress:hsla(0, 81%, 88%, 1);}
  }
:root.t_dark .t_light .t_red_Button, :root.t_dark .t_light .t_red_SliderTrackActive, :root.t_light .t_red_Button, :root.t_light .t_red_SliderTrackActive, .tm_xxt {--color:hsla(355, 49%, 15%, 1);--colorHover:hsla(358, 65%, 49%, 1);--colorPress:hsla(355, 49%, 15%, 1);--colorFocus:hsla(358, 65%, 49%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(355, 48%, 15%, 0.2);--background:hsla(0, 100%, 95%, 1);--backgroundHover:hsla(0, 100%, 97%, 1);--backgroundPress:hsla(0, 90%, 92%, 1);--backgroundFocus:hsla(0, 90%, 92%, 1);--borderColor:hsla(359, 74%, 82%, 1);--borderColorHover:hsla(0, 81%, 88%, 1);--borderColorFocus:hsla(359, 74%, 82%, 1);--borderColorPress:hsla(359, 69%, 74%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_red_Button, .t_red_SliderTrackActive {--color:hsla(355, 49%, 15%, 1);--colorHover:hsla(358, 65%, 49%, 1);--colorPress:hsla(355, 49%, 15%, 1);--colorFocus:hsla(358, 65%, 49%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(355, 48%, 15%, 0.2);--background:hsla(0, 100%, 95%, 1);--backgroundHover:hsla(0, 100%, 97%, 1);--backgroundPress:hsla(0, 90%, 92%, 1);--backgroundFocus:hsla(0, 90%, 92%, 1);--borderColor:hsla(359, 74%, 82%, 1);--borderColorHover:hsla(0, 81%, 88%, 1);--borderColorFocus:hsla(359, 74%, 82%, 1);--borderColorPress:hsla(359, 69%, 74%, 1);}
  }
:root.t_dark .t_light .t_red_Checkbox, :root.t_dark .t_light .t_red_RadioGroupItem, :root.t_dark .t_light .t_red_Switch, :root.t_dark .t_light .t_red_TooltipContent, :root.t_light .t_red_Checkbox, :root.t_light .t_red_RadioGroupItem, :root.t_light .t_red_Switch, :root.t_light .t_red_TooltipContent, .tm_xxt {--color:hsla(355, 49%, 15%, 1);--colorHover:hsla(358, 65%, 49%, 1);--colorPress:hsla(355, 49%, 15%, 1);--colorFocus:hsla(358, 65%, 49%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(355, 48%, 15%, 0.2);--background:hsla(0, 100%, 97%, 1);--backgroundHover:hsla(0, 100%, 99%, 1);--backgroundPress:hsla(0, 100%, 95%, 1);--backgroundFocus:hsla(0, 100%, 95%, 1);--borderColor:hsla(0, 81%, 88%, 1);--borderColorHover:hsla(0, 90%, 92%, 1);--borderColorFocus:hsla(0, 81%, 88%, 1);--borderColorPress:hsla(359, 74%, 82%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_red_Checkbox, .t_red_RadioGroupItem, .t_red_Switch, .t_red_TooltipContent {--color:hsla(355, 49%, 15%, 1);--colorHover:hsla(358, 65%, 49%, 1);--colorPress:hsla(355, 49%, 15%, 1);--colorFocus:hsla(358, 65%, 49%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(355, 48%, 15%, 0.2);--background:hsla(0, 100%, 97%, 1);--backgroundHover:hsla(0, 100%, 99%, 1);--backgroundPress:hsla(0, 100%, 95%, 1);--backgroundFocus:hsla(0, 100%, 95%, 1);--borderColor:hsla(0, 81%, 88%, 1);--borderColorHover:hsla(0, 90%, 92%, 1);--borderColorFocus:hsla(0, 81%, 88%, 1);--borderColorPress:hsla(359, 74%, 82%, 1);}
  }
:root.t_dark .t_light .t_red_ProgressIndicator, :root.t_dark .t_light .t_red_SliderThumb, :root.t_dark .t_light .t_red_SwitchThumb, :root.t_dark .t_light .t_red_Tooltip, :root.t_light .t_red_ProgressIndicator, :root.t_light .t_red_SliderThumb, :root.t_light .t_red_SwitchThumb, :root.t_light .t_red_Tooltip, .tm_xxt {--accentBackground:hsla(0, 0%, 38%, 1);--accentColor:hsla(0, 0%, 10%, 1);--background0:hsla(355, 48%, 15%, 0);--background02:hsla(355, 48%, 15%, 0.2);--background04:hsla(355, 48%, 15%, 0.4);--background06:hsla(355, 48%, 15%, 0.6);--background08:hsla(355, 48%, 15%, 0.8);--color1:hsla(355, 49%, 15%, 1);--color2:hsla(358, 65%, 49%, 1);--color3:hsla(358, 69%, 55%, 1);--color4:hsla(358, 75%, 59%, 1);--color5:hsla(359, 69%, 74%, 1);--color6:hsla(359, 74%, 82%, 1);--color7:hsla(0, 81%, 88%, 1);--color8:hsla(0, 90%, 92%, 1);--color9:hsla(0, 100%, 95%, 1);--color10:hsla(0, 100%, 97%, 1);--color11:hsla(0, 100%, 99%, 1);--color12:hsla(0, 100%, 99%, 1);--color0:hsla(0, 100%, 99%, 0);--color02:hsla(0, 100%, 99%, 0.2);--color04:hsla(0, 100%, 99%, 0.4);--color06:hsla(0, 100%, 99%, 0.6);--color08:hsla(0, 100%, 99%, 0.8);--background:hsla(355, 49%, 15%, 1);--backgroundHover:hsla(355, 48%, 15%, 0.8);--backgroundPress:hsla(358, 65%, 49%, 1);--backgroundFocus:hsla(358, 65%, 49%, 1);--borderColor:hsla(358, 75%, 59%, 1);--borderColorHover:hsla(358, 69%, 55%, 1);--borderColorPress:hsla(359, 69%, 74%, 1);--borderColorFocus:hsla(358, 75%, 59%, 1);--color:hsla(0, 100%, 99%, 1);--colorHover:hsla(0, 100%, 99%, 1);--colorPress:hsla(0, 100%, 99%, 1);--colorFocus:hsla(0, 100%, 99%, 1);--placeholderColor:hsla(0, 100%, 95%, 1);--outlineColor:hsla(0, 100%, 99%, 0.2);--colorTransparent:hsla(0, 100%, 99%, 0);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_red_ProgressIndicator, .t_red_SliderThumb, .t_red_SwitchThumb, .t_red_Tooltip {--accentBackground:hsla(0, 0%, 38%, 1);--accentColor:hsla(0, 0%, 10%, 1);--background0:hsla(355, 48%, 15%, 0);--background02:hsla(355, 48%, 15%, 0.2);--background04:hsla(355, 48%, 15%, 0.4);--background06:hsla(355, 48%, 15%, 0.6);--background08:hsla(355, 48%, 15%, 0.8);--color1:hsla(355, 49%, 15%, 1);--color2:hsla(358, 65%, 49%, 1);--color3:hsla(358, 69%, 55%, 1);--color4:hsla(358, 75%, 59%, 1);--color5:hsla(359, 69%, 74%, 1);--color6:hsla(359, 74%, 82%, 1);--color7:hsla(0, 81%, 88%, 1);--color8:hsla(0, 90%, 92%, 1);--color9:hsla(0, 100%, 95%, 1);--color10:hsla(0, 100%, 97%, 1);--color11:hsla(0, 100%, 99%, 1);--color12:hsla(0, 100%, 99%, 1);--color0:hsla(0, 100%, 99%, 0);--color02:hsla(0, 100%, 99%, 0.2);--color04:hsla(0, 100%, 99%, 0.4);--color06:hsla(0, 100%, 99%, 0.6);--color08:hsla(0, 100%, 99%, 0.8);--background:hsla(355, 49%, 15%, 1);--backgroundHover:hsla(355, 48%, 15%, 0.8);--backgroundPress:hsla(358, 65%, 49%, 1);--backgroundFocus:hsla(358, 65%, 49%, 1);--borderColor:hsla(358, 75%, 59%, 1);--borderColorHover:hsla(358, 69%, 55%, 1);--borderColorPress:hsla(359, 69%, 74%, 1);--borderColorFocus:hsla(358, 75%, 59%, 1);--color:hsla(0, 100%, 99%, 1);--colorHover:hsla(0, 100%, 99%, 1);--colorPress:hsla(0, 100%, 99%, 1);--colorFocus:hsla(0, 100%, 99%, 1);--placeholderColor:hsla(0, 100%, 95%, 1);--outlineColor:hsla(0, 100%, 99%, 0.2);--colorTransparent:hsla(0, 100%, 99%, 0);}
  }
.t_light_red_SwitchThumb ::selection, .t_light_red_SliderThumb ::selection, .t_light_red_Tooltip ::selection, .t_light_red_ProgressIndicator ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_yellow_Card, :root.t_dark .t_light .t_yellow_Input, :root.t_dark .t_light .t_yellow_ListItem, :root.t_dark .t_light .t_yellow_Progress, :root.t_dark .t_light .t_yellow_SelectTrigger, :root.t_dark .t_light .t_yellow_SliderTrack, :root.t_dark .t_light .t_yellow_TextArea, :root.t_dark .t_light .t_yellow_TooltipArrow, :root.t_light .t_yellow_Card, :root.t_light .t_yellow_Input, :root.t_light .t_yellow_ListItem, :root.t_light .t_yellow_Progress, :root.t_light .t_yellow_SelectTrigger, :root.t_light .t_yellow_SliderTrack, :root.t_light .t_yellow_TextArea, :root.t_light .t_yellow_TooltipArrow, .tm_xxt {--color:hsla(41, 56%, 13%, 1);--colorHover:hsla(42, 100%, 29%, 1);--colorPress:hsla(41, 56%, 13%, 1);--colorFocus:hsla(42, 100%, 29%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(41, 55%, 13%, 0.2);--background:hsla(52, 100%, 95%, 1);--backgroundHover:hsla(60, 50%, 98%, 1);--backgroundPress:hsla(55, 100%, 91%, 1);--backgroundFocus:hsla(55, 100%, 91%, 1);--borderColor:hsla(52, 98%, 82%, 1);--borderColorHover:hsla(54, 100%, 87%, 1);--borderColorFocus:hsla(52, 98%, 82%, 1);--borderColorPress:hsla(50, 90%, 76%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_yellow_Card, .t_yellow_Input, .t_yellow_ListItem, .t_yellow_Progress, .t_yellow_SelectTrigger, .t_yellow_SliderTrack, .t_yellow_TextArea, .t_yellow_TooltipArrow {--color:hsla(41, 56%, 13%, 1);--colorHover:hsla(42, 100%, 29%, 1);--colorPress:hsla(41, 56%, 13%, 1);--colorFocus:hsla(42, 100%, 29%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(41, 55%, 13%, 0.2);--background:hsla(52, 100%, 95%, 1);--backgroundHover:hsla(60, 50%, 98%, 1);--backgroundPress:hsla(55, 100%, 91%, 1);--backgroundFocus:hsla(55, 100%, 91%, 1);--borderColor:hsla(52, 98%, 82%, 1);--borderColorHover:hsla(54, 100%, 87%, 1);--borderColorFocus:hsla(52, 98%, 82%, 1);--borderColorPress:hsla(50, 90%, 76%, 1);}
  }
:root.t_dark .t_light .t_yellow_Button, :root.t_dark .t_light .t_yellow_SliderTrackActive, :root.t_light .t_yellow_Button, :root.t_light .t_yellow_SliderTrackActive, .tm_xxt {--color:hsla(41, 56%, 13%, 1);--colorHover:hsla(42, 100%, 29%, 1);--colorPress:hsla(41, 56%, 13%, 1);--colorFocus:hsla(42, 100%, 29%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(41, 55%, 13%, 0.2);--background:hsla(54, 100%, 87%, 1);--backgroundHover:hsla(55, 100%, 91%, 1);--backgroundPress:hsla(52, 98%, 82%, 1);--backgroundFocus:hsla(52, 98%, 82%, 1);--borderColor:hsla(47, 80%, 68%, 1);--borderColorHover:hsla(50, 90%, 76%, 1);--borderColorFocus:hsla(47, 80%, 68%, 1);--borderColorPress:hsla(48, 100%, 46%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_yellow_Button, .t_yellow_SliderTrackActive {--color:hsla(41, 56%, 13%, 1);--colorHover:hsla(42, 100%, 29%, 1);--colorPress:hsla(41, 56%, 13%, 1);--colorFocus:hsla(42, 100%, 29%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(41, 55%, 13%, 0.2);--background:hsla(54, 100%, 87%, 1);--backgroundHover:hsla(55, 100%, 91%, 1);--backgroundPress:hsla(52, 98%, 82%, 1);--backgroundFocus:hsla(52, 98%, 82%, 1);--borderColor:hsla(47, 80%, 68%, 1);--borderColorHover:hsla(50, 90%, 76%, 1);--borderColorFocus:hsla(47, 80%, 68%, 1);--borderColorPress:hsla(48, 100%, 46%, 1);}
  }
:root.t_dark .t_light .t_yellow_Checkbox, :root.t_dark .t_light .t_yellow_RadioGroupItem, :root.t_dark .t_light .t_yellow_Switch, :root.t_dark .t_light .t_yellow_TooltipContent, :root.t_light .t_yellow_Checkbox, :root.t_light .t_yellow_RadioGroupItem, :root.t_light .t_yellow_Switch, :root.t_light .t_yellow_TooltipContent, .tm_xxt {--color:hsla(41, 56%, 13%, 1);--colorHover:hsla(42, 100%, 29%, 1);--colorPress:hsla(41, 56%, 13%, 1);--colorFocus:hsla(42, 100%, 29%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(41, 55%, 13%, 0.2);--background:hsla(55, 100%, 91%, 1);--backgroundHover:hsla(52, 100%, 95%, 1);--backgroundPress:hsla(54, 100%, 87%, 1);--backgroundFocus:hsla(54, 100%, 87%, 1);--borderColor:hsla(50, 90%, 76%, 1);--borderColorHover:hsla(52, 98%, 82%, 1);--borderColorFocus:hsla(50, 90%, 76%, 1);--borderColorPress:hsla(47, 80%, 68%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_yellow_Checkbox, .t_yellow_RadioGroupItem, .t_yellow_Switch, .t_yellow_TooltipContent {--color:hsla(41, 56%, 13%, 1);--colorHover:hsla(42, 100%, 29%, 1);--colorPress:hsla(41, 56%, 13%, 1);--colorFocus:hsla(42, 100%, 29%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(41, 55%, 13%, 0.2);--background:hsla(55, 100%, 91%, 1);--backgroundHover:hsla(52, 100%, 95%, 1);--backgroundPress:hsla(54, 100%, 87%, 1);--backgroundFocus:hsla(54, 100%, 87%, 1);--borderColor:hsla(50, 90%, 76%, 1);--borderColorHover:hsla(52, 98%, 82%, 1);--borderColorFocus:hsla(50, 90%, 76%, 1);--borderColorPress:hsla(47, 80%, 68%, 1);}
  }
:root.t_dark .t_light .t_yellow_ProgressIndicator, :root.t_dark .t_light .t_yellow_SliderThumb, :root.t_dark .t_light .t_yellow_SwitchThumb, :root.t_dark .t_light .t_yellow_Tooltip, :root.t_light .t_yellow_ProgressIndicator, :root.t_light .t_yellow_SliderThumb, :root.t_light .t_yellow_SwitchThumb, :root.t_light .t_yellow_Tooltip, .tm_xxt {--accentBackground:hsla(0, 0%, 38%, 1);--accentColor:hsla(0, 0%, 10%, 1);--background0:hsla(41, 55%, 13%, 0);--background02:hsla(41, 55%, 13%, 0.2);--background04:hsla(41, 55%, 13%, 0.4);--background06:hsla(41, 55%, 13%, 0.6);--background08:hsla(41, 55%, 13%, 0.8);--color1:hsla(41, 56%, 13%, 1);--color2:hsla(42, 100%, 29%, 1);--color3:hsla(50, 100%, 48%, 1);--color4:hsla(53, 92%, 50%, 1);--color5:hsla(48, 100%, 46%, 1);--color6:hsla(47, 80%, 68%, 1);--color7:hsla(50, 90%, 76%, 1);--color8:hsla(52, 98%, 82%, 1);--color9:hsla(54, 100%, 87%, 1);--color10:hsla(55, 100%, 91%, 1);--color11:hsla(52, 100%, 95%, 1);--color12:hsla(60, 50%, 98%, 1);--color0:hsla(60, 45%, 98%, 0);--color02:hsla(60, 45%, 98%, 0.2);--color04:hsla(60, 45%, 98%, 0.4);--color06:hsla(60, 45%, 98%, 0.6);--color08:hsla(60, 45%, 98%, 0.8);--background:hsla(41, 56%, 13%, 1);--backgroundHover:hsla(41, 55%, 13%, 0.8);--backgroundPress:hsla(42, 100%, 29%, 1);--backgroundFocus:hsla(42, 100%, 29%, 1);--borderColor:hsla(53, 92%, 50%, 1);--borderColorHover:hsla(50, 100%, 48%, 1);--borderColorPress:hsla(48, 100%, 46%, 1);--borderColorFocus:hsla(53, 92%, 50%, 1);--color:hsla(60, 50%, 98%, 1);--colorHover:hsla(52, 100%, 95%, 1);--colorPress:hsla(60, 50%, 98%, 1);--colorFocus:hsla(52, 100%, 95%, 1);--placeholderColor:hsla(54, 100%, 87%, 1);--outlineColor:hsla(60, 45%, 98%, 0.2);--colorTransparent:hsla(60, 45%, 98%, 0);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_yellow_ProgressIndicator, .t_yellow_SliderThumb, .t_yellow_SwitchThumb, .t_yellow_Tooltip {--accentBackground:hsla(0, 0%, 38%, 1);--accentColor:hsla(0, 0%, 10%, 1);--background0:hsla(41, 55%, 13%, 0);--background02:hsla(41, 55%, 13%, 0.2);--background04:hsla(41, 55%, 13%, 0.4);--background06:hsla(41, 55%, 13%, 0.6);--background08:hsla(41, 55%, 13%, 0.8);--color1:hsla(41, 56%, 13%, 1);--color2:hsla(42, 100%, 29%, 1);--color3:hsla(50, 100%, 48%, 1);--color4:hsla(53, 92%, 50%, 1);--color5:hsla(48, 100%, 46%, 1);--color6:hsla(47, 80%, 68%, 1);--color7:hsla(50, 90%, 76%, 1);--color8:hsla(52, 98%, 82%, 1);--color9:hsla(54, 100%, 87%, 1);--color10:hsla(55, 100%, 91%, 1);--color11:hsla(52, 100%, 95%, 1);--color12:hsla(60, 50%, 98%, 1);--color0:hsla(60, 45%, 98%, 0);--color02:hsla(60, 45%, 98%, 0.2);--color04:hsla(60, 45%, 98%, 0.4);--color06:hsla(60, 45%, 98%, 0.6);--color08:hsla(60, 45%, 98%, 0.8);--background:hsla(41, 56%, 13%, 1);--backgroundHover:hsla(41, 55%, 13%, 0.8);--backgroundPress:hsla(42, 100%, 29%, 1);--backgroundFocus:hsla(42, 100%, 29%, 1);--borderColor:hsla(53, 92%, 50%, 1);--borderColorHover:hsla(50, 100%, 48%, 1);--borderColorPress:hsla(48, 100%, 46%, 1);--borderColorFocus:hsla(53, 92%, 50%, 1);--color:hsla(60, 50%, 98%, 1);--colorHover:hsla(52, 100%, 95%, 1);--colorPress:hsla(60, 50%, 98%, 1);--colorFocus:hsla(52, 100%, 95%, 1);--placeholderColor:hsla(54, 100%, 87%, 1);--outlineColor:hsla(60, 45%, 98%, 0.2);--colorTransparent:hsla(60, 45%, 98%, 0);}
  }
.t_light_yellow_SwitchThumb ::selection, .t_light_yellow_SliderThumb ::selection, .t_light_yellow_Tooltip ::selection, .t_light_yellow_ProgressIndicator ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_green_Card, :root.t_dark .t_light .t_green_Input, :root.t_dark .t_light .t_green_ListItem, :root.t_dark .t_light .t_green_Progress, :root.t_dark .t_light .t_green_SelectTrigger, :root.t_dark .t_light .t_green_SliderTrack, :root.t_dark .t_light .t_green_TextArea, :root.t_dark .t_light .t_green_TooltipArrow, :root.t_light .t_green_Card, :root.t_light .t_green_Input, :root.t_light .t_green_ListItem, :root.t_light .t_green_Progress, :root.t_light .t_green_SelectTrigger, :root.t_light .t_green_SliderTrack, :root.t_light .t_green_TextArea, :root.t_light .t_green_TooltipArrow, .tm_xxt {--color:hsla(155, 41%, 14%, 1);--colorHover:hsla(153, 67%, 28%, 1);--colorPress:hsla(155, 41%, 14%, 1);--colorFocus:hsla(153, 67%, 28%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(155, 41%, 14%, 0.2);--background:hsla(138, 63%, 97%, 1);--backgroundHover:hsla(140, 60%, 99%, 1);--backgroundPress:hsla(139, 57%, 95%, 1);--backgroundFocus:hsla(139, 57%, 95%, 1);--borderColor:hsla(141, 44%, 86%, 1);--borderColorHover:hsla(139, 48%, 91%, 1);--borderColorFocus:hsla(141, 44%, 86%, 1);--borderColorPress:hsla(142, 40%, 79%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_green_Card, .t_green_Input, .t_green_ListItem, .t_green_Progress, .t_green_SelectTrigger, .t_green_SliderTrack, .t_green_TextArea, .t_green_TooltipArrow {--color:hsla(155, 41%, 14%, 1);--colorHover:hsla(153, 67%, 28%, 1);--colorPress:hsla(155, 41%, 14%, 1);--colorFocus:hsla(153, 67%, 28%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(155, 41%, 14%, 0.2);--background:hsla(138, 63%, 97%, 1);--backgroundHover:hsla(140, 60%, 99%, 1);--backgroundPress:hsla(139, 57%, 95%, 1);--backgroundFocus:hsla(139, 57%, 95%, 1);--borderColor:hsla(141, 44%, 86%, 1);--borderColorHover:hsla(139, 48%, 91%, 1);--borderColorFocus:hsla(141, 44%, 86%, 1);--borderColorPress:hsla(142, 40%, 79%, 1);}
  }
:root.t_dark .t_light .t_green_Button, :root.t_dark .t_light .t_green_SliderTrackActive, :root.t_light .t_green_Button, :root.t_light .t_green_SliderTrackActive, .tm_xxt {--color:hsla(155, 41%, 14%, 1);--colorHover:hsla(153, 67%, 28%, 1);--colorPress:hsla(155, 41%, 14%, 1);--colorFocus:hsla(153, 67%, 28%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(155, 41%, 14%, 0.2);--background:hsla(139, 48%, 91%, 1);--backgroundHover:hsla(139, 57%, 95%, 1);--backgroundPress:hsla(141, 44%, 86%, 1);--backgroundFocus:hsla(141, 44%, 86%, 1);--borderColor:hsla(146, 38%, 69%, 1);--borderColorHover:hsla(142, 40%, 79%, 1);--borderColorFocus:hsla(146, 38%, 69%, 1);--borderColorPress:hsla(151, 40%, 54%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_green_Button, .t_green_SliderTrackActive {--color:hsla(155, 41%, 14%, 1);--colorHover:hsla(153, 67%, 28%, 1);--colorPress:hsla(155, 41%, 14%, 1);--colorFocus:hsla(153, 67%, 28%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(155, 41%, 14%, 0.2);--background:hsla(139, 48%, 91%, 1);--backgroundHover:hsla(139, 57%, 95%, 1);--backgroundPress:hsla(141, 44%, 86%, 1);--backgroundFocus:hsla(141, 44%, 86%, 1);--borderColor:hsla(146, 38%, 69%, 1);--borderColorHover:hsla(142, 40%, 79%, 1);--borderColorFocus:hsla(146, 38%, 69%, 1);--borderColorPress:hsla(151, 40%, 54%, 1);}
  }
:root.t_dark .t_light .t_green_Checkbox, :root.t_dark .t_light .t_green_RadioGroupItem, :root.t_dark .t_light .t_green_Switch, :root.t_dark .t_light .t_green_TooltipContent, :root.t_light .t_green_Checkbox, :root.t_light .t_green_RadioGroupItem, :root.t_light .t_green_Switch, :root.t_light .t_green_TooltipContent, .tm_xxt {--color:hsla(155, 41%, 14%, 1);--colorHover:hsla(153, 67%, 28%, 1);--colorPress:hsla(155, 41%, 14%, 1);--colorFocus:hsla(153, 67%, 28%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(155, 41%, 14%, 0.2);--background:hsla(139, 57%, 95%, 1);--backgroundHover:hsla(138, 63%, 97%, 1);--backgroundPress:hsla(139, 48%, 91%, 1);--backgroundFocus:hsla(139, 48%, 91%, 1);--borderColor:hsla(142, 40%, 79%, 1);--borderColorHover:hsla(141, 44%, 86%, 1);--borderColorFocus:hsla(142, 40%, 79%, 1);--borderColorPress:hsla(146, 38%, 69%, 1);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_green_Checkbox, .t_green_RadioGroupItem, .t_green_Switch, .t_green_TooltipContent {--color:hsla(155, 41%, 14%, 1);--colorHover:hsla(153, 67%, 28%, 1);--colorPress:hsla(155, 41%, 14%, 1);--colorFocus:hsla(153, 67%, 28%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(155, 41%, 14%, 0.2);--background:hsla(139, 57%, 95%, 1);--backgroundHover:hsla(138, 63%, 97%, 1);--backgroundPress:hsla(139, 48%, 91%, 1);--backgroundFocus:hsla(139, 48%, 91%, 1);--borderColor:hsla(142, 40%, 79%, 1);--borderColorHover:hsla(141, 44%, 86%, 1);--borderColorFocus:hsla(142, 40%, 79%, 1);--borderColorPress:hsla(146, 38%, 69%, 1);}
  }
:root.t_dark .t_light .t_green_ProgressIndicator, :root.t_dark .t_light .t_green_SliderThumb, :root.t_dark .t_light .t_green_SwitchThumb, :root.t_dark .t_light .t_green_Tooltip, :root.t_light .t_green_ProgressIndicator, :root.t_light .t_green_SliderThumb, :root.t_light .t_green_SwitchThumb, :root.t_light .t_green_Tooltip, .tm_xxt {--accentBackground:hsla(0, 0%, 38%, 1);--accentColor:hsla(0, 0%, 10%, 1);--background0:hsla(155, 41%, 14%, 0);--background02:hsla(155, 41%, 14%, 0.2);--background04:hsla(155, 41%, 14%, 0.4);--background06:hsla(155, 41%, 14%, 0.6);--background08:hsla(155, 41%, 14%, 0.8);--color1:hsla(155, 41%, 14%, 1);--color2:hsla(153, 67%, 28%, 1);--color3:hsla(152, 57%, 38%, 1);--color4:hsla(151, 55%, 42%, 1);--color5:hsla(151, 40%, 54%, 1);--color6:hsla(146, 38%, 69%, 1);--color7:hsla(142, 40%, 79%, 1);--color8:hsla(141, 44%, 86%, 1);--color9:hsla(139, 48%, 91%, 1);--color10:hsla(139, 57%, 95%, 1);--color11:hsla(138, 63%, 97%, 1);--color12:hsla(140, 60%, 99%, 1);--color0:hsla(140, 60%, 99%, 0);--color02:hsla(140, 60%, 99%, 0.2);--color04:hsla(140, 60%, 99%, 0.4);--color06:hsla(140, 60%, 99%, 0.6);--color08:hsla(140, 60%, 99%, 0.8);--background:hsla(155, 41%, 14%, 1);--backgroundHover:hsla(155, 41%, 14%, 0.8);--backgroundPress:hsla(153, 67%, 28%, 1);--backgroundFocus:hsla(153, 67%, 28%, 1);--borderColor:hsla(151, 55%, 42%, 1);--borderColorHover:hsla(152, 57%, 38%, 1);--borderColorPress:hsla(151, 40%, 54%, 1);--borderColorFocus:hsla(151, 55%, 42%, 1);--color:hsla(140, 60%, 99%, 1);--colorHover:hsla(138, 63%, 97%, 1);--colorPress:hsla(140, 60%, 99%, 1);--colorFocus:hsla(138, 63%, 97%, 1);--placeholderColor:hsla(139, 48%, 91%, 1);--outlineColor:hsla(140, 60%, 99%, 0.2);--colorTransparent:hsla(140, 60%, 99%, 0);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_green_ProgressIndicator, .t_green_SliderThumb, .t_green_SwitchThumb, .t_green_Tooltip {--accentBackground:hsla(0, 0%, 38%, 1);--accentColor:hsla(0, 0%, 10%, 1);--background0:hsla(155, 41%, 14%, 0);--background02:hsla(155, 41%, 14%, 0.2);--background04:hsla(155, 41%, 14%, 0.4);--background06:hsla(155, 41%, 14%, 0.6);--background08:hsla(155, 41%, 14%, 0.8);--color1:hsla(155, 41%, 14%, 1);--color2:hsla(153, 67%, 28%, 1);--color3:hsla(152, 57%, 38%, 1);--color4:hsla(151, 55%, 42%, 1);--color5:hsla(151, 40%, 54%, 1);--color6:hsla(146, 38%, 69%, 1);--color7:hsla(142, 40%, 79%, 1);--color8:hsla(141, 44%, 86%, 1);--color9:hsla(139, 48%, 91%, 1);--color10:hsla(139, 57%, 95%, 1);--color11:hsla(138, 63%, 97%, 1);--color12:hsla(140, 60%, 99%, 1);--color0:hsla(140, 60%, 99%, 0);--color02:hsla(140, 60%, 99%, 0.2);--color04:hsla(140, 60%, 99%, 0.4);--color06:hsla(140, 60%, 99%, 0.6);--color08:hsla(140, 60%, 99%, 0.8);--background:hsla(155, 41%, 14%, 1);--backgroundHover:hsla(155, 41%, 14%, 0.8);--backgroundPress:hsla(153, 67%, 28%, 1);--backgroundFocus:hsla(153, 67%, 28%, 1);--borderColor:hsla(151, 55%, 42%, 1);--borderColorHover:hsla(152, 57%, 38%, 1);--borderColorPress:hsla(151, 40%, 54%, 1);--borderColorFocus:hsla(151, 55%, 42%, 1);--color:hsla(140, 60%, 99%, 1);--colorHover:hsla(138, 63%, 97%, 1);--colorPress:hsla(140, 60%, 99%, 1);--colorFocus:hsla(138, 63%, 97%, 1);--placeholderColor:hsla(139, 48%, 91%, 1);--outlineColor:hsla(140, 60%, 99%, 0.2);--colorTransparent:hsla(140, 60%, 99%, 0);}
  }
.t_light_green_SwitchThumb ::selection, .t_light_green_SliderThumb ::selection, .t_light_green_Tooltip ::selection, .t_light_green_ProgressIndicator ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_white_ProgressIndicator, :root.t_dark .t_white_SliderThumb, :root.t_dark .t_white_SwitchThumb, :root.t_dark .t_white_Tooltip, :root.t_light .t_dark .t_white_ProgressIndicator, :root.t_light .t_dark .t_white_SliderThumb, :root.t_light .t_dark .t_white_SwitchThumb, :root.t_light .t_dark .t_white_Tooltip, .tm_xxt {--accentBackground:hsla(0, 0%, 93%, 1);--accentColor:hsla(0, 0%, 50%, 1);--background0:hsla(0, 0%, 9%, 0);--background02:hsla(0, 0%, 9%, 0.2);--background04:hsla(0, 0%, 9%, 0.4);--background06:hsla(0, 0%, 9%, 0.6);--background08:hsla(0, 0%, 9%, 0.8);--color1:hsla(0, 0%, 9%, 1);--color2:hsla(0, 0%, 42%, 1);--color3:hsla(0, 0%, 50%, 1);--color4:hsla(0, 0%, 56%, 1);--color5:hsla(0, 0%, 76%, 1);--color6:hsla(0, 0%, 82%, 1);--color7:hsla(0, 0%, 85%, 1);--color8:hsla(0, 0%, 88%, 1);--color9:hsla(0, 0%, 91%, 1);--color10:hsla(0, 0%, 93%, 1);--color11:hsla(0, 0%, 95%, 1);--color12:hsla(0, 0%, 100%, 1);--color0:hsla(0, 0%, 100%, 0);--color02:hsla(0, 0%, 100%, 0.2);--color04:hsla(0, 0%, 100%, 0.4);--color06:hsla(0, 0%, 100%, 0.6);--color08:hsla(0, 0%, 100%, 0.8);--background:hsla(0, 0%, 9%, 1);--backgroundHover:hsla(0, 0%, 42%, 1);--backgroundPress:hsla(0, 0%, 9%, 0.8);--backgroundFocus:hsla(0, 0%, 9%, 0.8);--borderColor:hsla(0, 0%, 56%, 1);--borderColorHover:hsla(0, 0%, 76%, 1);--borderColorPress:hsla(0, 0%, 50%, 1);--borderColorFocus:hsla(0, 0%, 56%, 1);--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 95%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 95%, 1);--placeholderColor:hsla(0, 0%, 91%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--colorTransparent:hsla(0, 0%, 100%, 0);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_white_ProgressIndicator, .t_white_SliderThumb, .t_white_SwitchThumb, .t_white_Tooltip {--accentBackground:hsla(0, 0%, 93%, 1);--accentColor:hsla(0, 0%, 50%, 1);--background0:hsla(0, 0%, 9%, 0);--background02:hsla(0, 0%, 9%, 0.2);--background04:hsla(0, 0%, 9%, 0.4);--background06:hsla(0, 0%, 9%, 0.6);--background08:hsla(0, 0%, 9%, 0.8);--color1:hsla(0, 0%, 9%, 1);--color2:hsla(0, 0%, 42%, 1);--color3:hsla(0, 0%, 50%, 1);--color4:hsla(0, 0%, 56%, 1);--color5:hsla(0, 0%, 76%, 1);--color6:hsla(0, 0%, 82%, 1);--color7:hsla(0, 0%, 85%, 1);--color8:hsla(0, 0%, 88%, 1);--color9:hsla(0, 0%, 91%, 1);--color10:hsla(0, 0%, 93%, 1);--color11:hsla(0, 0%, 95%, 1);--color12:hsla(0, 0%, 100%, 1);--color0:hsla(0, 0%, 100%, 0);--color02:hsla(0, 0%, 100%, 0.2);--color04:hsla(0, 0%, 100%, 0.4);--color06:hsla(0, 0%, 100%, 0.6);--color08:hsla(0, 0%, 100%, 0.8);--background:hsla(0, 0%, 9%, 1);--backgroundHover:hsla(0, 0%, 42%, 1);--backgroundPress:hsla(0, 0%, 9%, 0.8);--backgroundFocus:hsla(0, 0%, 9%, 0.8);--borderColor:hsla(0, 0%, 56%, 1);--borderColorHover:hsla(0, 0%, 76%, 1);--borderColorPress:hsla(0, 0%, 50%, 1);--borderColorFocus:hsla(0, 0%, 56%, 1);--color:hsla(0, 0%, 100%, 1);--colorHover:hsla(0, 0%, 95%, 1);--colorPress:hsla(0, 0%, 100%, 1);--colorFocus:hsla(0, 0%, 95%, 1);--placeholderColor:hsla(0, 0%, 91%, 1);--outlineColor:hsla(0, 0%, 100%, 0.2);--colorTransparent:hsla(0, 0%, 100%, 0);}
  }
.t_dark_white_SwitchThumb ::selection, .t_dark_white_SliderThumb ::selection, .t_dark_white_Tooltip ::selection, .t_dark_white_ProgressIndicator ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_blue_Card, :root.t_dark .t_blue_Input, :root.t_dark .t_blue_ListItem, :root.t_dark .t_blue_Progress, :root.t_dark .t_blue_SelectTrigger, :root.t_dark .t_blue_SliderTrack, :root.t_dark .t_blue_TextArea, :root.t_dark .t_blue_TooltipArrow, :root.t_light .t_dark .t_blue_Card, :root.t_light .t_dark .t_blue_Input, :root.t_light .t_dark .t_blue_ListItem, :root.t_light .t_dark .t_blue_Progress, :root.t_light .t_dark .t_blue_SelectTrigger, :root.t_light .t_dark .t_blue_SliderTrack, :root.t_light .t_dark .t_blue_TextArea, :root.t_light .t_dark .t_blue_TooltipArrow, .tm_xxt {--color:hsla(206, 100%, 96%, 1);--colorHover:hsla(210, 100%, 66%, 1);--colorPress:hsla(206, 100%, 96%, 1);--colorFocus:hsla(210, 100%, 66%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(207, 100%, 96%, 0.2);--background:hsla(216, 50%, 12%, 1);--backgroundHover:hsla(214, 59%, 15%, 1);--backgroundPress:hsla(212, 36%, 9%, 1);--backgroundFocus:hsla(212, 36%, 9%, 1);--borderColor:hsla(213, 71%, 20%, 1);--borderColorHover:hsla(212, 78%, 23%, 1);--borderColorFocus:hsla(213, 71%, 20%, 1);--borderColorPress:hsla(214, 65%, 18%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_blue_Card, .t_blue_Input, .t_blue_ListItem, .t_blue_Progress, .t_blue_SelectTrigger, .t_blue_SliderTrack, .t_blue_TextArea, .t_blue_TooltipArrow {--color:hsla(206, 100%, 96%, 1);--colorHover:hsla(210, 100%, 66%, 1);--colorPress:hsla(206, 100%, 96%, 1);--colorFocus:hsla(210, 100%, 66%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(207, 100%, 96%, 0.2);--background:hsla(216, 50%, 12%, 1);--backgroundHover:hsla(214, 59%, 15%, 1);--backgroundPress:hsla(212, 36%, 9%, 1);--backgroundFocus:hsla(212, 36%, 9%, 1);--borderColor:hsla(213, 71%, 20%, 1);--borderColorHover:hsla(212, 78%, 23%, 1);--borderColorFocus:hsla(213, 71%, 20%, 1);--borderColorPress:hsla(214, 65%, 18%, 1);}
  }
:root.t_dark .t_blue_Button, :root.t_dark .t_blue_SliderTrackActive, :root.t_light .t_dark .t_blue_Button, :root.t_light .t_dark .t_blue_SliderTrackActive, .tm_xxt {--color:hsla(206, 100%, 96%, 1);--colorHover:hsla(210, 100%, 66%, 1);--colorPress:hsla(206, 100%, 96%, 1);--colorFocus:hsla(210, 100%, 66%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(207, 100%, 96%, 0.2);--background:hsla(214, 65%, 18%, 1);--backgroundHover:hsla(213, 71%, 20%, 1);--backgroundPress:hsla(214, 59%, 15%, 1);--backgroundFocus:hsla(214, 59%, 15%, 1);--borderColor:hsla(211, 86%, 27%, 1);--borderColorHover:hsla(211, 90%, 34%, 1);--borderColorFocus:hsla(211, 86%, 27%, 1);--borderColorPress:hsla(212, 78%, 23%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_blue_Button, .t_blue_SliderTrackActive {--color:hsla(206, 100%, 96%, 1);--colorHover:hsla(210, 100%, 66%, 1);--colorPress:hsla(206, 100%, 96%, 1);--colorFocus:hsla(210, 100%, 66%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(207, 100%, 96%, 0.2);--background:hsla(214, 65%, 18%, 1);--backgroundHover:hsla(213, 71%, 20%, 1);--backgroundPress:hsla(214, 59%, 15%, 1);--backgroundFocus:hsla(214, 59%, 15%, 1);--borderColor:hsla(211, 86%, 27%, 1);--borderColorHover:hsla(211, 90%, 34%, 1);--borderColorFocus:hsla(211, 86%, 27%, 1);--borderColorPress:hsla(212, 78%, 23%, 1);}
  }
:root.t_dark .t_blue_Checkbox, :root.t_dark .t_blue_RadioGroupItem, :root.t_dark .t_blue_Switch, :root.t_dark .t_blue_TooltipContent, :root.t_light .t_dark .t_blue_Checkbox, :root.t_light .t_dark .t_blue_RadioGroupItem, :root.t_light .t_dark .t_blue_Switch, :root.t_light .t_dark .t_blue_TooltipContent, .tm_xxt {--color:hsla(206, 100%, 96%, 1);--colorHover:hsla(210, 100%, 66%, 1);--colorPress:hsla(206, 100%, 96%, 1);--colorFocus:hsla(210, 100%, 66%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(207, 100%, 96%, 0.2);--background:hsla(214, 59%, 15%, 1);--backgroundHover:hsla(214, 65%, 18%, 1);--backgroundPress:hsla(216, 50%, 12%, 1);--backgroundFocus:hsla(216, 50%, 12%, 1);--borderColor:hsla(212, 78%, 23%, 1);--borderColorHover:hsla(211, 86%, 27%, 1);--borderColorFocus:hsla(212, 78%, 23%, 1);--borderColorPress:hsla(213, 71%, 20%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_blue_Checkbox, .t_blue_RadioGroupItem, .t_blue_Switch, .t_blue_TooltipContent {--color:hsla(206, 100%, 96%, 1);--colorHover:hsla(210, 100%, 66%, 1);--colorPress:hsla(206, 100%, 96%, 1);--colorFocus:hsla(210, 100%, 66%, 1);--placeholderColor:hsla(206, 100%, 50%, 1);--outlineColor:hsla(207, 100%, 96%, 0.2);--background:hsla(214, 59%, 15%, 1);--backgroundHover:hsla(214, 65%, 18%, 1);--backgroundPress:hsla(216, 50%, 12%, 1);--backgroundFocus:hsla(216, 50%, 12%, 1);--borderColor:hsla(212, 78%, 23%, 1);--borderColorHover:hsla(211, 86%, 27%, 1);--borderColorFocus:hsla(212, 78%, 23%, 1);--borderColorPress:hsla(213, 71%, 20%, 1);}
  }
:root.t_dark .t_blue_ProgressIndicator, :root.t_dark .t_blue_SliderThumb, :root.t_dark .t_blue_SwitchThumb, :root.t_dark .t_blue_Tooltip, :root.t_light .t_dark .t_blue_ProgressIndicator, :root.t_light .t_dark .t_blue_SliderThumb, :root.t_light .t_dark .t_blue_SwitchThumb, :root.t_light .t_dark .t_blue_Tooltip, .tm_xxt {--accentBackground:hsla(0, 0%, 93%, 1);--accentColor:hsla(0, 0%, 50%, 1);--background0:hsla(207, 100%, 96%, 0);--background02:hsla(207, 100%, 96%, 0.2);--background04:hsla(207, 100%, 96%, 0.4);--background06:hsla(207, 100%, 96%, 0.6);--background08:hsla(207, 100%, 96%, 0.8);--color1:hsla(206, 100%, 96%, 1);--color2:hsla(210, 100%, 66%, 1);--color3:hsla(209, 100%, 61%, 1);--color4:hsla(206, 100%, 50%, 1);--color5:hsla(211, 90%, 34%, 1);--color6:hsla(211, 86%, 27%, 1);--color7:hsla(212, 78%, 23%, 1);--color8:hsla(213, 71%, 20%, 1);--color9:hsla(214, 65%, 18%, 1);--color10:hsla(214, 59%, 15%, 1);--color11:hsla(216, 50%, 12%, 1);--color12:hsla(212, 36%, 9%, 1);--color0:hsla(214, 35%, 9%, 0);--color02:hsla(214, 35%, 9%, 0.2);--color04:hsla(214, 35%, 9%, 0.4);--color06:hsla(214, 35%, 9%, 0.6);--color08:hsla(214, 35%, 9%, 0.8);--background:hsla(206, 100%, 96%, 1);--backgroundHover:hsla(210, 100%, 66%, 1);--backgroundPress:hsla(207, 100%, 96%, 0.8);--backgroundFocus:hsla(207, 100%, 96%, 0.8);--borderColor:hsla(206, 100%, 50%, 1);--borderColorHover:hsla(211, 90%, 34%, 1);--borderColorPress:hsla(209, 100%, 61%, 1);--borderColorFocus:hsla(206, 100%, 50%, 1);--color:hsla(212, 36%, 9%, 1);--colorHover:hsla(216, 50%, 12%, 1);--colorPress:hsla(212, 36%, 9%, 1);--colorFocus:hsla(216, 50%, 12%, 1);--placeholderColor:hsla(214, 65%, 18%, 1);--outlineColor:hsla(214, 35%, 9%, 0.2);--colorTransparent:hsla(214, 35%, 9%, 0);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_blue_ProgressIndicator, .t_blue_SliderThumb, .t_blue_SwitchThumb, .t_blue_Tooltip {--accentBackground:hsla(0, 0%, 93%, 1);--accentColor:hsla(0, 0%, 50%, 1);--background0:hsla(207, 100%, 96%, 0);--background02:hsla(207, 100%, 96%, 0.2);--background04:hsla(207, 100%, 96%, 0.4);--background06:hsla(207, 100%, 96%, 0.6);--background08:hsla(207, 100%, 96%, 0.8);--color1:hsla(206, 100%, 96%, 1);--color2:hsla(210, 100%, 66%, 1);--color3:hsla(209, 100%, 61%, 1);--color4:hsla(206, 100%, 50%, 1);--color5:hsla(211, 90%, 34%, 1);--color6:hsla(211, 86%, 27%, 1);--color7:hsla(212, 78%, 23%, 1);--color8:hsla(213, 71%, 20%, 1);--color9:hsla(214, 65%, 18%, 1);--color10:hsla(214, 59%, 15%, 1);--color11:hsla(216, 50%, 12%, 1);--color12:hsla(212, 36%, 9%, 1);--color0:hsla(214, 35%, 9%, 0);--color02:hsla(214, 35%, 9%, 0.2);--color04:hsla(214, 35%, 9%, 0.4);--color06:hsla(214, 35%, 9%, 0.6);--color08:hsla(214, 35%, 9%, 0.8);--background:hsla(206, 100%, 96%, 1);--backgroundHover:hsla(210, 100%, 66%, 1);--backgroundPress:hsla(207, 100%, 96%, 0.8);--backgroundFocus:hsla(207, 100%, 96%, 0.8);--borderColor:hsla(206, 100%, 50%, 1);--borderColorHover:hsla(211, 90%, 34%, 1);--borderColorPress:hsla(209, 100%, 61%, 1);--borderColorFocus:hsla(206, 100%, 50%, 1);--color:hsla(212, 36%, 9%, 1);--colorHover:hsla(216, 50%, 12%, 1);--colorPress:hsla(212, 36%, 9%, 1);--colorFocus:hsla(216, 50%, 12%, 1);--placeholderColor:hsla(214, 65%, 18%, 1);--outlineColor:hsla(214, 35%, 9%, 0.2);--colorTransparent:hsla(214, 35%, 9%, 0);}
  }
.t_dark_blue_SwitchThumb ::selection, .t_dark_blue_SliderThumb ::selection, .t_dark_blue_Tooltip ::selection, .t_dark_blue_ProgressIndicator ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_red_Card, :root.t_dark .t_red_Input, :root.t_dark .t_red_ListItem, :root.t_dark .t_red_Progress, :root.t_dark .t_red_SelectTrigger, :root.t_dark .t_red_SliderTrack, :root.t_dark .t_red_TextArea, :root.t_dark .t_red_TooltipArrow, :root.t_light .t_dark .t_red_Card, :root.t_light .t_dark .t_red_Input, :root.t_light .t_dark .t_red_ListItem, :root.t_light .t_dark .t_red_Progress, :root.t_light .t_dark .t_red_SelectTrigger, :root.t_light .t_dark .t_red_SliderTrack, :root.t_light .t_dark .t_red_TextArea, :root.t_light .t_dark .t_red_TooltipArrow, .tm_xxt {--color:hsla(353, 90%, 96%, 1);--colorHover:hsla(358, 100%, 69%, 1);--colorPress:hsla(353, 90%, 96%, 1);--colorFocus:hsla(358, 100%, 69%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(353, 90%, 96%, 0.2);--background:hsla(357, 34%, 12%, 1);--backgroundHover:hsla(357, 43%, 16%, 1);--backgroundPress:hsla(350, 24%, 10%, 1);--backgroundFocus:hsla(350, 24%, 10%, 1);--borderColor:hsla(356, 51%, 22%, 1);--borderColorHover:hsla(357, 55%, 26%, 1);--borderColorFocus:hsla(356, 51%, 22%, 1);--borderColorPress:hsla(356, 47%, 19%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_red_Card, .t_red_Input, .t_red_ListItem, .t_red_Progress, .t_red_SelectTrigger, .t_red_SliderTrack, .t_red_TextArea, .t_red_TooltipArrow {--color:hsla(353, 90%, 96%, 1);--colorHover:hsla(358, 100%, 69%, 1);--colorPress:hsla(353, 90%, 96%, 1);--colorFocus:hsla(358, 100%, 69%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(353, 90%, 96%, 0.2);--background:hsla(357, 34%, 12%, 1);--backgroundHover:hsla(357, 43%, 16%, 1);--backgroundPress:hsla(350, 24%, 10%, 1);--backgroundFocus:hsla(350, 24%, 10%, 1);--borderColor:hsla(356, 51%, 22%, 1);--borderColorHover:hsla(357, 55%, 26%, 1);--borderColorFocus:hsla(356, 51%, 22%, 1);--borderColorPress:hsla(356, 47%, 19%, 1);}
  }
:root.t_dark .t_red_Button, :root.t_dark .t_red_SliderTrackActive, :root.t_light .t_dark .t_red_Button, :root.t_light .t_dark .t_red_SliderTrackActive, .tm_xxt {--color:hsla(353, 90%, 96%, 1);--colorHover:hsla(358, 100%, 69%, 1);--colorPress:hsla(353, 90%, 96%, 1);--colorFocus:hsla(358, 100%, 69%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(353, 90%, 96%, 0.2);--background:hsla(356, 47%, 19%, 1);--backgroundHover:hsla(356, 51%, 22%, 1);--backgroundPress:hsla(357, 43%, 16%, 1);--backgroundFocus:hsla(357, 43%, 16%, 1);--borderColor:hsla(357, 60%, 32%, 1);--borderColorHover:hsla(358, 65%, 40%, 1);--borderColorFocus:hsla(357, 60%, 32%, 1);--borderColorPress:hsla(357, 55%, 26%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_red_Button, .t_red_SliderTrackActive {--color:hsla(353, 90%, 96%, 1);--colorHover:hsla(358, 100%, 69%, 1);--colorPress:hsla(353, 90%, 96%, 1);--colorFocus:hsla(358, 100%, 69%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(353, 90%, 96%, 0.2);--background:hsla(356, 47%, 19%, 1);--backgroundHover:hsla(356, 51%, 22%, 1);--backgroundPress:hsla(357, 43%, 16%, 1);--backgroundFocus:hsla(357, 43%, 16%, 1);--borderColor:hsla(357, 60%, 32%, 1);--borderColorHover:hsla(358, 65%, 40%, 1);--borderColorFocus:hsla(357, 60%, 32%, 1);--borderColorPress:hsla(357, 55%, 26%, 1);}
  }
:root.t_dark .t_red_Checkbox, :root.t_dark .t_red_RadioGroupItem, :root.t_dark .t_red_Switch, :root.t_dark .t_red_TooltipContent, :root.t_light .t_dark .t_red_Checkbox, :root.t_light .t_dark .t_red_RadioGroupItem, :root.t_light .t_dark .t_red_Switch, :root.t_light .t_dark .t_red_TooltipContent, .tm_xxt {--color:hsla(353, 90%, 96%, 1);--colorHover:hsla(358, 100%, 69%, 1);--colorPress:hsla(353, 90%, 96%, 1);--colorFocus:hsla(358, 100%, 69%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(353, 90%, 96%, 0.2);--background:hsla(357, 43%, 16%, 1);--backgroundHover:hsla(356, 47%, 19%, 1);--backgroundPress:hsla(357, 34%, 12%, 1);--backgroundFocus:hsla(357, 34%, 12%, 1);--borderColor:hsla(357, 55%, 26%, 1);--borderColorHover:hsla(357, 60%, 32%, 1);--borderColorFocus:hsla(357, 55%, 26%, 1);--borderColorPress:hsla(356, 51%, 22%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_red_Checkbox, .t_red_RadioGroupItem, .t_red_Switch, .t_red_TooltipContent {--color:hsla(353, 90%, 96%, 1);--colorHover:hsla(358, 100%, 69%, 1);--colorPress:hsla(353, 90%, 96%, 1);--colorFocus:hsla(358, 100%, 69%, 1);--placeholderColor:hsla(358, 75%, 59%, 1);--outlineColor:hsla(353, 90%, 96%, 0.2);--background:hsla(357, 43%, 16%, 1);--backgroundHover:hsla(356, 47%, 19%, 1);--backgroundPress:hsla(357, 34%, 12%, 1);--backgroundFocus:hsla(357, 34%, 12%, 1);--borderColor:hsla(357, 55%, 26%, 1);--borderColorHover:hsla(357, 60%, 32%, 1);--borderColorFocus:hsla(357, 55%, 26%, 1);--borderColorPress:hsla(356, 51%, 22%, 1);}
  }
:root.t_dark .t_red_ProgressIndicator, :root.t_dark .t_red_SliderThumb, :root.t_dark .t_red_SwitchThumb, :root.t_dark .t_red_Tooltip, :root.t_light .t_dark .t_red_ProgressIndicator, :root.t_light .t_dark .t_red_SliderThumb, :root.t_light .t_dark .t_red_SwitchThumb, :root.t_light .t_dark .t_red_Tooltip, .tm_xxt {--accentBackground:hsla(0, 0%, 93%, 1);--accentColor:hsla(0, 0%, 50%, 1);--background0:hsla(353, 90%, 96%, 0);--background02:hsla(353, 90%, 96%, 0.2);--background04:hsla(353, 90%, 96%, 0.4);--background06:hsla(353, 90%, 96%, 0.6);--background08:hsla(353, 90%, 96%, 0.8);--color1:hsla(353, 90%, 96%, 1);--color2:hsla(358, 100%, 69%, 1);--color3:hsla(358, 86%, 64%, 1);--color4:hsla(358, 75%, 59%, 1);--color5:hsla(358, 65%, 40%, 1);--color6:hsla(357, 60%, 32%, 1);--color7:hsla(357, 55%, 26%, 1);--color8:hsla(356, 51%, 22%, 1);--color9:hsla(356, 47%, 19%, 1);--color10:hsla(357, 43%, 16%, 1);--color11:hsla(357, 34%, 12%, 1);--color12:hsla(350, 24%, 10%, 1);--color0:hsla(351, 25%, 10%, 0);--color02:hsla(351, 25%, 10%, 0.2);--color04:hsla(351, 25%, 10%, 0.4);--color06:hsla(351, 25%, 10%, 0.6);--color08:hsla(351, 25%, 10%, 0.8);--background:hsla(353, 90%, 96%, 1);--backgroundHover:hsla(358, 100%, 69%, 1);--backgroundPress:hsla(353, 90%, 96%, 0.8);--backgroundFocus:hsla(353, 90%, 96%, 0.8);--borderColor:hsla(358, 75%, 59%, 1);--borderColorHover:hsla(358, 65%, 40%, 1);--borderColorPress:hsla(358, 86%, 64%, 1);--borderColorFocus:hsla(358, 75%, 59%, 1);--color:hsla(350, 24%, 10%, 1);--colorHover:hsla(357, 34%, 12%, 1);--colorPress:hsla(350, 24%, 10%, 1);--colorFocus:hsla(357, 34%, 12%, 1);--placeholderColor:hsla(356, 47%, 19%, 1);--outlineColor:hsla(351, 25%, 10%, 0.2);--colorTransparent:hsla(351, 25%, 10%, 0);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_red_ProgressIndicator, .t_red_SliderThumb, .t_red_SwitchThumb, .t_red_Tooltip {--accentBackground:hsla(0, 0%, 93%, 1);--accentColor:hsla(0, 0%, 50%, 1);--background0:hsla(353, 90%, 96%, 0);--background02:hsla(353, 90%, 96%, 0.2);--background04:hsla(353, 90%, 96%, 0.4);--background06:hsla(353, 90%, 96%, 0.6);--background08:hsla(353, 90%, 96%, 0.8);--color1:hsla(353, 90%, 96%, 1);--color2:hsla(358, 100%, 69%, 1);--color3:hsla(358, 86%, 64%, 1);--color4:hsla(358, 75%, 59%, 1);--color5:hsla(358, 65%, 40%, 1);--color6:hsla(357, 60%, 32%, 1);--color7:hsla(357, 55%, 26%, 1);--color8:hsla(356, 51%, 22%, 1);--color9:hsla(356, 47%, 19%, 1);--color10:hsla(357, 43%, 16%, 1);--color11:hsla(357, 34%, 12%, 1);--color12:hsla(350, 24%, 10%, 1);--color0:hsla(351, 25%, 10%, 0);--color02:hsla(351, 25%, 10%, 0.2);--color04:hsla(351, 25%, 10%, 0.4);--color06:hsla(351, 25%, 10%, 0.6);--color08:hsla(351, 25%, 10%, 0.8);--background:hsla(353, 90%, 96%, 1);--backgroundHover:hsla(358, 100%, 69%, 1);--backgroundPress:hsla(353, 90%, 96%, 0.8);--backgroundFocus:hsla(353, 90%, 96%, 0.8);--borderColor:hsla(358, 75%, 59%, 1);--borderColorHover:hsla(358, 65%, 40%, 1);--borderColorPress:hsla(358, 86%, 64%, 1);--borderColorFocus:hsla(358, 75%, 59%, 1);--color:hsla(350, 24%, 10%, 1);--colorHover:hsla(357, 34%, 12%, 1);--colorPress:hsla(350, 24%, 10%, 1);--colorFocus:hsla(357, 34%, 12%, 1);--placeholderColor:hsla(356, 47%, 19%, 1);--outlineColor:hsla(351, 25%, 10%, 0.2);--colorTransparent:hsla(351, 25%, 10%, 0);}
  }
.t_dark_red_SwitchThumb ::selection, .t_dark_red_SliderThumb ::selection, .t_dark_red_Tooltip ::selection, .t_dark_red_ProgressIndicator ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_yellow_Card, :root.t_dark .t_yellow_Input, :root.t_dark .t_yellow_ListItem, :root.t_dark .t_yellow_Progress, :root.t_dark .t_yellow_SelectTrigger, :root.t_dark .t_yellow_SliderTrack, :root.t_dark .t_yellow_TextArea, :root.t_dark .t_yellow_TooltipArrow, :root.t_light .t_dark .t_yellow_Card, :root.t_light .t_dark .t_yellow_Input, :root.t_light .t_dark .t_yellow_ListItem, :root.t_light .t_dark .t_yellow_Progress, :root.t_light .t_dark .t_yellow_SelectTrigger, :root.t_light .t_dark .t_yellow_SliderTrack, :root.t_light .t_dark .t_yellow_TextArea, :root.t_light .t_dark .t_yellow_TooltipArrow, .tm_xxt {--color:hsla(53, 100%, 91%, 1);--colorHover:hsla(48, 100%, 47%, 1);--colorPress:hsla(53, 100%, 91%, 1);--colorFocus:hsla(48, 100%, 47%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(53, 100%, 91%, 0.2);--background:hsla(46, 100%, 7%, 1);--backgroundHover:hsla(45, 100%, 9%, 1);--backgroundPress:hsla(45, 100%, 5%, 1);--backgroundFocus:hsla(45, 100%, 5%, 1);--borderColor:hsla(46, 100%, 12%, 1);--borderColorHover:hsla(49, 100%, 14%, 1);--borderColorFocus:hsla(46, 100%, 12%, 1);--borderColorPress:hsla(45, 100%, 10%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_yellow_Card, .t_yellow_Input, .t_yellow_ListItem, .t_yellow_Progress, .t_yellow_SelectTrigger, .t_yellow_SliderTrack, .t_yellow_TextArea, .t_yellow_TooltipArrow {--color:hsla(53, 100%, 91%, 1);--colorHover:hsla(48, 100%, 47%, 1);--colorPress:hsla(53, 100%, 91%, 1);--colorFocus:hsla(48, 100%, 47%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(53, 100%, 91%, 0.2);--background:hsla(46, 100%, 7%, 1);--backgroundHover:hsla(45, 100%, 9%, 1);--backgroundPress:hsla(45, 100%, 5%, 1);--backgroundFocus:hsla(45, 100%, 5%, 1);--borderColor:hsla(46, 100%, 12%, 1);--borderColorHover:hsla(49, 100%, 14%, 1);--borderColorFocus:hsla(46, 100%, 12%, 1);--borderColorPress:hsla(45, 100%, 10%, 1);}
  }
:root.t_dark .t_yellow_Button, :root.t_dark .t_yellow_SliderTrackActive, :root.t_light .t_dark .t_yellow_Button, :root.t_light .t_dark .t_yellow_SliderTrackActive, .tm_xxt {--color:hsla(53, 100%, 91%, 1);--colorHover:hsla(48, 100%, 47%, 1);--colorPress:hsla(53, 100%, 91%, 1);--colorFocus:hsla(48, 100%, 47%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(53, 100%, 91%, 0.2);--background:hsla(45, 100%, 10%, 1);--backgroundHover:hsla(46, 100%, 12%, 1);--backgroundPress:hsla(45, 100%, 9%, 1);--backgroundFocus:hsla(45, 100%, 9%, 1);--borderColor:hsla(49, 89%, 18%, 1);--borderColorHover:hsla(50, 100%, 22%, 1);--borderColorFocus:hsla(49, 89%, 18%, 1);--borderColorPress:hsla(49, 100%, 14%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_yellow_Button, .t_yellow_SliderTrackActive {--color:hsla(53, 100%, 91%, 1);--colorHover:hsla(48, 100%, 47%, 1);--colorPress:hsla(53, 100%, 91%, 1);--colorFocus:hsla(48, 100%, 47%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(53, 100%, 91%, 0.2);--background:hsla(45, 100%, 10%, 1);--backgroundHover:hsla(46, 100%, 12%, 1);--backgroundPress:hsla(45, 100%, 9%, 1);--backgroundFocus:hsla(45, 100%, 9%, 1);--borderColor:hsla(49, 89%, 18%, 1);--borderColorHover:hsla(50, 100%, 22%, 1);--borderColorFocus:hsla(49, 89%, 18%, 1);--borderColorPress:hsla(49, 100%, 14%, 1);}
  }
:root.t_dark .t_yellow_Checkbox, :root.t_dark .t_yellow_RadioGroupItem, :root.t_dark .t_yellow_Switch, :root.t_dark .t_yellow_TooltipContent, :root.t_light .t_dark .t_yellow_Checkbox, :root.t_light .t_dark .t_yellow_RadioGroupItem, :root.t_light .t_dark .t_yellow_Switch, :root.t_light .t_dark .t_yellow_TooltipContent, .tm_xxt {--color:hsla(53, 100%, 91%, 1);--colorHover:hsla(48, 100%, 47%, 1);--colorPress:hsla(53, 100%, 91%, 1);--colorFocus:hsla(48, 100%, 47%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(53, 100%, 91%, 0.2);--background:hsla(45, 100%, 9%, 1);--backgroundHover:hsla(45, 100%, 10%, 1);--backgroundPress:hsla(46, 100%, 7%, 1);--backgroundFocus:hsla(46, 100%, 7%, 1);--borderColor:hsla(49, 100%, 14%, 1);--borderColorHover:hsla(49, 89%, 18%, 1);--borderColorFocus:hsla(49, 100%, 14%, 1);--borderColorPress:hsla(46, 100%, 12%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_yellow_Checkbox, .t_yellow_RadioGroupItem, .t_yellow_Switch, .t_yellow_TooltipContent {--color:hsla(53, 100%, 91%, 1);--colorHover:hsla(48, 100%, 47%, 1);--colorPress:hsla(53, 100%, 91%, 1);--colorFocus:hsla(48, 100%, 47%, 1);--placeholderColor:hsla(53, 92%, 50%, 1);--outlineColor:hsla(53, 100%, 91%, 0.2);--background:hsla(45, 100%, 9%, 1);--backgroundHover:hsla(45, 100%, 10%, 1);--backgroundPress:hsla(46, 100%, 7%, 1);--backgroundFocus:hsla(46, 100%, 7%, 1);--borderColor:hsla(49, 100%, 14%, 1);--borderColorHover:hsla(49, 89%, 18%, 1);--borderColorFocus:hsla(49, 100%, 14%, 1);--borderColorPress:hsla(46, 100%, 12%, 1);}
  }
:root.t_dark .t_yellow_ProgressIndicator, :root.t_dark .t_yellow_SliderThumb, :root.t_dark .t_yellow_SwitchThumb, :root.t_dark .t_yellow_Tooltip, :root.t_light .t_dark .t_yellow_ProgressIndicator, :root.t_light .t_dark .t_yellow_SliderThumb, :root.t_light .t_dark .t_yellow_SwitchThumb, :root.t_light .t_dark .t_yellow_Tooltip, .tm_xxt {--accentBackground:hsla(0, 0%, 93%, 1);--accentColor:hsla(0, 0%, 50%, 1);--background0:hsla(53, 100%, 91%, 0);--background02:hsla(53, 100%, 91%, 0.2);--background04:hsla(53, 100%, 91%, 0.4);--background06:hsla(53, 100%, 91%, 0.6);--background08:hsla(53, 100%, 91%, 0.8);--color1:hsla(53, 100%, 91%, 1);--color2:hsla(48, 100%, 47%, 1);--color3:hsla(54, 100%, 68%, 1);--color4:hsla(53, 92%, 50%, 1);--color5:hsla(50, 100%, 22%, 1);--color6:hsla(49, 89%, 18%, 1);--color7:hsla(49, 100%, 14%, 1);--color8:hsla(46, 100%, 12%, 1);--color9:hsla(45, 100%, 10%, 1);--color10:hsla(45, 100%, 9%, 1);--color11:hsla(46, 100%, 7%, 1);--color12:hsla(45, 100%, 5%, 1);--color0:hsla(46, 100%, 5%, 0);--color02:hsla(46, 100%, 5%, 0.2);--color04:hsla(46, 100%, 5%, 0.4);--color06:hsla(46, 100%, 5%, 0.6);--color08:hsla(46, 100%, 5%, 0.8);--background:hsla(53, 100%, 91%, 1);--backgroundHover:hsla(48, 100%, 47%, 1);--backgroundPress:hsla(53, 100%, 91%, 0.8);--backgroundFocus:hsla(53, 100%, 91%, 0.8);--borderColor:hsla(53, 92%, 50%, 1);--borderColorHover:hsla(50, 100%, 22%, 1);--borderColorPress:hsla(54, 100%, 68%, 1);--borderColorFocus:hsla(53, 92%, 50%, 1);--color:hsla(45, 100%, 5%, 1);--colorHover:hsla(46, 100%, 7%, 1);--colorPress:hsla(45, 100%, 5%, 1);--colorFocus:hsla(46, 100%, 7%, 1);--placeholderColor:hsla(45, 100%, 10%, 1);--outlineColor:hsla(46, 100%, 5%, 0.2);--colorTransparent:hsla(46, 100%, 5%, 0);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_yellow_ProgressIndicator, .t_yellow_SliderThumb, .t_yellow_SwitchThumb, .t_yellow_Tooltip {--accentBackground:hsla(0, 0%, 93%, 1);--accentColor:hsla(0, 0%, 50%, 1);--background0:hsla(53, 100%, 91%, 0);--background02:hsla(53, 100%, 91%, 0.2);--background04:hsla(53, 100%, 91%, 0.4);--background06:hsla(53, 100%, 91%, 0.6);--background08:hsla(53, 100%, 91%, 0.8);--color1:hsla(53, 100%, 91%, 1);--color2:hsla(48, 100%, 47%, 1);--color3:hsla(54, 100%, 68%, 1);--color4:hsla(53, 92%, 50%, 1);--color5:hsla(50, 100%, 22%, 1);--color6:hsla(49, 89%, 18%, 1);--color7:hsla(49, 100%, 14%, 1);--color8:hsla(46, 100%, 12%, 1);--color9:hsla(45, 100%, 10%, 1);--color10:hsla(45, 100%, 9%, 1);--color11:hsla(46, 100%, 7%, 1);--color12:hsla(45, 100%, 5%, 1);--color0:hsla(46, 100%, 5%, 0);--color02:hsla(46, 100%, 5%, 0.2);--color04:hsla(46, 100%, 5%, 0.4);--color06:hsla(46, 100%, 5%, 0.6);--color08:hsla(46, 100%, 5%, 0.8);--background:hsla(53, 100%, 91%, 1);--backgroundHover:hsla(48, 100%, 47%, 1);--backgroundPress:hsla(53, 100%, 91%, 0.8);--backgroundFocus:hsla(53, 100%, 91%, 0.8);--borderColor:hsla(53, 92%, 50%, 1);--borderColorHover:hsla(50, 100%, 22%, 1);--borderColorPress:hsla(54, 100%, 68%, 1);--borderColorFocus:hsla(53, 92%, 50%, 1);--color:hsla(45, 100%, 5%, 1);--colorHover:hsla(46, 100%, 7%, 1);--colorPress:hsla(45, 100%, 5%, 1);--colorFocus:hsla(46, 100%, 7%, 1);--placeholderColor:hsla(45, 100%, 10%, 1);--outlineColor:hsla(46, 100%, 5%, 0.2);--colorTransparent:hsla(46, 100%, 5%, 0);}
  }
.t_dark_yellow_SwitchThumb ::selection, .t_dark_yellow_SliderThumb ::selection, .t_dark_yellow_Tooltip ::selection, .t_dark_yellow_ProgressIndicator ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_green_Card, :root.t_dark .t_green_Input, :root.t_dark .t_green_ListItem, :root.t_dark .t_green_Progress, :root.t_dark .t_green_SelectTrigger, :root.t_dark .t_green_SliderTrack, :root.t_dark .t_green_TextArea, :root.t_dark .t_green_TooltipArrow, :root.t_light .t_dark .t_green_Card, :root.t_light .t_dark .t_green_Input, :root.t_light .t_dark .t_green_ListItem, :root.t_light .t_dark .t_green_Progress, :root.t_light .t_dark .t_green_SelectTrigger, :root.t_light .t_dark .t_green_SliderTrack, :root.t_light .t_dark .t_green_TextArea, :root.t_light .t_dark .t_green_TooltipArrow, .tm_xxt {--color:hsla(136, 73%, 94%, 1);--colorHover:hsla(151, 50%, 53%, 1);--colorPress:hsla(136, 73%, 94%, 1);--colorFocus:hsla(151, 50%, 53%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(134, 73%, 94%, 0.2);--background:hsla(155, 44%, 8%, 1);--backgroundHover:hsla(155, 46%, 11%, 1);--backgroundPress:hsla(145, 32%, 7%, 1);--backgroundFocus:hsla(145, 32%, 7%, 1);--borderColor:hsla(155, 50%, 15%, 1);--borderColorHover:hsla(154, 51%, 18%, 1);--borderColorFocus:hsla(155, 50%, 15%, 1);--borderColorPress:hsla(154, 48%, 13%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_green_Card, .t_green_Input, .t_green_ListItem, .t_green_Progress, .t_green_SelectTrigger, .t_green_SliderTrack, .t_green_TextArea, .t_green_TooltipArrow {--color:hsla(136, 73%, 94%, 1);--colorHover:hsla(151, 50%, 53%, 1);--colorPress:hsla(136, 73%, 94%, 1);--colorFocus:hsla(151, 50%, 53%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(134, 73%, 94%, 0.2);--background:hsla(155, 44%, 8%, 1);--backgroundHover:hsla(155, 46%, 11%, 1);--backgroundPress:hsla(145, 32%, 7%, 1);--backgroundFocus:hsla(145, 32%, 7%, 1);--borderColor:hsla(155, 50%, 15%, 1);--borderColorHover:hsla(154, 51%, 18%, 1);--borderColorFocus:hsla(155, 50%, 15%, 1);--borderColorPress:hsla(154, 48%, 13%, 1);}
  }
:root.t_dark .t_green_Button, :root.t_dark .t_green_SliderTrackActive, :root.t_light .t_dark .t_green_Button, :root.t_light .t_dark .t_green_SliderTrackActive, .tm_xxt {--color:hsla(136, 73%, 94%, 1);--colorHover:hsla(151, 50%, 53%, 1);--colorPress:hsla(136, 73%, 94%, 1);--colorFocus:hsla(151, 50%, 53%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(134, 73%, 94%, 0.2);--background:hsla(154, 48%, 13%, 1);--backgroundHover:hsla(155, 50%, 15%, 1);--backgroundPress:hsla(155, 46%, 11%, 1);--backgroundFocus:hsla(155, 46%, 11%, 1);--borderColor:hsla(153, 51%, 22%, 1);--borderColorHover:hsla(151, 52%, 28%, 1);--borderColorFocus:hsla(153, 51%, 22%, 1);--borderColorPress:hsla(154, 51%, 18%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_green_Button, .t_green_SliderTrackActive {--color:hsla(136, 73%, 94%, 1);--colorHover:hsla(151, 50%, 53%, 1);--colorPress:hsla(136, 73%, 94%, 1);--colorFocus:hsla(151, 50%, 53%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(134, 73%, 94%, 0.2);--background:hsla(154, 48%, 13%, 1);--backgroundHover:hsla(155, 50%, 15%, 1);--backgroundPress:hsla(155, 46%, 11%, 1);--backgroundFocus:hsla(155, 46%, 11%, 1);--borderColor:hsla(153, 51%, 22%, 1);--borderColorHover:hsla(151, 52%, 28%, 1);--borderColorFocus:hsla(153, 51%, 22%, 1);--borderColorPress:hsla(154, 51%, 18%, 1);}
  }
:root.t_dark .t_green_Checkbox, :root.t_dark .t_green_RadioGroupItem, :root.t_dark .t_green_Switch, :root.t_dark .t_green_TooltipContent, :root.t_light .t_dark .t_green_Checkbox, :root.t_light .t_dark .t_green_RadioGroupItem, :root.t_light .t_dark .t_green_Switch, :root.t_light .t_dark .t_green_TooltipContent, .tm_xxt {--color:hsla(136, 73%, 94%, 1);--colorHover:hsla(151, 50%, 53%, 1);--colorPress:hsla(136, 73%, 94%, 1);--colorFocus:hsla(151, 50%, 53%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(134, 73%, 94%, 0.2);--background:hsla(155, 46%, 11%, 1);--backgroundHover:hsla(154, 48%, 13%, 1);--backgroundPress:hsla(155, 44%, 8%, 1);--backgroundFocus:hsla(155, 44%, 8%, 1);--borderColor:hsla(154, 51%, 18%, 1);--borderColorHover:hsla(153, 51%, 22%, 1);--borderColorFocus:hsla(154, 51%, 18%, 1);--borderColorPress:hsla(155, 50%, 15%, 1);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_green_Checkbox, .t_green_RadioGroupItem, .t_green_Switch, .t_green_TooltipContent {--color:hsla(136, 73%, 94%, 1);--colorHover:hsla(151, 50%, 53%, 1);--colorPress:hsla(136, 73%, 94%, 1);--colorFocus:hsla(151, 50%, 53%, 1);--placeholderColor:hsla(151, 55%, 42%, 1);--outlineColor:hsla(134, 73%, 94%, 0.2);--background:hsla(155, 46%, 11%, 1);--backgroundHover:hsla(154, 48%, 13%, 1);--backgroundPress:hsla(155, 44%, 8%, 1);--backgroundFocus:hsla(155, 44%, 8%, 1);--borderColor:hsla(154, 51%, 18%, 1);--borderColorHover:hsla(153, 51%, 22%, 1);--borderColorFocus:hsla(154, 51%, 18%, 1);--borderColorPress:hsla(155, 50%, 15%, 1);}
  }
:root.t_dark .t_green_ProgressIndicator, :root.t_dark .t_green_SliderThumb, :root.t_dark .t_green_SwitchThumb, :root.t_dark .t_green_Tooltip, :root.t_light .t_dark .t_green_ProgressIndicator, :root.t_light .t_dark .t_green_SliderThumb, :root.t_light .t_dark .t_green_SwitchThumb, :root.t_light .t_dark .t_green_Tooltip, .tm_xxt {--accentBackground:hsla(0, 0%, 93%, 1);--accentColor:hsla(0, 0%, 50%, 1);--background0:hsla(134, 73%, 94%, 0);--background02:hsla(134, 73%, 94%, 0.2);--background04:hsla(134, 73%, 94%, 0.4);--background06:hsla(134, 73%, 94%, 0.6);--background08:hsla(134, 73%, 94%, 0.8);--color1:hsla(136, 73%, 94%, 1);--color2:hsla(151, 50%, 53%, 1);--color3:hsla(151, 49%, 46%, 1);--color4:hsla(151, 55%, 42%, 1);--color5:hsla(151, 52%, 28%, 1);--color6:hsla(153, 51%, 22%, 1);--color7:hsla(154, 51%, 18%, 1);--color8:hsla(155, 50%, 15%, 1);--color9:hsla(154, 48%, 13%, 1);--color10:hsla(155, 46%, 11%, 1);--color11:hsla(155, 44%, 8%, 1);--color12:hsla(145, 32%, 7%, 1);--color0:hsla(145, 33%, 7%, 0);--color02:hsla(145, 33%, 7%, 0.2);--color04:hsla(145, 33%, 7%, 0.4);--color06:hsla(145, 33%, 7%, 0.6);--color08:hsla(145, 33%, 7%, 0.8);--background:hsla(136, 73%, 94%, 1);--backgroundHover:hsla(151, 50%, 53%, 1);--backgroundPress:hsla(134, 73%, 94%, 0.8);--backgroundFocus:hsla(134, 73%, 94%, 0.8);--borderColor:hsla(151, 55%, 42%, 1);--borderColorHover:hsla(151, 52%, 28%, 1);--borderColorPress:hsla(151, 49%, 46%, 1);--borderColorFocus:hsla(151, 55%, 42%, 1);--color:hsla(145, 32%, 7%, 1);--colorHover:hsla(155, 44%, 8%, 1);--colorPress:hsla(145, 32%, 7%, 1);--colorFocus:hsla(155, 44%, 8%, 1);--placeholderColor:hsla(154, 48%, 13%, 1);--outlineColor:hsla(145, 33%, 7%, 0.2);--colorTransparent:hsla(145, 33%, 7%, 0);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_green_ProgressIndicator, .t_green_SliderThumb, .t_green_SwitchThumb, .t_green_Tooltip {--accentBackground:hsla(0, 0%, 93%, 1);--accentColor:hsla(0, 0%, 50%, 1);--background0:hsla(134, 73%, 94%, 0);--background02:hsla(134, 73%, 94%, 0.2);--background04:hsla(134, 73%, 94%, 0.4);--background06:hsla(134, 73%, 94%, 0.6);--background08:hsla(134, 73%, 94%, 0.8);--color1:hsla(136, 73%, 94%, 1);--color2:hsla(151, 50%, 53%, 1);--color3:hsla(151, 49%, 46%, 1);--color4:hsla(151, 55%, 42%, 1);--color5:hsla(151, 52%, 28%, 1);--color6:hsla(153, 51%, 22%, 1);--color7:hsla(154, 51%, 18%, 1);--color8:hsla(155, 50%, 15%, 1);--color9:hsla(154, 48%, 13%, 1);--color10:hsla(155, 46%, 11%, 1);--color11:hsla(155, 44%, 8%, 1);--color12:hsla(145, 32%, 7%, 1);--color0:hsla(145, 33%, 7%, 0);--color02:hsla(145, 33%, 7%, 0.2);--color04:hsla(145, 33%, 7%, 0.4);--color06:hsla(145, 33%, 7%, 0.6);--color08:hsla(145, 33%, 7%, 0.8);--background:hsla(136, 73%, 94%, 1);--backgroundHover:hsla(151, 50%, 53%, 1);--backgroundPress:hsla(134, 73%, 94%, 0.8);--backgroundFocus:hsla(134, 73%, 94%, 0.8);--borderColor:hsla(151, 55%, 42%, 1);--borderColorHover:hsla(151, 52%, 28%, 1);--borderColorPress:hsla(151, 49%, 46%, 1);--borderColorFocus:hsla(151, 55%, 42%, 1);--color:hsla(145, 32%, 7%, 1);--colorHover:hsla(155, 44%, 8%, 1);--colorPress:hsla(145, 32%, 7%, 1);--colorFocus:hsla(155, 44%, 8%, 1);--placeholderColor:hsla(154, 48%, 13%, 1);--outlineColor:hsla(145, 33%, 7%, 0.2);--colorTransparent:hsla(145, 33%, 7%, 0);}
  }
.t_dark_green_SwitchThumb ::selection, .t_dark_green_SliderThumb ::selection, .t_dark_green_Tooltip ::selection, .t_dark_green_ProgressIndicator ::selection{background:var(--color5);color:var(--color11)}
  