/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/convert"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fconvert.tsx&page=%2Fwallet%2Fconvert!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fconvert.tsx&page=%2Fwallet%2Fconvert! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/convert\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/convert.tsx */ \"./pages/wallet/convert.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/convert\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRmNvbnZlcnQudHN4JnBhZ2U9JTJGd2FsbGV0JTJGY29udmVydCEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyw4REFBNEI7QUFDbkQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvP2YzYjEiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi93YWxsZXQvY29udmVydFwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L2NvbnZlcnQudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi93YWxsZXQvY29udmVydFwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fconvert.tsx&page=%2Fwallet%2Fconvert!\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/buy2.png":
/*!****************************************************!*\
  !*** ../../packages/assets/images/wallet/buy2.png ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/buy2.1cda78b1.png\",\"height\":56,\"width\":56,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbuy2.1cda78b1.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvYnV5Mi5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsMExBQTBMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9idXkyLnBuZz8zOWE2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9idXkyLjFjZGE3OGIxLnBuZ1wiLFwiaGVpZ2h0XCI6NTYsXCJ3aWR0aFwiOjU2LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJ1eTIuMWNkYTc4YjEucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/buy2.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/net3.png":
/*!****************************************************!*\
  !*** ../../packages/assets/images/wallet/net3.png ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/net3.4cb3a483.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnet3.4cb3a483.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvbmV0My5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsMExBQTBMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9uZXQzLnBuZz82YzczIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9uZXQzLjRjYjNhNDgzLnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRm5ldDMuNGNiM2E0ODMucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/net3.png\n"));

/***/ }),

/***/ "./pages/wallet/convert.tsx":
/*!**********************************!*\
  !*** ./pages/wallet/convert.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_convert_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/convert-screen */ \"../../packages/app/features/wallet/convert-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_convert_screen__WEBPACK_IMPORTED_MODULE_1__.ConvertScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/convert.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvY29udmVydC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDa0U7QUFFbkQsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELDZFQUFhQTs7Ozs7QUFDdkI7S0FGd0JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3BhZ2VzL3dhbGxldC9jb252ZXJ0LnRzeD8yZDk0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0IHsgQ29udmVydFNjcmVlbiB9IGZyb20gJ2FwcC9mZWF0dXJlcy93YWxsZXQvY29udmVydC1zY3JlZW4nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XG4gIHJldHVybiA8Q29udmVydFNjcmVlbiAvPlxufSJdLCJuYW1lcyI6WyJDb252ZXJ0U2NyZWVuIiwiUGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/wallet/convert.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/convert-screen.tsx":
/*!*************************************************************!*\
  !*** ../../packages/app/features/wallet/convert-screen.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConvertScreen: function() { return /* binding */ ConvertScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_net3_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/net3.png */ \"../../packages/assets/images/wallet/net3.png\");\n/* harmony import */ var _assets_images_wallet_buy2_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/buy2.png */ \"../../packages/assets/images/wallet/buy2.png\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_3__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 60\n});\n_c = Underline;\nfunction ConvertScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.YStack, {\n        \"data-at\": \"convert-screen.tsx:23\",\n        \"data-in\": \"ConvertScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        position: \"relative\",\n        height: 800,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                \"data-at\": \"convert-screen.tsx:24\",\n                \"data-in\": \"ConvertScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.NavBar, {\n                        title: \"兑换\",\n                        onBack: ()=>router.back()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Image, {\n                        \"data-at\": \"convert-screen.tsx:26\",\n                        \"data-in\": \"ConvertScreen\",\n                        \"data-is\": \"Image\",\n                        source: _assets_images_wallet_net3_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                        width: 16,\n                        height: 16\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.YStack, {\n                \"data-at\": \"convert-screen.tsx:28\",\n                \"data-in\": \"ConvertScreen\",\n                \"data-is\": \"YStack\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                        \"data-at\": \"convert-screen.tsx:29\",\n                        \"data-in\": \"ConvertScreen\",\n                        \"data-is\": \"XStack\",\n                        mt: 60,\n                        position: \"relative\",\n                        px: 16,\n                        items: \"center\",\n                        justifyContent: \"center\",\n                        height: 100,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                            \"data-at\": \"convert-screen.tsx:30\",\n                            \"data-in\": \"ConvertScreen\",\n                            \"data-is\": \"Text\",\n                            color: \"#3C72F9\",\n                            fontSize: 60,\n                            textAlign: \"center\",\n                            children: \"$0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                        \"data-at\": \"convert-screen.tsx:32\",\n                        \"data-in\": \"ConvertScreen\",\n                        \"data-is\": \"XStack\",\n                        mt: 80,\n                        items: \"center\",\n                        justifyContent: \"space-between\",\n                        px: 16,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                                \"data-at\": \"convert-screen.tsx:33\",\n                                \"data-in\": \"ConvertScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Image, {\n                                        \"data-at\": \"convert-screen.tsx:34\",\n                                        \"data-in\": \"ConvertScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: {\n                                            uri: \"\"\n                                        },\n                                        width: 28,\n                                        height: 28,\n                                        rounded: 14,\n                                        bg: \"$accent11\",\n                                        mr: 6\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                        \"data-at\": \"convert-screen.tsx:35\",\n                                        \"data-in\": \"ConvertScreen\",\n                                        \"data-is\": \"View\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                \"data-at\": \"convert-screen.tsx:36\",\n                                                \"data-in\": \"ConvertScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"white\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                children: \"从\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                \"data-at\": \"convert-screen.tsx:37\",\n                                                \"data-in\": \"ConvertScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"$accent11\",\n                                                fontSize: 12,\n                                                fontWeight: 500,\n                                                children: \"选择资产\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                                \"data-at\": \"convert-screen.tsx:40\",\n                                \"data-in\": \"ConvertScreen\",\n                                \"data-is\": \"XStack\",\n                                justifyContent: \"flex-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                        \"data-at\": \"convert-screen.tsx:41\",\n                                        \"data-in\": \"ConvertScreen\",\n                                        \"data-is\": \"View\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_6__.ChevronRight, {\n                                        size: 20,\n                                        color: \"$white6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                        \"data-at\": \"convert-screen.tsx:45\",\n                        \"data-in\": \"ConvertScreen\",\n                        \"data-is\": \"View\",\n                        width: 1,\n                        height: 16,\n                        bg: \"$black10\",\n                        ml: 30,\n                        mt: 10\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                        \"data-at\": \"convert-screen.tsx:46\",\n                        \"data-in\": \"ConvertScreen\",\n                        \"data-is\": \"XStack\",\n                        mt: 10,\n                        items: \"center\",\n                        justifyContent: \"space-between\",\n                        px: 16,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                                \"data-at\": \"convert-screen.tsx:47\",\n                                \"data-in\": \"ConvertScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Image, {\n                                        \"data-at\": \"convert-screen.tsx:48\",\n                                        \"data-in\": \"ConvertScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_buy2_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                        width: 28,\n                                        height: 28,\n                                        rounded: 14,\n                                        bg: \"$accent11\",\n                                        mr: 6\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                        \"data-at\": \"convert-screen.tsx:49\",\n                                        \"data-in\": \"ConvertScreen\",\n                                        \"data-is\": \"View\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                \"data-at\": \"convert-screen.tsx:50\",\n                                                \"data-in\": \"ConvertScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"white\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                children: \"到 KTA\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                \"data-at\": \"convert-screen.tsx:51\",\n                                                \"data-in\": \"ConvertScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"$accent11\",\n                                                fontSize: 12,\n                                                fontWeight: 500,\n                                                children: \"在 Base 上\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                                \"data-at\": \"convert-screen.tsx:54\",\n                                \"data-in\": \"ConvertScreen\",\n                                \"data-is\": \"XStack\",\n                                justifyContent: \"flex-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                        \"data-at\": \"convert-screen.tsx:55\",\n                                        \"data-in\": \"ConvertScreen\",\n                                        \"data-is\": \"View\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_6__.ChevronRight, {\n                                        size: 20,\n                                        color: \"$white6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                \"data-at\": \"convert-screen.tsx:62-69\",\n                \"data-in\": \"ConvertScreen\",\n                \"data-is\": \"Button\",\n                position: \"absolute\",\n                bottom: 20,\n                ml: 16,\n                rounded: 30,\n                width: \"92%\",\n                style: {\n                    // backgroundColor: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',\n                    background: \"linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)\"\n                },\n                onPress: ()=>{\n                    router.push(\"/wallet/convertAssets\");\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                    \"data-at\": \"convert-screen.tsx:70\",\n                    \"data-in\": \"ConvertScreen\",\n                    \"data-is\": \"Text\",\n                    color: \"$black1\",\n                    fontSize: 14,\n                    fontWeight: \"bold\",\n                    children: \"将加密货币添加到您的钱包\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/convert-screen.tsx\",\n        lineNumber: 17,\n        columnNumber: 10\n    }, this);\n}\n_s(ConvertScreen, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c1 = ConvertScreen;\nvar _c, _c1;\n$RefreshReg$(_c, \"Underline\");\n$RefreshReg$(_c1, \"ConvertScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/convert-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fconvert.tsx&page=%2Fwallet%2Fconvert!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);