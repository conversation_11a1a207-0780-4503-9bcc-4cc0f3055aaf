/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/network"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fnetwork.tsx&page=%2Fwallet%2Fnetwork!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fnetwork.tsx&page=%2Fwallet%2Fnetwork! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/network\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/network.tsx */ \"./pages/wallet/network.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/network\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRm5ldHdvcmsudHN4JnBhZ2U9JTJGd2FsbGV0JTJGbmV0d29yayEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyw4REFBNEI7QUFDbkQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzdjMDciXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi93YWxsZXQvbmV0d29ya1wiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L25ldHdvcmsudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi93YWxsZXQvbmV0d29ya1wiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fnetwork.tsx&page=%2Fwallet%2Fnetwork!\n"));

/***/ }),

/***/ "../../packages/assets/images/close.png":
/*!**********************************************!*\
  !*** ../../packages/assets/images/close.png ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/close.41136e88.png\",\"height\":24,\"width\":24,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fclose.41136e88.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9jbG9zZS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsNExBQTRMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL2Nsb3NlLnBuZz81NzdkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9jbG9zZS40MTEzNmU4OC5wbmdcIixcImhlaWdodFwiOjI0LFwid2lkdGhcIjoyNCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZjbG9zZS40MTEzNmU4OC5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/close.png\n"));

/***/ }),

/***/ "../../packages/assets/images/copy.png":
/*!*********************************************!*\
  !*** ../../packages/assets/images/copy.png ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/copy.7685cfef.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcopy.7685cfef.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9jb3B5LnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQywwTEFBMEwiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvY29weS5wbmc/NmVkMiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvY29weS43Njg1Y2ZlZi5wbmdcIixcImhlaWdodFwiOjMyLFwid2lkdGhcIjozMixcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZjb3B5Ljc2ODVjZmVmLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/copy.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-1-active.png":
/*!**********************************************************!*\
  !*** ../../packages/assets/images/fot-icon-1-active.png ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-1-active.e535a13f.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-1-active.e535a13f.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi0xLWFjdGl2ZS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsb05BQW9OIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL2ZvdC1pY29uLTEtYWN0aXZlLnBuZz8yNzcwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mb3QtaWNvbi0xLWFjdGl2ZS5lNTM1YTEzZi5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi0xLWFjdGl2ZS5lNTM1YTEzZi5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-1-active.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-1.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/fot-icon-1.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-1.ea89c0a4.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-1.ea89c0a4.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi0xLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxzTUFBc00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvZm90LWljb24tMS5wbmc/MGM4NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZm90LWljb24tMS5lYTg5YzBhNC5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi0xLmVhODljMGE0LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-1.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-2-active.png":
/*!**********************************************************!*\
  !*** ../../packages/assets/images/fot-icon-2-active.png ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-2-active.5e386890.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-2-active.5e386890.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi0yLWFjdGl2ZS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsb05BQW9OIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL2ZvdC1pY29uLTItYWN0aXZlLnBuZz9iMGFjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mb3QtaWNvbi0yLWFjdGl2ZS41ZTM4Njg5MC5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi0yLWFjdGl2ZS41ZTM4Njg5MC5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-2-active.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-2.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/fot-icon-2.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-2.ca442dfc.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-2.ca442dfc.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi0yLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxzTUFBc00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvZm90LWljb24tMi5wbmc/MmMxZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZm90LWljb24tMi5jYTQ0MmRmYy5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi0yLmNhNDQyZGZjLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-2.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-3-active.png":
/*!**********************************************************!*\
  !*** ../../packages/assets/images/fot-icon-3-active.png ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-3-active.d4c8b8ea.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-3-active.d4c8b8ea.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi0zLWFjdGl2ZS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsb05BQW9OIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL2ZvdC1pY29uLTMtYWN0aXZlLnBuZz8yZjc4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mb3QtaWNvbi0zLWFjdGl2ZS5kNGM4YjhlYS5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi0zLWFjdGl2ZS5kNGM4YjhlYS5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-3-active.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-3.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/fot-icon-3.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-3.8beb2569.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-3.8beb2569.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi0zLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxzTUFBc00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvZm90LWljb24tMy5wbmc/YzY2YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZm90LWljb24tMy44YmViMjU2OS5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi0zLjhiZWIyNTY5LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-3.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-4-active.png":
/*!**********************************************************!*\
  !*** ../../packages/assets/images/fot-icon-4-active.png ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-4-active.4982b8c6.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-4-active.4982b8c6.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi00LWFjdGl2ZS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsb05BQW9OIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL2ZvdC1pY29uLTQtYWN0aXZlLnBuZz81MjgyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mb3QtaWNvbi00LWFjdGl2ZS40OTgyYjhjNi5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi00LWFjdGl2ZS40OTgyYjhjNi5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-4-active.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-4.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/fot-icon-4.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-4.88eec0df.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-4.88eec0df.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi00LnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxzTUFBc00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvZm90LWljb24tNC5wbmc/YTdiNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZm90LWljb24tNC44OGVlYzBkZi5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi00Ljg4ZWVjMGRmLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-4.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-5-active.png":
/*!**********************************************************!*\
  !*** ../../packages/assets/images/fot-icon-5-active.png ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-5-active.6ccd55e5.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-5-active.6ccd55e5.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi01LWFjdGl2ZS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsb05BQW9OIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL2ZvdC1pY29uLTUtYWN0aXZlLnBuZz9iMzM2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mb3QtaWNvbi01LWFjdGl2ZS42Y2NkNTVlNS5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi01LWFjdGl2ZS42Y2NkNTVlNS5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-5-active.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-5.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/fot-icon-5.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-5.571cfb9d.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-5.571cfb9d.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi01LnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxzTUFBc00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvZm90LWljb24tNS5wbmc/YWQxMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZm90LWljb24tNS41NzFjZmI5ZC5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi01LjU3MWNmYjlkLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-5.png\n"));

/***/ }),

/***/ "../../packages/assets/images/main-connect.png":
/*!*****************************************************!*\
  !*** ../../packages/assets/images/main-connect.png ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/main-connect.a1c10843.png\",\"height\":182,\"width\":348,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmain-connect.a1c10843.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":4});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9tYWluLWNvbm5lY3QucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLDRNQUE0TSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9tYWluLWNvbm5lY3QucG5nPzI1NzQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL21haW4tY29ubmVjdC5hMWMxMDg0My5wbmdcIixcImhlaWdodFwiOjE4MixcIndpZHRoXCI6MzQ4LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRm1haW4tY29ubmVjdC5hMWMxMDg0My5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6NH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/main-connect.png\n"));

/***/ }),

/***/ "../../packages/assets/images/mint1.png":
/*!**********************************************!*\
  !*** ../../packages/assets/images/mint1.png ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/mint1.b94776a6.png\",\"height\":456,\"width\":686,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmint1.b94776a6.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9taW50MS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsOExBQThMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL21pbnQxLnBuZz9kMzE0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9taW50MS5iOTQ3NzZhNi5wbmdcIixcImhlaWdodFwiOjQ1NixcIndpZHRoXCI6Njg2LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRm1pbnQxLmI5NDc3NmE2LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/mint1.png\n"));

/***/ }),

/***/ "../../packages/assets/images/mint2.png":
/*!**********************************************!*\
  !*** ../../packages/assets/images/mint2.png ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/mint2.37d559f3.png\",\"height\":388,\"width\":686,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmint2.37d559f3.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9taW50Mi5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsOExBQThMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL21pbnQyLnBuZz9jZDVjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9taW50Mi4zN2Q1NTlmMy5wbmdcIixcImhlaWdodFwiOjM4OCxcIndpZHRoXCI6Njg2LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRm1pbnQyLjM3ZDU1OWYzLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/mint2.png\n"));

/***/ }),

/***/ "../../packages/assets/images/search.png":
/*!***********************************************!*\
  !*** ../../packages/assets/images/search.png ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/search.e0802f75.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fsearch.e0802f75.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9zZWFyY2gucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLDhMQUE4TCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9zZWFyY2gucG5nPzlhY2MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL3NlYXJjaC5lMDgwMmY3NS5wbmdcIixcImhlaWdodFwiOjMyLFwid2lkdGhcIjozMixcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZzZWFyY2guZTA4MDJmNzUucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/search.png\n"));

/***/ }),

/***/ "../../packages/assets/images/setting.png":
/*!************************************************!*\
  !*** ../../packages/assets/images/setting.png ***!
  \************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/setting.1f39b21b.png\",\"height\":24,\"width\":24,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fsetting.1f39b21b.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9zZXR0aW5nLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxnTUFBZ00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvc2V0dGluZy5wbmc/OTJiNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvc2V0dGluZy4xZjM5YjIxYi5wbmdcIixcImhlaWdodFwiOjI0LFwid2lkdGhcIjoyNCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZzZXR0aW5nLjFmMzliMjFiLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/setting.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/net1.png":
/*!****************************************************!*\
  !*** ../../packages/assets/images/wallet/net1.png ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/net1.7804692c.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnet1.7804692c.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvbmV0MS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsMExBQTBMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9uZXQxLnBuZz9mZDU4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9uZXQxLjc4MDQ2OTJjLnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRm5ldDEuNzgwNDY5MmMucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/net1.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/net2.png":
/*!****************************************************!*\
  !*** ../../packages/assets/images/wallet/net2.png ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/net2.ffca884a.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnet2.ffca884a.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvbmV0Mi5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsMExBQTBMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9uZXQyLnBuZz8wNGNhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9uZXQyLmZmY2E4ODRhLnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRm5ldDIuZmZjYTg4NGEucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/net2.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/net3.png":
/*!****************************************************!*\
  !*** ../../packages/assets/images/wallet/net3.png ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/net3.4cb3a483.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnet3.4cb3a483.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvbmV0My5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsMExBQTBMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9uZXQzLnBuZz82YzczIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9uZXQzLjRjYjNhNDgzLnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRm5ldDMuNGNiM2E0ODMucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/net3.png\n"));

/***/ }),

/***/ "./pages/wallet/network.tsx":
/*!**********************************!*\
  !*** ./pages/wallet/network.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_network_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/network-screen */ \"../../packages/app/features/wallet/network-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_network_screen__WEBPACK_IMPORTED_MODULE_1__.NetworkScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/network.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvbmV0d29yay50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDa0U7QUFFbkQsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELDZFQUFhQTs7Ozs7QUFDdkI7S0FGd0JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3BhZ2VzL3dhbGxldC9uZXR3b3JrLnRzeD8wOTBiIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0IHsgTmV0d29ya1NjcmVlbiB9IGZyb20gJ2FwcC9mZWF0dXJlcy93YWxsZXQvbmV0d29yay1zY3JlZW4nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XG4gIHJldHVybiA8TmV0d29ya1NjcmVlbiAvPlxufSJdLCJuYW1lcyI6WyJOZXR3b3JrU2NyZWVuIiwiUGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/wallet/network.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/home/<USER>":
/*!*****************************************************!*\
  !*** ../../packages/app/features/home/<USER>
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HomePage: function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/close.png */ \"../../packages/assets/images/close.png\");\n/* harmony import */ var _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/main-connect.png */ \"../../packages/assets/images/main-connect.png\");\n/* harmony import */ var _assets_images_mint1_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/mint1.png */ \"../../packages/assets/images/mint1.png\");\n/* harmony import */ var _assets_images_mint2_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/mint2.png */ \"../../packages/assets/images/mint2.png\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 10\n});\n_c = Underline;\nconst ActiveBlock = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: 22,\n    height: 2,\n    background: \"#fff\",\n    borderRadius: 10\n});\n_c1 = ActiveBlock;\nconst Block = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: 22,\n    height: 2,\n    background: \"#262729\",\n    borderRadius: 10\n});\n_c2 = Block;\nconst SwiperContainer = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    position: \"relative\",\n    overflow: \"hidden\",\n    width: \"100%\"\n});\n_c3 = SwiperContainer;\nconst SwiperWrapper = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    display: \"flex\",\n    flexDirection: \"row\",\n    transition: \"transform 0.3s ease\",\n    width: \"400%\" // 4页内容，每页100%\n});\n_c4 = SwiperWrapper;\nconst SwiperSlide = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: \"25%\",\n    // 每个slide占25%（因为总宽度是400%）\n    flexShrink: 0\n});\n_c5 = SwiperSlide;\nfunction HomePage(param) {\n    let { pagesMode = false } = param;\n    _s();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const totalPages = 4;\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_8__.useTranslation)();\n    // 处理触摸开始\n    const handleTouchStart = (e)=>{\n        setTouchEnd(0); // 重置touchEnd\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    // 处理触摸移动\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    // 处理触摸结束\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isLeftSwipe = distance > 50;\n        const isRightSwipe = distance < -50;\n        if (isLeftSwipe && currentPage < totalPages - 1) {\n            setCurrentPage(currentPage + 1);\n        }\n        if (isRightSwipe && currentPage > 0) {\n            setCurrentPage(currentPage - 1);\n        }\n    };\n    // 轮播内容数据\n    const swiperData = [\n        {\n            id: 1,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"4.1\") || \"获得 4.1% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Base\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"4.1\") || \"将 Base 上的 USDC 添加到您的钱包，每年可赚取 4.1% 的奖励\"\n        },\n        {\n            id: 2,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"3.8\") || \"获得 3.8% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Ethereum\").replace(\"{token}\", \"USDT\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"3.8\") || \"将 Ethereum 上的 USDT 添加到您的钱包，每年可赚取 3.8% 的奖励\"\n        },\n        {\n            id: 3,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"5.2\") || \"获得 5.2% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Polygon\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"5.2\") || \"将 Polygon 上的 USDC 添加到您的钱包，每年可赚取 5.2% 的奖励\"\n        },\n        {\n            id: 4,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"4.5\") || \"获得 4.5% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Arbitrum\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"4.5\") || \"将 Arbitrum 上的 USDC 添加到您的钱包，每年可赚取 4.5% 的奖励\"\n        }\n    ];\n    const handleAction = (action)=>{\n        if (action === \"watchlist\") {\n            router.push(\"/user/myAttention\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n        \"data-at\": \"homePage.tsx:132\",\n        \"data-in\": \"HomePage\",\n        \"data-is\": \"YStack\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:134\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"homePage.tsx:135\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"XStack\",\n                        mt: 20,\n                        flex: 1,\n                        alignContent: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                \"data-at\": \"homePage.tsx:136\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"XStack\",\n                                gap: \"$2\",\n                                children: [\n                                    0,\n                                    1,\n                                    2,\n                                    3\n                                ].map((index)=>index <= currentPage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveBlock, {}, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 111,\n                                        columnNumber: 63\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Block, {}, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 111,\n                                        columnNumber: 93\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                \"data-at\": \"homePage.tsx:141\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                style: {\n                                    width: 12,\n                                    height: 12\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperContainer, {\n                        onTouchStart: handleTouchStart,\n                        onTouchMove: handleTouchMove,\n                        onTouchEnd: handleTouchEnd,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperWrapper, {\n                            style: {\n                                transform: \"translateX(-\".concat(currentPage * 25, \"%)\")\n                            },\n                            children: swiperData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperSlide, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                        \"data-at\": \"homePage.tsx:155-162\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"XStack\",\n                                        mt: 10,\n                                        flex: 1,\n                                        gap: \"$4\",\n                                        alignContent: \"center\",\n                                        justifyContent: \"space-between\",\n                                        mb: 10,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                                \"data-at\": \"homePage.tsx:163\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Image\",\n                                                source: _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                                style: {\n                                                    width: 50,\n                                                    height: 50\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                                \"data-at\": \"homePage.tsx:164\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"YStack\",\n                                                flex: 1,\n                                                flexWrap: \"wrap\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:165\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"#fff\",\n                                                        fontSize: 14,\n                                                        fontWeight: \"bold\",\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:168\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"#8B8F9A\",\n                                                        fontSize: 12,\n                                                        children: item.desc\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this)\n                                }, item.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 122,\n                                    columnNumber: 46\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:179\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                mb: 20,\n                onPress: ()=>handleAction(\"watchlist\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                        \"data-at\": \"homePage.tsx:180\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"Text\",\n                        color: \"white\",\n                        fontSize: 16,\n                        fontWeight: \"bold\",\n                        children: \"Watchlist\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"homePage.tsx:183-192\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"XStack\",\n                        bg: \"#141519\",\n                        borderRadius: 10,\n                        p: 10,\n                        mt: 10,\n                        height: 70,\n                        flex: 1,\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                \"data-at\": \"homePage.tsx:193\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"YStack\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:194\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"white\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"创建“我的关注”\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:197\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#8B8F9A\",\n                                        fontSize: 12,\n                                        fontWeight: 500,\n                                        mt: 6,\n                                        children: \"获取价格提醒并了解最新信息\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                \"data-at\": \"homePage.tsx:201\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                style: {\n                                    width: 70,\n                                    height: 37\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:205\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                        \"data-at\": \"homePage.tsx:206\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"Text\",\n                        fontSize: 16,\n                        color: \"white\",\n                        fontWeight: \"bold\",\n                        children: \"Trending swaps on Base\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"homePage.tsx:209\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"XStack\",\n                        mt: 10,\n                        width: 260,\n                        height: 180,\n                        borderRadius: 10,\n                        bg: \"#141519\",\n                        px: 14,\n                        py: 14,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                            \"data-at\": \"homePage.tsx:210\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"YStack\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                    \"data-at\": \"homePage.tsx:211\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"XStack\",\n                                    mb: \"$4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                            \"data-at\": \"homePage.tsx:212-215\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"Image\",\n                                            source: {\n                                                uri: \"\"\n                                            },\n                                            style: {\n                                                width: 28,\n                                                height: 28,\n                                                borderRadius: \"50%\",\n                                                background: \"#2B2B2B\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                            \"data-at\": \"homePage.tsx:216\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"View\",\n                                            ml: \"$2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    \"data-at\": \"homePage.tsx:217\",\n                                                    \"data-in\": \"HomePage\",\n                                                    \"data-is\": \"Text\",\n                                                    color: \"white\",\n                                                    fontSize: 14,\n                                                    fontWeight: \"bold\",\n                                                    children: \"KTA\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    \"data-at\": \"homePage.tsx:220\",\n                                                    \"data-in\": \"HomePage\",\n                                                    \"data-is\": \"Text\",\n                                                    color: \"#8B8F9A\",\n                                                    fontSize: 12,\n                                                    fontWeight: 500,\n                                                    mt: 6,\n                                                    children: \"US$0.54\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                            \"data-at\": \"homePage.tsx:224\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#C7545E\",\n                                            fontSize: 14,\n                                            fontWeight: \"bold\",\n                                            ml: \"$4\",\n                                            children: \"15.51%\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                    \"data-at\": \"homePage.tsx:228\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"XStack\",\n                                    alignItems: \"center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                            \"data-at\": \"homePage.tsx:229\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"Text\",\n                                            color: \"white\",\n                                            fontSize: 20,\n                                            fontWeight: \"bold\",\n                                            children: \"681\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                            \"data-at\": \"homePage.tsx:232\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"Text\",\n                                            color: \"white\",\n                                            fontSize: 16,\n                                            fontWeight: \"bold\",\n                                            ml: \"$1\",\n                                            children: \"兑换\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                            \"data-at\": \"homePage.tsx:235\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"View\",\n                                            bg: \"#282B32\",\n                                            width: 137,\n                                            height: 34,\n                                            borderRadius: 20,\n                                            ml: \"$5\",\n                                            onPress: ()=>{\n                                                router.push(\"/wallet/convert\");\n                                            },\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                \"data-at\": \"homePage.tsx:236\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Text\",\n                                                color: \"white\",\n                                                text: \"center\",\n                                                lineHeight: 34,\n                                                children: \"兑换\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                    \"data-at\": \"homePage.tsx:241\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"XStack\",\n                                    mt: 20,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                            \"data-at\": \"homePage.tsx:242\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"View\",\n                                            width: 124,\n                                            height: 4,\n                                            bg: \"#2FAB77\",\n                                            borderRadius: 20\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                            \"data-at\": \"homePage.tsx:243\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"View\",\n                                            width: 100,\n                                            height: 4,\n                                            bg: \"#C7545E\",\n                                            borderRadius: 20\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                    \"data-at\": \"homePage.tsx:245\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"XStack\",\n                                    mt: 20,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                            \"data-at\": \"homePage.tsx:246\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"XStack\",\n                                            flex: 1,\n                                            alignItems: \"center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                    \"data-at\": \"homePage.tsx:247\",\n                                                    \"data-in\": \"HomePage\",\n                                                    \"data-is\": \"View\",\n                                                    width: 6,\n                                                    height: 6,\n                                                    bg: \"#2FAB77\",\n                                                    borderRadius: 3\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    \"data-at\": \"homePage.tsx:248\",\n                                                    \"data-in\": \"HomePage\",\n                                                    \"data-is\": \"Text\",\n                                                    fontSize: 12,\n                                                    color: \"#8B8F9A\",\n                                                    ml: \"$2\",\n                                                    children: \"已购买 70%\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                            \"data-at\": \"homePage.tsx:252\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"XStack\",\n                                            flex: 1,\n                                            alignItems: \"center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                    \"data-at\": \"homePage.tsx:253\",\n                                                    \"data-in\": \"HomePage\",\n                                                    \"data-is\": \"View\",\n                                                    width: 6,\n                                                    height: 6,\n                                                    bg: \"#C7545E\",\n                                                    borderRadius: 3\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    \"data-at\": \"homePage.tsx:254\",\n                                                    \"data-in\": \"HomePage\",\n                                                    \"data-is\": \"Text\",\n                                                    fontSize: 12,\n                                                    color: \"#8B8F9A\",\n                                                    ml: \"$2\",\n                                                    children: [\n                                                        \"已售出 30%\",\n                                                        \" \"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:263\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                    \"data-at\": \"homePage.tsx:264\",\n                    \"data-in\": \"HomePage\",\n                    \"data-is\": \"View\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                            \"data-at\": \"homePage.tsx:265\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_mint1_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                            style: {\n                                width: \"100%\",\n                                height: 228,\n                                borderRadius: 12\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:266\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"white\",\n                            fontSize: 12,\n                            fontWeight: \"bold\",\n                            mt: 6,\n                            children: \"Sponsored\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:269\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            fontSize: 16,\n                            color: \"white\",\n                            fontWeight: \"bold\",\n                            mt: 6,\n                            children: \"In-App Bridging is Here\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:272\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"#8B8F9A\",\n                            fontSize: 12,\n                            fontWeight: 500,\n                            mt: 6,\n                            children: \"For when you really, really want that one token. Onthat other chain.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                            \"data-at\": \"homePage.tsx:275\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"XStack\",\n                            mt: \"$4\",\n                            alignItems: \"center\",\n                            gap: \"$5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                    \"data-at\": \"homePage.tsx:276\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"View\",\n                                    bg: \"#282B32\",\n                                    width: 137,\n                                    height: 34,\n                                    borderRadius: 20,\n                                    alignItems: \"center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:277\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"white\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        lineHeight: 34,\n                                        children: \"Learn More\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                    \"data-at\": \"homePage.tsx:281\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"Text\",\n                                    color: \"white\",\n                                    fontSize: 12,\n                                    fontWeight: \"bold\",\n                                    children: \"Dismiss\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:288\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                    \"data-at\": \"homePage.tsx:289\",\n                    \"data-in\": \"HomePage\",\n                    \"data-is\": \"View\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:290\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"white\",\n                            fontSize: 16,\n                            fontWeight: \"bold\",\n                            mb: 10,\n                            children: \"Trending onchain\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                            \"data-at\": \"homePage.tsx:293\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_mint2_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                            style: {\n                                width: \"100%\",\n                                height: 228,\n                                borderRadius: 12\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:294\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            fontSize: 16,\n                            color: \"white\",\n                            fontWeight: \"bold\",\n                            mt: 6,\n                            children: \"Drifters\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:297\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"#8B8F9A\",\n                            fontSize: 12,\n                            fontWeight: 500,\n                            mt: 6,\n                            children: \"Drifters are handcrafted, fully customiz..\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:303\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                    \"data-at\": \"homePage.tsx:304\",\n                    \"data-in\": \"HomePage\",\n                    \"data-is\": \"Text\",\n                    color: \"white\",\n                    fontSize: 16,\n                    children: \"NFT mints for you\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 274,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n        lineNumber: 106,\n        columnNumber: 10\n    }, this);\n}\n_s(HomePage, \"W+W+6QFeS0A5s7b4o2uYY16EQGU=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_8__.useTranslation\n    ];\n});\n_c6 = HomePage;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"Underline\");\n$RefreshReg$(_c1, \"ActiveBlock\");\n$RefreshReg$(_c2, \"Block\");\n$RefreshReg$(_c3, \"SwiperContainer\");\n$RefreshReg$(_c4, \"SwiperWrapper\");\n$RefreshReg$(_c5, \"SwiperSlide\");\n$RefreshReg$(_c6, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/home/<USER>"));

/***/ }),

/***/ "../../packages/app/features/home/<USER>":
/*!***************************************************!*\
  !*** ../../packages/app/features/home/<USER>
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FooterNavBar: function() { return /* binding */ FooterNavBar; },\n/* harmony export */   FotIconContainer: function() { return /* binding */ FotIconContainer; },\n/* harmony export */   HomeScreen: function() { return /* binding */ HomeScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/search.png */ \"../../packages/assets/images/search.png\");\n/* harmony import */ var _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/copy.png */ \"../../packages/assets/images/copy.png\");\n/* harmony import */ var _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/setting.png */ \"../../packages/assets/images/setting.png\");\n/* harmony import */ var _assets_images_fot_icon_1_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/fot-icon-1.png */ \"../../packages/assets/images/fot-icon-1.png\");\n/* harmony import */ var _assets_images_fot_icon_2_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../assets/images/fot-icon-2.png */ \"../../packages/assets/images/fot-icon-2.png\");\n/* harmony import */ var _assets_images_fot_icon_3_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../assets/images/fot-icon-3.png */ \"../../packages/assets/images/fot-icon-3.png\");\n/* harmony import */ var _assets_images_fot_icon_4_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../assets/images/fot-icon-4.png */ \"../../packages/assets/images/fot-icon-4.png\");\n/* harmony import */ var _assets_images_fot_icon_5_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../assets/images/fot-icon-5.png */ \"../../packages/assets/images/fot-icon-5.png\");\n/* harmony import */ var _assets_images_fot_icon_1_active_png__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../../assets/images/fot-icon-1-active.png */ \"../../packages/assets/images/fot-icon-1-active.png\");\n/* harmony import */ var _assets_images_fot_icon_2_active_png__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../../assets/images/fot-icon-2-active.png */ \"../../packages/assets/images/fot-icon-2-active.png\");\n/* harmony import */ var _assets_images_fot_icon_3_active_png__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../../assets/images/fot-icon-3-active.png */ \"../../packages/assets/images/fot-icon-3-active.png\");\n/* harmony import */ var _assets_images_fot_icon_4_active_png__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../../assets/images/fot-icon-4-active.png */ \"../../packages/assets/images/fot-icon-4-active.png\");\n/* harmony import */ var _assets_images_fot_icon_5_active_png__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../../assets/images/fot-icon-5-active.png */ \"../../packages/assets/images/fot-icon-5-active.png\");\n/* harmony import */ var _homePage__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./homePage */ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_15__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c = ActiveText;\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_15__.styled)(react_native__WEBPACK_IMPORTED_MODULE_16__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c1 = Underline;\nconst FotIconList = [\n    _assets_images_fot_icon_1_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    _assets_images_fot_icon_2_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    _assets_images_fot_icon_3_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    _assets_images_fot_icon_4_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    _assets_images_fot_icon_5_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n];\nconst FotIconListActive = [\n    _assets_images_fot_icon_1_active_png__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    _assets_images_fot_icon_2_active_png__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    _assets_images_fot_icon_3_active_png__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    _assets_images_fot_icon_4_active_png__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    _assets_images_fot_icon_5_active_png__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n];\nconst FotIconContainer = (0,tamagui__WEBPACK_IMPORTED_MODULE_15__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n    maxWidth: 640,\n    margin: \"auto\",\n    alignItems: \"center\",\n    position: \"absolute\",\n    bottom: 0,\n    left: 0,\n    right: 0,\n    height: 80,\n    backgroundColor: \"#131518\",\n    paddingHorizontal: 20,\n    cursor: \"pointer\",\n    zIndex: 100\n});\n_c2 = FotIconContainer;\nconst FooterNavBar = ()=>{\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useRouter)();\n    const pathname = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_17__.usePathname)();\n    const fotLinks = [\n        \"/\",\n        \"/wallet/network\",\n        \"/wallet/buy\",\n        \"/wallet/exchange\",\n        \"/user/home\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FotIconContainer, {\n        justifyContent: \"space-between\",\n        children: FotIconList.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_16__.Pressable, {\n                onPress: ()=>{\n                    router.push(fotLinks[index]);\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Image, {\n                    \"data-at\": \"screen.tsx:92-95\",\n                    \"data-in\": \"FooterNavBar\",\n                    \"data-is\": \"Image\",\n                    source: pathname === fotLinks[index] ? FotIconListActive[index].src : item.src,\n                    style: {\n                        width: 40,\n                        height: 40\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, undefined)\n            }, index, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 58,\n                columnNumber: 41\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n        lineNumber: 57,\n        columnNumber: 10\n    }, undefined);\n};\n_s(FooterNavBar, \"gA9e4WsoP6a20xDgQgrFkfMP8lc=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useRouter,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_17__.usePathname\n    ];\n});\n_c3 = FooterNavBar;\nfunction HomeScreen(param) {\n    let { pagesMode = false } = param;\n    _s1();\n    const linkTarget = pagesMode ? \"/user\" : \"/user\";\n    const toast = (0,_my_ui__WEBPACK_IMPORTED_MODULE_18__.useToastController)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_19__.useTranslation)();\n    const linkProps = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useLink)({\n        href: \"\".concat(linkTarget, \"/nate\")\n    });\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentAccount, setCurrentAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_20__.useWalletStore)();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useRouter)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWalletId, setSelectedWalletId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        walletStore.init();\n        // 如果没有钱包数据，加载测试数据\n        if (walletStore.walletList.length === 0) {\n            console.log(\"没有钱包数据，加载测试数据\");\n            // const testWalletData = [\n            //   {\n            //     \"mnemonic\": \"below neutral satoshi inhale hotel inhale humor forum visual citizen element seat\",\n            //     \"walletId\": \"cb1bfc86-1d02-4f3f-b52a-dca1988297c2\",\n            //     \"accounts\": [\n            //       {\n            //         \"walletId\": \"cb1bfc86-1d02-4f3f-b52a-dca1988297c2\",\n            //         \"accountId\": \"61053e03-2dbb-4e39-ac12-173dc80da9a6\",\n            //         \"name\": \"主钱包\",\n            //         \"btc\": {\n            //           \"uid\": \"97cba0ac-5708-44b7-be93-bebbb2939c94\",\n            //           \"address\": \"**********************************\",\n            //           \"privateKey\": \"97ca01b8eb25fc932f875df3acd2b3dc4b7f65090d575d4953f4432fad054bcb\",\n            //           \"accountType\": \"btc\"\n            //         },\n            //         \"eth\": {\n            //           \"uid\": \"c3aff85b-0f2a-40db-abf7-d14d8e6299a8\",\n            //           \"address\": \"******************************************\",\n            //           \"privateKey\": \"0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18c\",\n            //           \"accountType\": \"eth\"\n            //         },\n            //         \"bsc\": {\n            //           \"uid\": \"7b1e48d5-4c27-48c9-b0c2-70f4a7f4c10e\",\n            //           \"address\": \"******************************************\",\n            //           \"privateKey\": \"0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18c\",\n            //           \"accountType\": \"bsc\"\n            //         },\n            //         \"solana\": {\n            //           \"uid\": \"e1e06ec5-c947-4a21-b516-0743596c8064\",\n            //           \"address\": \"C5ThiTSKBAhYufh6uqLwzp1SGqWSHtTuE7guRKyVcaXx\",\n            //           \"privateKey\": \"71143f84a0b1a3beb45cd2882bc1f4a13692465e407aac824207fa170ba781c6a495124a84be84ca5b12032ab0b89950ce0fcc570ec5d545221193ea669cab5f\",\n            //           \"accountType\": \"solana\"\n            //         }\n            //       },\n            //       {\n            //         \"walletId\": \"cb1bfc86-1d02-4f3f-b52a-dca1988297c2\",\n            //         \"accountId\": \"61053e03-2dbb-4e39-ac12-173dc80da9a7\",\n            //         \"name\": \"备用钱包\",\n            //         \"btc\": {\n            //           \"uid\": \"97cba0ac-5708-44b7-be93-bebbb2939c95\",\n            //           \"address\": \"**********************************\",\n            //           \"privateKey\": \"97ca01b8eb25fc932f875df3acd2b3dc4b7f65090d575d4953f4432fad054bcd\",\n            //           \"accountType\": \"btc\"\n            //         },\n            //         \"eth\": {\n            //           \"uid\": \"c3aff85b-0f2a-40db-abf7-d14d8e6299a9\",\n            //           \"address\": \"******************************************\",\n            //           \"privateKey\": \"0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18d\",\n            //           \"accountType\": \"eth\"\n            //         },\n            //         \"bsc\": {\n            //           \"uid\": \"7b1e48d5-4c27-48c9-b0c2-70f4a7f4c10f\",\n            //           \"address\": \"******************************************\",\n            //           \"privateKey\": \"0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18d\",\n            //           \"accountType\": \"bsc\"\n            //         },\n            //         \"solana\": {\n            //           \"uid\": \"e1e06ec5-c947-4a21-b516-0743596c8065\",\n            //           \"address\": \"D5ThiTSKBAhYufh6uqLwzp1SGqWSHtTuE7guRKyVcaXy\",\n            //           \"privateKey\": \"71143f84a0b1a3beb45cd2882bc1f4a13692465e407aac824207fa170ba781c6a495124a84be84ca5b12032ab0b89950ce0fcc570ec5d545221193ea669cab60\",\n            //           \"accountType\": \"solana\"\n            //         }\n            //       }\n            //     ]\n            //   }\n            // ]\n            // localStorage.setItem('WALLET_LIST', JSON.stringify(testWalletData))\n            walletStore.init(); // 重新初始化\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (walletStore.currentAccount && walletStore.currentAccount.accountId) {\n            // 从 store 中获取当前账户，并设置显示名称\n            const account = walletStore.currentAccount;\n            // 如果账户有自定义名称，使用自定义名称；否则使用默认格式\n            let accountName = account.name;\n            if (!accountName) {\n                // 查找账户在钱包列表中的索引来生成默认名称\n                let accountIndex = 1;\n                for (const wallet of walletStore.walletList){\n                    const foundIndex = wallet.accounts.findIndex((acc)=>acc.accountId === account.accountId);\n                    if (foundIndex !== -1) {\n                        accountIndex = foundIndex + 1;\n                        break;\n                    }\n                }\n                accountName = (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(accountIndex));\n            }\n            setCurrentAccount({\n                ...account,\n                accountName\n            });\n            setSelectedWalletId(account.accountId);\n        }\n    }, [\n        walletStore.currentAccount,\n        walletStore.walletList,\n        t\n    ]);\n    const handleAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (action)=>{\n        if (action === \"search\") {\n            router.push(\"/wallet/search\");\n        }\n        if (action === \"copy\") {\n            try {\n                await navigator.clipboard.writeText(currentAccount.eth.address);\n                toast.show(t(\"success.addressCopied\") || \"地址已复制到剪贴板\", {\n                    duration: 2000\n                });\n            } catch (err) {\n                toast.show(t(\"home.copyFailed\") || \"复制失败，请手动复制\", {\n                    duration: 2000\n                });\n            }\n        }\n        if (action === \"setting\") {\n            router.push(\"/wallet/setting\");\n        }\n    }, [\n        currentAccount\n    ]);\n    // 手动刷新余额\n    const handleRefreshBalance = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (isRefreshing) return;\n        setIsRefreshing(true);\n        try {\n            await walletStore.fetchAllBalances();\n            toast.show(t(\"success.balanceRefreshed\") || \"余额已刷新\", {\n                duration: 2000\n            });\n        } catch (error) {\n            toast.show(t(\"error.refreshFailed\") || \"刷新失败，请稍后重试\", {\n                duration: 2000\n            });\n        } finally{\n            setIsRefreshing(false);\n        }\n    }, [\n        isRefreshing,\n        walletStore,\n        toast,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n        \"data-at\": \"screen.tsx:263\",\n        \"data-in\": \"HomeScreen\",\n        \"data-is\": \"YStack\",\n        height: \"100vh\",\n        bg: \"#0A0B0D\",\n        width: \"100%\",\n        maxW: 640,\n        margin: \"auto\",\n        overflow: \"hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n                \"data-at\": \"screen.tsx:264\",\n                \"data-in\": \"HomeScreen\",\n                \"data-is\": \"YStack\",\n                flex: 1,\n                gap: \"$3\",\n                p: \"$4\",\n                overflow: \"scroll\",\n                pb: 100,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.XStack, {\n                        \"data-at\": \"screen.tsx:265\",\n                        \"data-in\": \"HomeScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        space: \"$2\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.XStack, {\n                                \"data-at\": \"screen.tsx:266-272\",\n                                \"data-in\": \"HomeScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                space: \"$2\",\n                                onPress: ()=>{\n                                    setIsOpen(true);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Avatar, {\n                                        circular: true,\n                                        size: \"$4\",\n                                        mr: 12,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Avatar.Image, {\n                                                src: \"https://api.dicebear.com/7.x/identicon/svg?seed=\".concat(currentAccount.accountId),\n                                                accessibilityLabel: currentAccount.accountId\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Avatar.Fallback, {\n                                                backgroundColor: \"$blue10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                        \"data-at\": \"screen.tsx:280\",\n                                        \"data-in\": \"HomeScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#8B8F9A\",\n                                        fontSize: 14,\n                                        children: currentAccount.accountName\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_21__.ChevronDown, {\n                                        color: \"#8B8F9A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.XStack, {\n                                \"data-at\": \"screen.tsx:285\",\n                                \"data-in\": \"HomeScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                gap: \"$3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n                                        \"data-at\": \"screen.tsx:286\",\n                                        \"data-in\": \"HomeScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"search\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Image, {\n                                            \"data-at\": \"screen.tsx:287\",\n                                            \"data-in\": \"HomeScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n                                        \"data-at\": \"screen.tsx:289\",\n                                        \"data-in\": \"HomeScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"copy\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Image, {\n                                            \"data-at\": \"screen.tsx:290\",\n                                            \"data-in\": \"HomeScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                            style: {\n                                                width: 16,\n                                                height: 16\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n                                        \"data-at\": \"screen.tsx:292\",\n                                        \"data-in\": \"HomeScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"setting\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Image, {\n                                            \"data-at\": \"screen.tsx:293\",\n                                            \"data-in\": \"HomeScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                                            width: 16,\n                                            style: {\n                                                width: 16,\n                                                height: 16\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.XStack, {\n                        \"data-at\": \"screen.tsx:297\",\n                        \"data-in\": \"HomeScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$2\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.H2, {\n                            \"data-at\": \"screen.tsx:298\",\n                            \"data-in\": \"HomeScreen\",\n                            \"data-is\": \"H2\",\n                            textAlign: \"left\",\n                            color: \"#fff\",\n                            children: [\n                                \"$ \",\n                                walletStore.getCurrentAccountBalance().toFixed(4)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_homePage__WEBPACK_IMPORTED_MODULE_22__.HomePage, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.WalletSheet, {\n                open: isOpen,\n                onOpenChange: setIsOpen,\n                wallets: walletStore.walletList,\n                selectedId: selectedWalletId,\n                onSelect: (wallet, index)=>{\n                    if (wallet === \"addWallet\") {\n                        router.push(\"/wallet/manager\");\n                    } else {\n                        // 使用 store 的方法设置当前账户\n                        walletStore.setCurrentAccount(wallet);\n                        setSelectedWalletId(wallet.accountId);\n                        // 设置显示名称\n                        const accountName = wallet.name || (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(Number(index) + 1));\n                        setCurrentAccount({\n                            ...wallet,\n                            accountName\n                        });\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 310,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n        lineNumber: 230,\n        columnNumber: 10\n    }, this);\n}\n_s1(HomeScreen, \"Dv8Je33w+g6qB19H0e0sfZ7Y2kY=\", false, function() {\n    return [\n        _my_ui__WEBPACK_IMPORTED_MODULE_18__.useToastController,\n        app_i18n__WEBPACK_IMPORTED_MODULE_19__.useTranslation,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useLink,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_20__.useWalletStore,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useRouter\n    ];\n});\n_c4 = HomeScreen;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ActiveText\");\n$RefreshReg$(_c1, \"Underline\");\n$RefreshReg$(_c2, \"FotIconContainer\");\n$RefreshReg$(_c3, \"FooterNavBar\");\n$RefreshReg$(_c4, \"HomeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/home/<USER>"));

/***/ }),

/***/ "../../packages/app/features/wallet/network-screen.tsx":
/*!*************************************************************!*\
  !*** ../../packages/app/features/wallet/network-screen.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NetworkScreen: function() { return /* binding */ NetworkScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_net1_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/net1.png */ \"../../packages/assets/images/wallet/net1.png\");\n/* harmony import */ var _assets_images_wallet_net2_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/net2.png */ \"../../packages/assets/images/wallet/net2.png\");\n/* harmony import */ var _assets_images_wallet_net3_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/net3.png */ \"../../packages/assets/images/wallet/net3.png\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 20\n});\n_c = Underline;\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c1 = ActiveText;\nconst ActiveUnderline = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c2 = ActiveUnderline;\nfunction NetworkScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const tabList = [\n        \"全部\",\n        \"交换\",\n        \"赚取\",\n        \"社交媒体\",\n        \"管理\",\n        \"监听\"\n    ];\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const dataList = [\n        {\n            id: 1,\n            name: \"Aerodrome\",\n            desc: \"交易资产\",\n            isSelected: true,\n            url: \"https://aerodrome.finance/\"\n        },\n        {\n            id: 2,\n            name: \"Uniswap\",\n            desc: \"交易资产\",\n            isSelected: false,\n            url: \"https://app.uniswap.org/swap?disableNFTs=true\"\n        },\n        {\n            id: 3,\n            name: \"Seamless Protocol\",\n            desc: \"交易资产\",\n            isSelected: false,\n            url: \"https://seamlessprotocol.com/\"\n        }\n    ];\n    const onMnemonicClick = ()=>{\n        router.push(\"/wallet/password\");\n    };\n    const handleOpenUrl = (url)=>{\n        window.location.href = url;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n        \"data-at\": \"network-screen.tsx:79\",\n        \"data-in\": \"NetworkScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        minHeight: \"100vh\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                \"data-at\": \"network-screen.tsx:80\",\n                \"data-in\": \"NetworkScreen\",\n                \"data-is\": \"XStack\",\n                pl: 16,\n                alignItems: \"center\",\n                mb: 32,\n                justifyContent: \"space-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.NavBar, {\n                        title: \"\",\n                        onBack: ()=>router.back()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                        \"data-at\": \"network-screen.tsx:82\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"Input\",\n                        placeholder: \"搜索或输入网址\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                        \"data-at\": \"network-screen.tsx:83\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"View\",\n                        flexDirection: \"row\",\n                        justifyContent: \"space-between\",\n                        ml: 30,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>{},\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                    \"data-at\": \"network-screen.tsx:85\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_wallet_net1_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                    width: 16,\n                                    height: 16,\n                                    mr: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>{},\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                    \"data-at\": \"network-screen.tsx:88\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_wallet_net2_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                    width: 16,\n                                    height: 16,\n                                    mr: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>{},\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                    \"data-at\": \"network-screen.tsx:91\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_wallet_net3_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                    width: 16,\n                                    height: 16,\n                                    mr: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                \"data-at\": \"network-screen.tsx:96-105\",\n                \"data-in\": \"NetworkScreen\",\n                \"data-is\": \"XStack\",\n                pl: 16,\n                bg: \"#02A9DE\",\n                width: 343,\n                height: 80,\n                borderRadius: 20,\n                margin: \"auto\",\n                pt: 6,\n                alignItems: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                        \"data-at\": \"network-screen.tsx:106\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"Image\",\n                        source: _assets_images_wallet_net1_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                        width: 70,\n                        height: 70,\n                        mr: 10\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                        \"data-at\": \"network-screen.tsx:107\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"View\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                \"data-at\": \"network-screen.tsx:108\",\n                                \"data-in\": \"NetworkScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$black1\",\n                                fontWeight: \"bold\",\n                                fontSize: 14,\n                                children: \"用作资金的免费 NFT\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                \"data-at\": \"network-screen.tsx:111\",\n                                \"data-in\": \"NetworkScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$black1\",\n                                fontSize: 14,\n                                fontWeight: 500,\n                                mt: 4,\n                                children: \"获取特别的 NFT 来为钱包注入资金。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"network-screen.tsx:118\",\n                \"data-in\": \"NetworkScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                py: 20,\n                rowGap: 16,\n                flex: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                        \"data-at\": \"network-screen.tsx:119\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"Text\",\n                        children: \"热门应用\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"network-screen.tsx:120\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$5\",\n                        children: tabList.map((i, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>setCurrentTab(index),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                                    \"data-at\": \"network-screen.tsx:123\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"View\",\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === index ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: i\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 41\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            \"data-at\": \"network-screen.tsx:127\",\n                                            \"data-in\": \"NetworkScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: i\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 72\n                                        }, this),\n                                        currentTab === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveUnderline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 42\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this)\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 38\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                        \"data-at\": \"network-screen.tsx:134\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"YStack\",\n                        py: 10,\n                        children: dataList.map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"network-screen.tsx:136\",\n                                \"data-in\": \"NetworkScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                mb: 16,\n                                onPress: ()=>handleOpenUrl(i.url),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                        \"data-at\": \"network-screen.tsx:137-144\",\n                                        \"data-in\": \"NetworkScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: {\n                                            uri: \"\"\n                                        },\n                                        width: 40,\n                                        height: 40,\n                                        rounded: 20,\n                                        mr: 10,\n                                        bg: \"$white1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                                        \"data-at\": \"network-screen.tsx:145\",\n                                        \"data-in\": \"NetworkScreen\",\n                                        \"data-is\": \"View\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                                \"data-at\": \"network-screen.tsx:146\",\n                                                \"data-in\": \"NetworkScreen\",\n                                                \"data-is\": \"XStack\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                        \"data-at\": \"network-screen.tsx:147\",\n                                                        \"data-in\": \"NetworkScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        mr: 20,\n                                                        children: i.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    i.isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                        \"data-at\": \"network-screen.tsx:149-157\",\n                                                        \"data-in\": \"NetworkScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        bg: \"#141519\",\n                                                        rounded: 10,\n                                                        width: 55,\n                                                        height: 22,\n                                                        fontSize: 12,\n                                                        textAlign: \"center\",\n                                                        lineHeight: 22,\n                                                        children: \"精选\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 36\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                \"data-at\": \"network-screen.tsx:162\",\n                                                \"data-in\": \"NetworkScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"$color10\",\n                                                fontSize: 14,\n                                                mt: 11,\n                                                children: i.desc\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, i.id, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 30\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_9__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n        lineNumber: 57,\n        columnNumber: 10\n    }, this);\n}\n_s(NetworkScreen, \"k5oVBT4gVizGZi8FjIw9Gkdgvrs=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c3 = NetworkScreen;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"Underline\");\n$RefreshReg$(_c1, \"ActiveText\");\n$RefreshReg$(_c2, \"ActiveUnderline\");\n$RefreshReg$(_c3, \"NetworkScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/network-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fnetwork.tsx&page=%2Fwallet%2Fnetwork!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);