"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["packages_app_i18n_locales_zh_ts"],{

/***/ "../../packages/app/i18n/locales/zh.ts":
/*!*********************************************!*\
  !*** ../../packages/app/i18n/locales/zh.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\nconst zh = {\n    common: {\n        confirm: \"确认\",\n        cancel: \"取消\",\n        save: \"保存\",\n        delete: \"删除\",\n        edit: \"编辑\",\n        copy: \"复制\",\n        share: \"分享\",\n        loading: \"加载中...\",\n        error: \"错误\",\n        success: \"成功\",\n        back: \"返回\",\n        next: \"下一步\",\n        done: \"完成\",\n        close: \"关闭\"\n    },\n    wallet: {\n        balance: \"余额\",\n        send: \"发送\",\n        receive: \"接收\",\n        transaction: \"交易\",\n        address: \"地址\",\n        amount: \"金额\",\n        gasPrice: \"Gas价格\",\n        gasLimit: \"Gas限制\",\n        networkFee: \"网络费用\",\n        totalBalance: \"总余额\",\n        availableBalance: \"可用余额\",\n        sendTransaction: \"发送交易\",\n        receiveTokens: \"接收代币\",\n        transactionHistory: \"交易历史\",\n        copyAddress: \"复制地址\",\n        shareAddress: \"分享\",\n        insufficientBalance: \"余额不足\",\n        transactionFailed: \"交易失败\",\n        transactionSuccess: \"交易成功\",\n        pending: \"待确认\",\n        confirmed: \"已确认\",\n        failed: \"失败\",\n        yourAddress: \"您的地址\",\n        enterRecipientAddress: \"输入接收地址\",\n        max: \"最大\",\n        sending: \"发送中...\",\n        usingWallet: \"使用的钱包\",\n        network: \"网络\"\n    },\n    navigation: {\n        home: \"首页\",\n        wallet: \"钱包\",\n        settings: \"设置\",\n        security: \"安全\",\n        about: \"关于\",\n        language: \"语言\",\n        network: \"网络\",\n        backup: \"备份\",\n        import: \"导入\",\n        export: \"导出\",\n        display: \"显示\"\n    },\n    form: {\n        enterAmount: \"输入金额\",\n        enterAddress: \"输入地址\",\n        selectNetwork: \"选择网络\",\n        selectLanguage: \"选择语言\",\n        password: \"密码\",\n        confirmPassword: \"确认密码\",\n        mnemonic: \"助记词\",\n        privateKey: \"私钥\",\n        walletName: \"钱包名称\",\n        required: \"请填写完整信息\",\n        invalid: \"无效\",\n        tooShort: \"太短\",\n        tooLong: \"太长\",\n        passwordMismatch: \"密码不匹配\"\n    },\n    errors: {\n        networkError: \"网络错误\",\n        invalidAddress: \"无效地址\",\n        invalidAmount: \"无效金额\",\n        insufficientFunds: \"资金不足\",\n        transactionFailed: \"交易失败\",\n        walletNotFound: \"钱包未找到\",\n        invalidMnemonic: \"无效助记词\",\n        invalidPrivateKey: \"无效私钥\",\n        passwordRequired: \"需要密码\",\n        confirmationFailed: \"确认失败\",\n        copyFailed: \"复制失败\"\n    },\n    success: {\n        transactionSent: \"交易已发送\",\n        addressCopied: \"地址已复制到剪贴板\",\n        walletCreated: \"钱包已创建\",\n        walletImported: \"钱包已导入\",\n        settingsSaved: \"设置已保存\",\n        backupCompleted: \"备份已完成\",\n        copySuccess: \"复制成功\",\n        balanceRefreshed: \"余额已刷新\"\n    },\n    error: {\n        refreshFailed: \"刷新失败，请稍后重试\"\n    },\n    // 首页相关\n    home: {\n        rewards: \"奖励\",\n        earnRewards: \"获得 {rate}% 的奖励\",\n        addToWallet: \"将 {network} 上的 {token} 添加到您的钱包\",\n        yearlyEarnings: \"每年可赚取 {rate}% 的奖励\",\n        watchlist: \"关注列表\",\n        addressLabel: \"地址{number}\",\n        copyFailed: \"复制失败，请手动复制\",\n        createWatchlist: '创建\"我的关注\"',\n        getPriceAlerts: \"获取价格提醒并了解最新信息\",\n        swapCount: \"兑换\",\n        swapAction: \"兑换\",\n        boughtPercent: \"已购买 {percent}%\",\n        soldPercent: \"已售出 {percent}%\"\n    },\n    // 钱包管理\n    walletManagement: {\n        addAndManage: \"添加和管理钱包\",\n        backupWallet: \"备份您的钱包\",\n        securityWarning: \"绝对不要分享这些词。任何得知它们的人都可以窃取您所有的加密货币。Coinbase 绝不会要求您提供这些信息。\",\n        recoveryPhrase: \"以下 12 个单词是您钱包的恢复短语。该短语可让您在丢失设备时恢复钱包。将其备份到iCloud(推荐)或记下来。或同时采用这两种方式。\",\n        copyToClipboard: \"复制到剪贴板\",\n        neverShare: \"绝不分享\",\n        writeDown: \"记下来\",\n        backupToiCloud: \"备份到iCloud\",\n        importMnemonic: \"导入助记词\",\n        importDescription: \"输入助记词来添加或恢复你的钱包。导入的助记词将被加密并安全存储在你的设备上。为了你的资产安全，不会存储你的助记词。\",\n        enterMnemonic: \"输入助记词\",\n        mnemonicPlaceholder: \"输入助记词单词，并使用空格分隔\",\n        yourAddress: \"您的\",\n        address: \"地址\",\n        shareAddress: \"分享地址\",\n        editLabel: \"编辑标签\",\n        addressLabel: \"地址标签\",\n        labelDescription: \"为您的地址提供标签，以便轻松识别。标签存储在本地，仅限您可以看到。\",\n        enterWalletName: \"输入钱包名称\",\n        saving: \"保存中...\",\n        save: \"保存\"\n    },\n    // 交易相关\n    trading: {\n        all: \"全部\",\n        exchange: \"交换\",\n        earn: \"赚取\",\n        socialMedia: \"社交媒体\",\n        manage: \"管理\",\n        listen: \"监听\",\n        tradeAssets: \"交易资产\",\n        buy: \"买入\"\n    },\n    // 时间相关\n    time: {\n        today: \"今天\",\n        yesterday: \"昨天\",\n        filter: \"筛选\"\n    },\n    settings: {\n        languageNote: \"语言设置将在下次启动应用时生效。某些功能可能需要重新加载。\",\n        languageSubtitle: \"选择应用语言\",\n        securitySubtitle: \"密码和安全设置\",\n        notifications: \"通知\",\n        notificationsSubtitle: \"管理通知设置\",\n        aboutSubtitle: \"版本信息和帮助\",\n        version: \"版本\",\n        copyright: \"\\xa9 2024 Coinbase Wallet\"\n    }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (zh);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/i18n/locales/zh.ts\n"));

/***/ })

}]);