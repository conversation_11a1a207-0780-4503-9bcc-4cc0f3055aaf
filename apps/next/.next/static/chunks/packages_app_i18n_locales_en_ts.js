"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["packages_app_i18n_locales_en_ts"],{

/***/ "../../packages/app/i18n/locales/en.ts":
/*!*********************************************!*\
  !*** ../../packages/app/i18n/locales/en.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\nconst en = {\n    common: {\n        confirm: \"Confirm\",\n        cancel: \"Cancel\",\n        save: \"Save\",\n        delete: \"Delete\",\n        edit: \"Edit\",\n        copy: \"Copy\",\n        share: \"Share\",\n        loading: \"Loading...\",\n        error: \"Error\",\n        success: \"Success\",\n        back: \"Back\",\n        next: \"Next\",\n        done: \"Done\",\n        close: \"Close\"\n    },\n    wallet: {\n        balance: \"Balance\",\n        send: \"Send\",\n        receive: \"Receive\",\n        transaction: \"Transaction\",\n        address: \"Address\",\n        amount: \"Amount\",\n        gasPrice: \"Gas Price\",\n        gasLimit: \"Gas Limit\",\n        networkFee: \"Network Fee\",\n        totalBalance: \"Total Balance\",\n        availableBalance: \"Available Balance\",\n        sendTransaction: \"Send Transaction\",\n        receiveTokens: \"Receive Tokens\",\n        transactionHistory: \"Transaction History\",\n        copyAddress: \"Copy Address\",\n        shareAddress: \"Share\",\n        insufficientBalance: \"Insufficient Balance\",\n        transactionFailed: \"Transaction Failed\",\n        transactionSuccess: \"Transaction Success\",\n        pending: \"Pending\",\n        confirmed: \"Confirmed\",\n        failed: \"Failed\",\n        yourAddress: \"Your Address\",\n        enterRecipientAddress: \"Enter recipient address\",\n        max: \"Max\",\n        sending: \"Sending...\",\n        usingWallet: \"Using Wallet\",\n        network: \"Network\"\n    },\n    navigation: {\n        home: \"Home\",\n        wallet: \"Wallet\",\n        settings: \"Settings\",\n        security: \"Security\",\n        about: \"About\",\n        language: \"Language\",\n        network: \"Network\",\n        backup: \"Backup\",\n        import: \"Import\",\n        export: \"Export\",\n        display: \"Display\"\n    },\n    form: {\n        enterAmount: \"Enter Amount\",\n        enterAddress: \"Enter Address\",\n        selectNetwork: \"Select Network\",\n        selectLanguage: \"Select Language\",\n        password: \"Password\",\n        confirmPassword: \"Confirm Password\",\n        mnemonic: \"Mnemonic\",\n        privateKey: \"Private Key\",\n        walletName: \"Wallet Name\",\n        required: \"Please fill in all required fields\",\n        invalid: \"Invalid\",\n        tooShort: \"Too Short\",\n        tooLong: \"Too Long\",\n        passwordMismatch: \"Password Mismatch\"\n    },\n    errors: {\n        networkError: \"Network Error\",\n        invalidAddress: \"Invalid Address\",\n        invalidAmount: \"Invalid Amount\",\n        insufficientFunds: \"Insufficient Funds\",\n        transactionFailed: \"Transaction Failed\",\n        walletNotFound: \"Wallet Not Found\",\n        invalidMnemonic: \"Invalid Mnemonic\",\n        invalidPrivateKey: \"Invalid Private Key\",\n        passwordRequired: \"Password Required\",\n        confirmationFailed: \"Confirmation Failed\",\n        copyFailed: \"Copy failed\"\n    },\n    success: {\n        transactionSent: \"Transaction Sent\",\n        addressCopied: \"Address copied to clipboard\",\n        walletCreated: \"Wallet Created\",\n        walletImported: \"Wallet Imported\",\n        settingsSaved: \"Settings Saved\",\n        backupCompleted: \"Backup Completed\",\n        copySuccess: \"Copy Success\",\n        balanceRefreshed: \"Balance Refreshed\"\n    },\n    error: {\n        refreshFailed: \"Refresh failed, please try again later\"\n    },\n    // 首页相关\n    home: {\n        rewards: \"Rewards\",\n        earnRewards: \"Earn {rate}% rewards\",\n        addToWallet: \"Add {token} on {network} to your wallet\",\n        yearlyEarnings: \"Earn {rate}% annually\",\n        watchlist: \"Watchlist\",\n        addressLabel: \"Address {number}\",\n        copyFailed: \"Copy failed, please copy manually\",\n        createWatchlist: 'Create \"My Watchlist\"',\n        getPriceAlerts: \"Get price alerts and stay informed\",\n        swapCount: \"swaps\",\n        swapAction: \"Swap\",\n        boughtPercent: \"Bought {percent}%\",\n        soldPercent: \"Sold {percent}%\"\n    },\n    // 钱包管理\n    walletManagement: {\n        addAndManage: \"Add and manage wallets\",\n        backupWallet: \"Backup your wallet\",\n        securityWarning: \"Never share these words. Anyone who knows them can steal all your cryptocurrency. Coinbase will never ask for this information.\",\n        recoveryPhrase: \"The following 12 words are your wallet recovery phrase. This phrase allows you to recover your wallet if you lose your device. Back it up to iCloud (recommended) or write it down. Or use both methods.\",\n        copyToClipboard: \"Copy to clipboard\",\n        neverShare: \"Never share\",\n        writeDown: \"Write down\",\n        backupToiCloud: \"Backup to iCloud\",\n        importMnemonic: \"Import mnemonic\",\n        importDescription: \"Enter your mnemonic phrase to add or restore your wallet. The imported mnemonic will be encrypted and securely stored on your device. For your asset security, your mnemonic will not be stored.\",\n        enterMnemonic: \"Enter mnemonic\",\n        mnemonicPlaceholder: \"Enter mnemonic words separated by spaces\",\n        yourAddress: \"Your\",\n        address: \"address\",\n        shareAddress: \"Share\",\n        editLabel: \"Edit Label\",\n        addressLabel: \"Address Label\",\n        labelDescription: \"Provide a label for your address for easy identification. Labels are stored locally and only you can see them.\",\n        enterWalletName: \"Enter wallet name\",\n        saving: \"Saving...\",\n        save: \"Save\"\n    },\n    // 交易相关\n    trading: {\n        all: \"All\",\n        exchange: \"Exchange\",\n        earn: \"Earn\",\n        socialMedia: \"Social Media\",\n        manage: \"Manage\",\n        listen: \"Listen\",\n        tradeAssets: \"Trade Assets\",\n        buy: \"Buy\"\n    },\n    // 时间相关\n    time: {\n        today: \"Today\",\n        yesterday: \"Yesterday\",\n        filter: \"Filter\"\n    },\n    settings: {\n        languageNote: \"Language settings will take effect on next app launch. Some features may require reload.\",\n        languageSubtitle: \"Choose app language\",\n        securitySubtitle: \"Password and security settings\",\n        notifications: \"Notifications\",\n        notificationsSubtitle: \"Manage notification settings\",\n        aboutSubtitle: \"Version info and help\",\n        version: \"Version\",\n        copyright: \"\\xa9 2024 Coinbase Wallet\"\n    }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (en);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/i18n/locales/en.ts\n"));

/***/ })

}]);