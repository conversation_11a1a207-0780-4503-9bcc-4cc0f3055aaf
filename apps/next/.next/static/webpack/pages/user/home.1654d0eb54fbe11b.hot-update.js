"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/user/home",{

/***/ "../../packages/app/features/home/<USER>":
/*!*****************************************************!*\
  !*** ../../packages/app/features/home/<USER>
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HomePage: function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/close.png */ \"../../packages/assets/images/close.png\");\n/* harmony import */ var _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/main-connect.png */ \"../../packages/assets/images/main-connect.png\");\n/* harmony import */ var _assets_images_mint1_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/mint1.png */ \"../../packages/assets/images/mint1.png\");\n/* harmony import */ var _assets_images_mint2_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/mint2.png */ \"../../packages/assets/images/mint2.png\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 10\n});\n_c = Underline;\nconst ActiveBlock = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: 22,\n    height: 2,\n    background: \"#fff\",\n    borderRadius: 10\n});\n_c1 = ActiveBlock;\nconst Block = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: 22,\n    height: 2,\n    background: \"#262729\",\n    borderRadius: 10\n});\n_c2 = Block;\nconst SwiperContainer = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    position: \"relative\",\n    overflow: \"hidden\",\n    width: \"100%\"\n});\n_c3 = SwiperContainer;\nconst SwiperWrapper = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    display: \"flex\",\n    flexDirection: \"row\",\n    transition: \"transform 0.3s ease\",\n    width: \"400%\" // 4页内容，每页100%\n});\n_c4 = SwiperWrapper;\nconst SwiperSlide = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: \"25%\",\n    // 每个slide占25%（因为总宽度是400%）\n    flexShrink: 0\n});\n_c5 = SwiperSlide;\nfunction HomePage(param) {\n    let { pagesMode = false } = param;\n    _s();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const totalPages = 4;\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_8__.useTranslation)();\n    // 处理触摸开始\n    const handleTouchStart = (e)=>{\n        setTouchEnd(0); // 重置touchEnd\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    // 处理触摸移动\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    // 处理触摸结束\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isLeftSwipe = distance > 50;\n        const isRightSwipe = distance < -50;\n        if (isLeftSwipe && currentPage < totalPages - 1) {\n            setCurrentPage(currentPage + 1);\n        }\n        if (isRightSwipe && currentPage > 0) {\n            setCurrentPage(currentPage - 1);\n        }\n    };\n    // 轮播内容数据\n    const swiperData = [\n        {\n            id: 1,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"4.1\") || \"获得 4.1% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Base\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"4.1\") || \"将 Base 上的 USDC 添加到您的钱包，每年可赚取 4.1% 的奖励\"\n        },\n        {\n            id: 2,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"3.8\") || \"获得 3.8% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Ethereum\").replace(\"{token}\", \"USDT\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"3.8\") || \"将 Ethereum 上的 USDT 添加到您的钱包，每年可赚取 3.8% 的奖励\"\n        },\n        {\n            id: 3,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"5.2\") || \"获得 5.2% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Polygon\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"5.2\") || \"将 Polygon 上的 USDC 添加到您的钱包，每年可赚取 5.2% 的奖励\"\n        },\n        {\n            id: 4,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"4.5\") || \"获得 4.5% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Arbitrum\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"4.5\") || \"将 Arbitrum 上的 USDC 添加到您的钱包，每年可赚取 4.5% 的奖励\"\n        }\n    ];\n    // Trending swaps 测试数据\n    const trendingSwapsData = [\n        {\n            id: 1,\n            name: \"KTA\",\n            price: \"US$0.54\",\n            change: \"15.51%\",\n            changeColor: \"#C7545E\",\n            swaps: 681,\n            buyPercentage: 70,\n            sellPercentage: 30\n        },\n        {\n            id: 2,\n            name: \"DEGEN\",\n            price: \"US$0.012\",\n            change: \"+8.23%\",\n            changeColor: \"#2FAB77\",\n            swaps: 1247,\n            buyPercentage: 65,\n            sellPercentage: 35\n        },\n        {\n            id: 3,\n            name: \"HIGHER\",\n            price: \"US$0.089\",\n            change: \"-3.45%\",\n            changeColor: \"#C7545E\",\n            swaps: 892,\n            buyPercentage: 45,\n            sellPercentage: 55\n        },\n        {\n            id: 4,\n            name: \"BALD\",\n            price: \"US$0.0034\",\n            change: \"+12.67%\",\n            changeColor: \"#2FAB77\",\n            swaps: 2156,\n            buyPercentage: 78,\n            sellPercentage: 22\n        }\n    ];\n    const handleAction = (action)=>{\n        if (action === \"watchlist\") {\n            router.push(\"/user/myAttention\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n        \"data-at\": \"homePage.tsx:176\",\n        \"data-in\": \"HomePage\",\n        \"data-is\": \"YStack\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:178\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"homePage.tsx:179\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"XStack\",\n                        mt: 20,\n                        flex: 1,\n                        alignContent: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                \"data-at\": \"homePage.tsx:180\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"XStack\",\n                                gap: \"$2\",\n                                children: [\n                                    0,\n                                    1,\n                                    2,\n                                    3\n                                ].map((index)=>index <= currentPage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveBlock, {}, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 150,\n                                        columnNumber: 63\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Block, {}, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 150,\n                                        columnNumber: 93\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                \"data-at\": \"homePage.tsx:185\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                style: {\n                                    width: 12,\n                                    height: 12\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperContainer, {\n                        onTouchStart: handleTouchStart,\n                        onTouchMove: handleTouchMove,\n                        onTouchEnd: handleTouchEnd,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperWrapper, {\n                            style: {\n                                transform: \"translateX(-\".concat(currentPage * 25, \"%)\")\n                            },\n                            children: swiperData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperSlide, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                        \"data-at\": \"homePage.tsx:199-206\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"XStack\",\n                                        mt: 10,\n                                        flex: 1,\n                                        gap: \"$4\",\n                                        alignContent: \"center\",\n                                        justifyContent: \"space-between\",\n                                        mb: 10,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                                \"data-at\": \"homePage.tsx:207\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Image\",\n                                                source: _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                                style: {\n                                                    width: 50,\n                                                    height: 50\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                                \"data-at\": \"homePage.tsx:208\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"YStack\",\n                                                flex: 1,\n                                                flexWrap: \"wrap\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:209\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"#fff\",\n                                                        fontSize: 14,\n                                                        fontWeight: \"bold\",\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:212\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"#8B8F9A\",\n                                                        fontSize: 12,\n                                                        children: item.desc\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 171,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this)\n                                }, item.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 161,\n                                    columnNumber: 46\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:223\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                mb: 20,\n                onPress: ()=>handleAction(\"watchlist\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                        \"data-at\": \"homePage.tsx:224\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"Text\",\n                        color: \"white\",\n                        fontSize: 16,\n                        fontWeight: \"bold\",\n                        children: \"Watchlist\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"homePage.tsx:227-236\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"XStack\",\n                        bg: \"#141519\",\n                        borderRadius: 10,\n                        p: 10,\n                        mt: 10,\n                        height: 70,\n                        flex: 1,\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                \"data-at\": \"homePage.tsx:237\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"YStack\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:238\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"white\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"创建“我的关注”\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:241\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#8B8F9A\",\n                                        fontSize: 12,\n                                        fontWeight: 500,\n                                        mt: 6,\n                                        children: \"获取价格提醒并了解最新信息\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                \"data-at\": \"homePage.tsx:245\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                style: {\n                                    width: 70,\n                                    height: 37\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:249\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                        \"data-at\": \"homePage.tsx:250\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"Text\",\n                        fontSize: 16,\n                        color: \"white\",\n                        fontWeight: \"bold\",\n                        children: \"Trending swaps on Base\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"homePage.tsx:253\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"XStack\",\n                        mt: 10,\n                        width: 260,\n                        height: 180,\n                        borderRadius: 10,\n                        bg: \"#141519\",\n                        px: 14,\n                        py: 14,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                            \"data-at\": \"homePage.tsx:254\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"YStack\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                    \"data-at\": \"homePage.tsx:255\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"XStack\",\n                                    mb: \"$4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                            \"data-at\": \"homePage.tsx:256-259\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"Image\",\n                                            source: {\n                                                uri: \"\"\n                                            },\n                                            style: {\n                                                width: 28,\n                                                height: 28,\n                                                borderRadius: \"50%\",\n                                                background: \"#2B2B2B\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                            \"data-at\": \"homePage.tsx:260\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"View\",\n                                            ml: \"$2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    \"data-at\": \"homePage.tsx:261\",\n                                                    \"data-in\": \"HomePage\",\n                                                    \"data-is\": \"Text\",\n                                                    color: \"white\",\n                                                    fontSize: 14,\n                                                    fontWeight: \"bold\",\n                                                    children: \"KTA\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    \"data-at\": \"homePage.tsx:264\",\n                                                    \"data-in\": \"HomePage\",\n                                                    \"data-is\": \"Text\",\n                                                    color: \"#8B8F9A\",\n                                                    fontSize: 12,\n                                                    fontWeight: 500,\n                                                    mt: 6,\n                                                    children: \"US$0.54\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                            \"data-at\": \"homePage.tsx:268\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#C7545E\",\n                                            fontSize: 14,\n                                            fontWeight: \"bold\",\n                                            ml: \"$4\",\n                                            children: \"15.51%\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                    \"data-at\": \"homePage.tsx:272\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"XStack\",\n                                    alignItems: \"center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                            \"data-at\": \"homePage.tsx:273\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"Text\",\n                                            color: \"white\",\n                                            fontSize: 20,\n                                            fontWeight: \"bold\",\n                                            children: \"681\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                            \"data-at\": \"homePage.tsx:276\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"Text\",\n                                            color: \"white\",\n                                            fontSize: 16,\n                                            fontWeight: \"bold\",\n                                            ml: \"$1\",\n                                            children: \"兑换\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                            \"data-at\": \"homePage.tsx:279\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"View\",\n                                            bg: \"#282B32\",\n                                            width: 137,\n                                            height: 34,\n                                            borderRadius: 20,\n                                            ml: \"$5\",\n                                            onPress: ()=>{\n                                                router.push(\"/wallet/convert\");\n                                            },\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                \"data-at\": \"homePage.tsx:280\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Text\",\n                                                color: \"white\",\n                                                text: \"center\",\n                                                lineHeight: 34,\n                                                children: \"兑换\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                    \"data-at\": \"homePage.tsx:285\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"XStack\",\n                                    mt: 20,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                            \"data-at\": \"homePage.tsx:286\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"View\",\n                                            width: 124,\n                                            height: 4,\n                                            bg: \"#2FAB77\",\n                                            borderRadius: 20\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                            \"data-at\": \"homePage.tsx:287\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"View\",\n                                            width: 100,\n                                            height: 4,\n                                            bg: \"#C7545E\",\n                                            borderRadius: 20\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                    \"data-at\": \"homePage.tsx:289\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"XStack\",\n                                    mt: 20,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                            \"data-at\": \"homePage.tsx:290\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"XStack\",\n                                            flex: 1,\n                                            alignItems: \"center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                    \"data-at\": \"homePage.tsx:291\",\n                                                    \"data-in\": \"HomePage\",\n                                                    \"data-is\": \"View\",\n                                                    width: 6,\n                                                    height: 6,\n                                                    bg: \"#2FAB77\",\n                                                    borderRadius: 3\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    \"data-at\": \"homePage.tsx:292\",\n                                                    \"data-in\": \"HomePage\",\n                                                    \"data-is\": \"Text\",\n                                                    fontSize: 12,\n                                                    color: \"#8B8F9A\",\n                                                    ml: \"$2\",\n                                                    children: \"已购买 70%\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                            \"data-at\": \"homePage.tsx:296\",\n                                            \"data-in\": \"HomePage\",\n                                            \"data-is\": \"XStack\",\n                                            flex: 1,\n                                            alignItems: \"center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                    \"data-at\": \"homePage.tsx:297\",\n                                                    \"data-in\": \"HomePage\",\n                                                    \"data-is\": \"View\",\n                                                    width: 6,\n                                                    height: 6,\n                                                    bg: \"#C7545E\",\n                                                    borderRadius: 3\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    \"data-at\": \"homePage.tsx:298\",\n                                                    \"data-in\": \"HomePage\",\n                                                    \"data-is\": \"Text\",\n                                                    fontSize: 12,\n                                                    color: \"#8B8F9A\",\n                                                    ml: \"$2\",\n                                                    children: [\n                                                        \"已售出 30%\",\n                                                        \" \"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:308\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                    \"data-at\": \"homePage.tsx:309\",\n                    \"data-in\": \"HomePage\",\n                    \"data-is\": \"View\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                            \"data-at\": \"homePage.tsx:310\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_mint1_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                            style: {\n                                width: \"100%\",\n                                height: 228,\n                                borderRadius: 12\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:311\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"white\",\n                            fontSize: 12,\n                            fontWeight: \"bold\",\n                            mt: 6,\n                            children: \"Sponsored\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:314\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            fontSize: 16,\n                            color: \"white\",\n                            fontWeight: \"bold\",\n                            mt: 6,\n                            children: \"In-App Bridging is Here\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:317\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"#8B8F9A\",\n                            fontSize: 12,\n                            fontWeight: 500,\n                            mt: 6,\n                            children: \"For when you really, really want that one token. Onthat other chain.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                            \"data-at\": \"homePage.tsx:320\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"XStack\",\n                            mt: \"$4\",\n                            alignItems: \"center\",\n                            gap: \"$5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                    \"data-at\": \"homePage.tsx:321\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"View\",\n                                    bg: \"#282B32\",\n                                    width: 137,\n                                    height: 34,\n                                    borderRadius: 20,\n                                    alignItems: \"center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:322\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"white\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        lineHeight: 34,\n                                        children: \"Learn More\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                    \"data-at\": \"homePage.tsx:326\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"Text\",\n                                    color: \"white\",\n                                    fontSize: 12,\n                                    fontWeight: \"bold\",\n                                    children: \"Dismiss\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:333\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                    \"data-at\": \"homePage.tsx:334\",\n                    \"data-in\": \"HomePage\",\n                    \"data-is\": \"View\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:335\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"white\",\n                            fontSize: 16,\n                            fontWeight: \"bold\",\n                            mb: 10,\n                            children: \"Trending onchain\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                            \"data-at\": \"homePage.tsx:338\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_mint2_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                            style: {\n                                width: \"100%\",\n                                height: 228,\n                                borderRadius: 12\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:339\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            fontSize: 16,\n                            color: \"white\",\n                            fontWeight: \"bold\",\n                            mt: 6,\n                            children: \"Drifters\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:342\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"#8B8F9A\",\n                            fontSize: 12,\n                            fontWeight: 500,\n                            mt: 6,\n                            children: \"Drifters are handcrafted, fully customiz..\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:348\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                    \"data-at\": \"homePage.tsx:349\",\n                    \"data-in\": \"HomePage\",\n                    \"data-is\": \"Text\",\n                    color: \"white\",\n                    fontSize: 16,\n                    children: \"NFT mints for you\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n        lineNumber: 145,\n        columnNumber: 10\n    }, this);\n}\n_s(HomePage, \"W+W+6QFeS0A5s7b4o2uYY16EQGU=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_8__.useTranslation\n    ];\n});\n_c6 = HomePage;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"Underline\");\n$RefreshReg$(_c1, \"ActiveBlock\");\n$RefreshReg$(_c2, \"Block\");\n$RefreshReg$(_c3, \"SwiperContainer\");\n$RefreshReg$(_c4, \"SwiperWrapper\");\n$RefreshReg$(_c5, \"SwiperSlide\");\n$RefreshReg$(_c6, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXBwL2ZlYXR1cmVzL2hvbWUvaG9tZVBhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFnQztBQUNPO0FBQ3dCO0FBQ1A7QUFDWTtBQUNiO0FBQ0E7QUFDVjtBQUNKO0FBRXpDLE1BQU1hLFlBQVlSLCtDQUFNQSxDQUFDQyx5Q0FBSUEsRUFBRTtJQUM3QlEsT0FBTztJQUNQQyxRQUFRO0lBQ1JDLGlCQUFpQjtJQUNqQkMsSUFBSTtBQUNOO0tBTE1KO0FBT04sTUFBTUssY0FBY2IsK0NBQU1BLENBQUNDLHlDQUFJQSxFQUFFO0lBQy9CUSxPQUFPO0lBQ1BDLFFBQVE7SUFDUkksWUFBWTtJQUNaQyxjQUFjO0FBQ2hCO01BTE1GO0FBT04sTUFBTUcsUUFBUWhCLCtDQUFNQSxDQUFDQyx5Q0FBSUEsRUFBRTtJQUN6QlEsT0FBTztJQUNQQyxRQUFRO0lBQ1JJLFlBQVk7SUFDWkMsY0FBYztBQUNoQjtNQUxNQztBQU9OLE1BQU1DLGtCQUFrQmpCLCtDQUFNQSxDQUFDQyx5Q0FBSUEsRUFBRTtJQUNuQ2lCLFVBQVU7SUFDVkMsVUFBVTtJQUNWVixPQUFPO0FBQ1Q7TUFKTVE7QUFNTixNQUFNRyxnQkFBZ0JwQiwrQ0FBTUEsQ0FBQ0MseUNBQUlBLEVBQUU7SUFDakNvQixTQUFTO0lBQ1RDLGVBQWU7SUFDZkMsWUFBWTtJQUNaZCxPQUFPLE9BQVE7QUFDakI7TUFMTVc7QUFPTixNQUFNSSxjQUFjeEIsK0NBQU1BLENBQUNDLHlDQUFJQSxFQUFFO0lBQy9CUSxPQUFPO0lBQU87SUFDZGdCLFlBQVk7QUFDZDtNQUhNRDtBQUtDLFNBQVNFLFNBQVMsS0FBOEM7UUFBOUMsRUFBRUMsWUFBWSxPQUFnQyxHQUE5Qzs7SUFDdkIsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdsQywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNbUMsYUFBYTtJQUNuQixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR3JDLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3NDLFVBQVVDLFlBQVksR0FBR3ZDLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU13QyxTQUFTN0IsNERBQVNBO0lBQ3hCLE1BQU0sRUFBRThCLENBQUFBLEVBQUcsR0FBRzdCLHdEQUFjQTtJQUU1QjtJQUNBLE1BQU04QixtQkFBbUJBLENBQUNDO1FBQ3hCSixZQUFZLElBQUc7UUFDZkYsY0FBY00sRUFBRUMsYUFBYSxDQUFDLEVBQUUsQ0FBQ0MsT0FBTztJQUMxQztJQUVBO0lBQ0EsTUFBTUMsa0JBQWtCQSxDQUFDSDtRQUN2QkosWUFBWUksRUFBRUMsYUFBYSxDQUFDLEVBQUUsQ0FBQ0MsT0FBTztJQUN4QztJQUVBO0lBQ0EsTUFBTUUsaUJBQWlCQTtRQUNyQixJQUFJLENBQUNYLGNBQWMsQ0FBQ0UsVUFBVTtRQUU5QixNQUFNVSxXQUFXWixhQUFhRTtRQUM5QixNQUFNVyxjQUFjRCxXQUFXO1FBQy9CLE1BQU1FLGVBQWVGLFdBQVcsQ0FBQztRQUVqQyxJQUFJQyxlQUFlaEIsY0FBY0UsYUFBYSxHQUFHO1lBQy9DRCxlQUFlRCxjQUFjO1FBQy9CO1FBQ0EsSUFBSWlCLGdCQUFnQmpCLGNBQWMsR0FBRztZQUNuQ0MsZUFBZUQsY0FBYztRQUMvQjtJQUNGO0lBRUE7SUFDQSxNQUFNa0IsYUFBYTtRQUNqQjtZQUNFQyxJQUFJO1lBQ0pDLE9BQU9aLEVBQUUsb0JBQW9CYSxPQUFPLENBQUMsVUFBVSxVQUFVO1lBQ3pEQyxNQUNFZCxFQUFFLG9CQUFvQmEsT0FBTyxDQUFDLGFBQWEsUUFBUUEsT0FBTyxDQUFDLFdBQVcsVUFDdEUsTUFDQWIsRUFBRSx1QkFBdUJhLE9BQU8sQ0FBQyxVQUFVLFVBQzNDO1FBQ0o7UUFDQTtZQUNFRixJQUFJO1lBQ0pDLE9BQU9aLEVBQUUsb0JBQW9CYSxPQUFPLENBQUMsVUFBVSxVQUFVO1lBQ3pEQyxNQUNFZCxFQUFFLG9CQUFvQmEsT0FBTyxDQUFDLGFBQWEsWUFBWUEsT0FBTyxDQUFDLFdBQVcsVUFDMUUsTUFDQWIsRUFBRSx1QkFBdUJhLE9BQU8sQ0FBQyxVQUFVLFVBQzNDO1FBQ0o7UUFDQTtZQUNFRixJQUFJO1lBQ0pDLE9BQU9aLEVBQUUsb0JBQW9CYSxPQUFPLENBQUMsVUFBVSxVQUFVO1lBQ3pEQyxNQUNFZCxFQUFFLG9CQUFvQmEsT0FBTyxDQUFDLGFBQWEsV0FBV0EsT0FBTyxDQUFDLFdBQVcsVUFDekUsTUFDQWIsRUFBRSx1QkFBdUJhLE9BQU8sQ0FBQyxVQUFVLFVBQzNDO1FBQ0o7UUFDQTtZQUNFRixJQUFJO1lBQ0pDLE9BQU9aLEVBQUUsb0JBQW9CYSxPQUFPLENBQUMsVUFBVSxVQUFVO1lBQ3pEQyxNQUNFZCxFQUFFLG9CQUFvQmEsT0FBTyxDQUFDLGFBQWEsWUFBWUEsT0FBTyxDQUFDLFdBQVcsVUFDMUUsTUFDQWIsRUFBRSx1QkFBdUJhLE9BQU8sQ0FBQyxVQUFVLFVBQzNDO1FBQ0o7S0FDRDtJQUVEO0lBQ0EsTUFBTUUsb0JBQW9CO1FBQ3hCO1lBQ0VKLElBQUk7WUFDSkssTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLFFBQVE7WUFDUkMsYUFBYTtZQUNiQyxPQUFPO1lBQ1BDLGVBQWU7WUFDZkMsZ0JBQWdCO1FBQ2xCO1FBQ0E7WUFDRVgsSUFBSTtZQUNKSyxNQUFNO1lBQ05DLE9BQU87WUFDUEMsUUFBUTtZQUNSQyxhQUFhO1lBQ2JDLE9BQU87WUFDUEMsZUFBZTtZQUNmQyxnQkFBZ0I7UUFDbEI7UUFDQTtZQUNFWCxJQUFJO1lBQ0pLLE1BQU07WUFDTkMsT0FBTztZQUNQQyxRQUFRO1lBQ1JDLGFBQWE7WUFDYkMsT0FBTztZQUNQQyxlQUFlO1lBQ2ZDLGdCQUFnQjtRQUNsQjtRQUNBO1lBQ0VYLElBQUk7WUFDSkssTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLFFBQVE7WUFDUkMsYUFBYTtZQUNiQyxPQUFPO1lBQ1BDLGVBQWU7WUFDZkMsZ0JBQWdCO1FBQ2xCO0tBQ0Q7SUFFRCxNQUFNQyxlQUFlQSxDQUFDQztRQUNwQixJQUFJQSxXQUFXLGFBQWE7WUFDMUJ6QixPQUFPMEIsSUFBSSxDQUFDO1FBQ2Q7SUFDRjtJQUVBLHFCQUNFLDhEQUFDaEUsMENBQU1BO1FBQUFpRSxXQUFBO1FBQUFDLFdBQUE7UUFBQUMsV0FBQTs7MEJBQ0wsOERBQUN4RDs7Ozs7MEJBQ0QsOERBQUNYLDBDQUFNQTtnQkFBQWlFLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7O2tDQUNMLDhEQUFDcEUsMENBQU1BO3dCQUFBa0UsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ3BELElBQUk7d0JBQUlxRCxNQUFNO3dCQUFHQyxjQUFhO3dCQUFTQyxnQkFBZTs7MENBQzVELDhEQUFDdkUsMENBQU1BO2dDQUFBa0UsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQ0ksS0FBSTswQ0FDVDtvQ0FBQztvQ0FBRztvQ0FBRztvQ0FBRztpQ0FBRSxDQUFDQyxHQUFHLENBQUVDLENBQUFBLFFBQ2pCQSxTQUFTMUMsNEJBQWMsOERBQUNmLGlCQUFpQnlEOzs7OzZEQUFZLDhEQUFDdEQsV0FBV3NEOzs7Ozs7Ozs7OzBDQUdyRSw4REFBQ3hFLDBDQUFLQTtnQ0FBQWdFLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUNPLFFBQVFyRSxvRUFBYTtnQ0FBRXVFLE9BQU87b0NBQUVoRSxPQUFPO29DQUFJQyxRQUFRO2dDQUFHOzs7Ozs7Ozs7Ozs7a0NBRS9ELDhEQUFDTzt3QkFDQ3lELGNBQWNyQzt3QkFDZHNDLGFBQWFsQzt3QkFDYm1DLFlBQVlsQztrQ0FFWiw0RUFBQ3RCOzRCQUNDcUQsT0FBTztnQ0FDTEksV0FBVyxlQUErQixPQUFoQmpELGNBQWMsSUFBRTs0QkFDNUM7c0NBRUNrQixXQUFXdUIsR0FBRyxDQUFDLENBQUNTLE1BQU1SLHNCQUNyQiw4REFBQzlDOzhDQUNDLDRFQUFDNUIsMENBQU1BO3dDQUFBa0UsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FDTHBELElBQUk7d0NBQ0pxRCxNQUFNO3dDQUNORyxLQUFJO3dDQUNKRixjQUFhO3dDQUNiQyxnQkFBZTt3Q0FDZlksSUFBSTs7MERBRUosOERBQUNqRiwwQ0FBS0E7Z0RBQUFnRSxXQUFBO2dEQUFBQyxXQUFBO2dEQUFBQyxXQUFBO2dEQUFDTyxRQUFRckUsb0VBQWE7Z0RBQUV1RSxPQUFPO29EQUFFaEUsT0FBTztvREFBSUMsUUFBUTtnREFBRzs7Ozs7OzBEQUM3RCw4REFBQ2IsMENBQU1BO2dEQUFBaUUsV0FBQTtnREFBQUMsV0FBQTtnREFBQUMsV0FBQTtnREFBQ0MsTUFBTTtnREFBR2UsVUFBUzs7a0VBQ3hCLDhEQUFDakYseUNBQUlBO3dEQUFBK0QsV0FBQTt3REFBQUMsV0FBQTt3REFBQUMsV0FBQTt3REFBQ2lCLE9BQU07d0RBQU9DLFVBQVU7d0RBQUlDLFlBQVc7a0VBQ3pDTCxLQUFLOUIsS0FBSzs7Ozs7O2tFQUViLDhEQUFDakQseUNBQUlBO3dEQUFBK0QsV0FBQTt3REFBQUMsV0FBQTt3REFBQUMsV0FBQTt3REFBQ2lCLE9BQU07d0RBQVVDLFVBQVU7a0VBQzdCSixLQUFLNUIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7O21DQWZBNEIsS0FBSy9CLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkF3QmpDLDhEQUFDdkM7Ozs7OzBCQUNELDhEQUFDWCwwQ0FBTUE7Z0JBQUFpRSxXQUFBO2dCQUFBQyxXQUFBO2dCQUFBQyxXQUFBO2dCQUFDcEQsSUFBSTtnQkFBSW1FLElBQUk7Z0JBQUlLLFNBQVMsSUFBTXpCLGFBQWE7O2tDQUNsRCw4REFBQzVELHlDQUFJQTt3QkFBQStELFdBQUE7d0JBQUFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUNpQixPQUFNO3dCQUFRQyxVQUFVO3dCQUFJQyxZQUFXO2tDQUFNOzs7Ozs7a0NBR25ELDhEQUFDdkYsMENBQU1BO3dCQUFBa0UsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFDTHFCLElBQUc7d0JBQ0h0RSxjQUFjO3dCQUNkdUUsR0FBRzt3QkFDSDFFLElBQUk7d0JBQ0pGLFFBQVE7d0JBQ1J1RCxNQUFNO3dCQUNOc0IsWUFBVzt3QkFDWHBCLGdCQUFlOzswQ0FFZiw4REFBQ3RFLDBDQUFNQTtnQ0FBQWlFLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7O2tEQUNMLDhEQUFDakUseUNBQUlBO3dDQUFBK0QsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQ2lCLE9BQU07d0NBQVFDLFVBQVU7d0NBQUlDLFlBQVc7a0RBQU07Ozs7OztrREFHbkQsOERBQUNwRix5Q0FBSUE7d0NBQUErRCxXQUFBO3dDQUFBQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFDaUIsT0FBTTt3Q0FBVUMsVUFBVTt3Q0FBSUMsWUFBWTt3Q0FBS3ZFLElBQUk7a0RBQUU7Ozs7Ozs7Ozs7OzswQ0FJN0QsOERBQUNkLDBDQUFLQTtnQ0FBQWdFLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUNPLFFBQVFwRSwyRUFBa0I7Z0NBQUVzRSxPQUFPO29DQUFFaEUsT0FBTztvQ0FBSUMsUUFBUTtnQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUd0RSw4REFBQ0Y7Ozs7OzBCQUNELDhEQUFDWCwwQ0FBTUE7Z0JBQUFpRSxXQUFBO2dCQUFBQyxXQUFBO2dCQUFBQyxXQUFBO2dCQUFDcEQsSUFBSTtnQkFBSW1FLElBQUk7O2tDQUNsQiw4REFBQ2hGLHlDQUFJQTt3QkFBQStELFdBQUE7d0JBQUFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUNrQixVQUFVO3dCQUFJRCxPQUFNO3dCQUFRRSxZQUFZO2tDQUFPOzs7Ozs7a0NBR3JELDhEQUFDdkYsMENBQU1BO3dCQUFBa0UsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ3BELElBQUk7d0JBQUlILE9BQU87d0JBQUtDLFFBQVE7d0JBQUtLLGNBQWM7d0JBQUlzRSxJQUFHO3dCQUFVRyxJQUFJO3dCQUFJQyxJQUFJO2tDQUNsRiw0RUFBQzVGLDBDQUFNQTs0QkFBQWlFLFdBQUE7NEJBQUFDLFdBQUE7NEJBQUFDLFdBQUE7OzhDQUNMLDhEQUFDcEUsMENBQU1BO29DQUFBa0UsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQ2UsSUFBRzs7c0RBQ1QsOERBQUNqRiwwQ0FBS0E7NENBQUFnRSxXQUFBOzRDQUFBQyxXQUFBOzRDQUFBQyxXQUFBOzRDQUNKTyxRQUFRO2dEQUFFbUIsS0FBSzs0Q0FBRzs0Q0FDbEJqQixPQUFPO2dEQUFFaEUsT0FBTztnREFBSUMsUUFBUTtnREFBSUssY0FBYztnREFBT0QsWUFBWTs0Q0FBVTs7Ozs7O3NEQUU3RSw4REFBQ2IseUNBQUlBOzRDQUFBNkQsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQzJCLElBQUc7OzhEQUNQLDhEQUFDNUYseUNBQUlBO29EQUFBK0QsV0FBQTtvREFBQUMsV0FBQTtvREFBQUMsV0FBQTtvREFBQ2lCLE9BQU07b0RBQVFDLFVBQVU7b0RBQUlDLFlBQVc7OERBQU07Ozs7Ozs4REFHbkQsOERBQUNwRix5Q0FBSUE7b0RBQUErRCxXQUFBO29EQUFBQyxXQUFBO29EQUFBQyxXQUFBO29EQUFDaUIsT0FBTTtvREFBVUMsVUFBVTtvREFBSUMsWUFBWTtvREFBS3ZFLElBQUk7OERBQUU7Ozs7Ozs7Ozs7OztzREFJN0QsOERBQUNiLHlDQUFJQTs0Q0FBQStELFdBQUE7NENBQUFDLFdBQUE7NENBQUFDLFdBQUE7NENBQUNpQixPQUFNOzRDQUFVQyxVQUFVOzRDQUFJQyxZQUFXOzRDQUFPUSxJQUFHO3NEQUFJOzs7Ozs7Ozs7Ozs7OENBSS9ELDhEQUFDL0YsMENBQU1BO29DQUFBa0UsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQ3VCLFlBQVc7O3NEQUNqQiw4REFBQ3hGLHlDQUFJQTs0Q0FBQStELFdBQUE7NENBQUFDLFdBQUE7NENBQUFDLFdBQUE7NENBQUNpQixPQUFNOzRDQUFRQyxVQUFVOzRDQUFJQyxZQUFXO3NEQUFNOzs7Ozs7c0RBR25ELDhEQUFDcEYseUNBQUlBOzRDQUFBK0QsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQ2lCLE9BQU07NENBQVFDLFVBQVU7NENBQUlDLFlBQVc7NENBQU9RLElBQUc7c0RBQUk7Ozs7OztzREFHM0QsOERBQUMxRix5Q0FBSUE7NENBQUE2RCxXQUFBOzRDQUFBQyxXQUFBOzRDQUFBQyxXQUFBOzRDQUFDcUIsSUFBRzs0Q0FBVTVFLE9BQU87NENBQUtDLFFBQVE7NENBQUlLLGNBQWM7NENBQUk0RSxJQUFHOzRDQUFLUCxTQUFTO2dEQUFRakQsT0FBTzBCLElBQUksQ0FBQzs0Q0FBbUI7NENBQUcwQixZQUFXOzRDQUFTcEIsZ0JBQWU7c0RBQ3pKLDRFQUFDcEUseUNBQUlBO2dEQUFBK0QsV0FBQTtnREFBQUMsV0FBQTtnREFBQUMsV0FBQTtnREFBQ2lCLE9BQU07Z0RBQVFXLE1BQUs7Z0RBQVNDLFlBQVk7MERBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUtyRCw4REFBQ2pHLDBDQUFNQTtvQ0FBQWtFLFdBQUE7b0NBQUFDLFdBQUE7b0NBQUFDLFdBQUE7b0NBQUNwRCxJQUFJOztzREFDViw4REFBQ1gseUNBQUlBOzRDQUFBNkQsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQ3ZELE9BQU87NENBQUtDLFFBQVE7NENBQUcyRSxJQUFHOzRDQUFVdEUsY0FBYzs7Ozs7O3NEQUN4RCw4REFBQ2QseUNBQUlBOzRDQUFBNkQsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQ3ZELE9BQU87NENBQUtDLFFBQVE7NENBQUcyRSxJQUFHOzRDQUFVdEUsY0FBYzs7Ozs7Ozs7Ozs7OzhDQUUxRCw4REFBQ25CLDBDQUFNQTtvQ0FBQWtFLFdBQUE7b0NBQUFDLFdBQUE7b0NBQUFDLFdBQUE7b0NBQUNwRCxJQUFJOztzREFDViw4REFBQ2hCLDBDQUFNQTs0Q0FBQWtFLFdBQUE7NENBQUFDLFdBQUE7NENBQUFDLFdBQUE7NENBQUNDLE1BQU07NENBQUdzQixZQUFXOzs4REFDMUIsOERBQUN0Rix5Q0FBSUE7b0RBQUE2RCxXQUFBO29EQUFBQyxXQUFBO29EQUFBQyxXQUFBO29EQUFDdkQsT0FBTztvREFBR0MsUUFBUTtvREFBRzJFLElBQUc7b0RBQVV0RSxjQUFjOzs7Ozs7OERBQ3RELDhEQUFDaEIseUNBQUlBO29EQUFBK0QsV0FBQTtvREFBQUMsV0FBQTtvREFBQUMsV0FBQTtvREFBQ2tCLFVBQVU7b0RBQUlELE9BQU07b0RBQVVVLElBQUc7OERBQUk7Ozs7Ozs7Ozs7OztzREFJN0MsOERBQUMvRiwwQ0FBTUE7NENBQUFrRSxXQUFBOzRDQUFBQyxXQUFBOzRDQUFBQyxXQUFBOzRDQUFDQyxNQUFNOzRDQUFHc0IsWUFBVzs7OERBQzFCLDhEQUFDdEYseUNBQUlBO29EQUFBNkQsV0FBQTtvREFBQUMsV0FBQTtvREFBQUMsV0FBQTtvREFBQ3ZELE9BQU87b0RBQUdDLFFBQVE7b0RBQUcyRSxJQUFHO29EQUFVdEUsY0FBYzs7Ozs7OzhEQUN0RCw4REFBQ2hCLHlDQUFJQTtvREFBQStELFdBQUE7b0RBQUFDLFdBQUE7b0RBQUFDLFdBQUE7b0RBQUNrQixVQUFVO29EQUFJRCxPQUFNO29EQUFVVSxJQUFHOzt3REFBSTt3REFDakM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRcEIsOERBQUNuRjs7Ozs7MEJBQ0QsOERBQUNYLDBDQUFNQTtnQkFBQWlFLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNwRCxJQUFJOzBCQUNWLDRFQUFDWCx5Q0FBSUE7b0JBQUE2RCxXQUFBO29CQUFBQyxXQUFBO29CQUFBQyxXQUFBOztzQ0FDSCw4REFBQ2xFLDBDQUFLQTs0QkFBQWdFLFdBQUE7NEJBQUFDLFdBQUE7NEJBQUFDLFdBQUE7NEJBQUNPLFFBQVFuRSxvRUFBWTs0QkFBRXFFLE9BQU87Z0NBQUVoRSxPQUFPO2dDQUFRQyxRQUFRO2dDQUFLSyxjQUFjOzRCQUFHOzs7Ozs7c0NBQ25GLDhEQUFDaEIseUNBQUlBOzRCQUFBK0QsV0FBQTs0QkFBQUMsV0FBQTs0QkFBQUMsV0FBQTs0QkFBQ2lCLE9BQU07NEJBQVFDLFVBQVU7NEJBQUlDLFlBQVc7NEJBQU92RSxJQUFJO3NDQUFFOzs7Ozs7c0NBRzFELDhEQUFDYix5Q0FBSUE7NEJBQUErRCxXQUFBOzRCQUFBQyxXQUFBOzRCQUFBQyxXQUFBOzRCQUFDa0IsVUFBVTs0QkFBSUQsT0FBTTs0QkFBUUUsWUFBVzs0QkFBT3ZFLElBQUk7c0NBQUU7Ozs7OztzQ0FHMUQsOERBQUNiLHlDQUFJQTs0QkFBQStELFdBQUE7NEJBQUFDLFdBQUE7NEJBQUFDLFdBQUE7NEJBQUNpQixPQUFNOzRCQUFVQyxVQUFVOzRCQUFJQyxZQUFZOzRCQUFLdkUsSUFBSTtzQ0FBRTs7Ozs7O3NDQUczRCw4REFBQ2hCLDBDQUFNQTs0QkFBQWtFLFdBQUE7NEJBQUFDLFdBQUE7NEJBQUFDLFdBQUE7NEJBQUNwRCxJQUFHOzRCQUFLMkUsWUFBVzs0QkFBU25CLEtBQUk7OzhDQUN0Qyw4REFBQ25FLHlDQUFJQTtvQ0FBQTZELFdBQUE7b0NBQUFDLFdBQUE7b0NBQUFDLFdBQUE7b0NBQUNxQixJQUFHO29DQUFVNUUsT0FBTztvQ0FBS0MsUUFBUTtvQ0FBSUssY0FBYztvQ0FBSXdFLFlBQVc7OENBQ3RFLDRFQUFDeEYseUNBQUlBO3dDQUFBK0QsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQ2lCLE9BQU07d0NBQVFDLFVBQVU7d0NBQUlDLFlBQVc7d0NBQU9VLFlBQVk7a0RBQUc7Ozs7Ozs7Ozs7OzhDQUlyRSw4REFBQzlGLHlDQUFJQTtvQ0FBQStELFdBQUE7b0NBQUFDLFdBQUE7b0NBQUFDLFdBQUE7b0NBQUNpQixPQUFNO29DQUFRQyxVQUFVO29DQUFJQyxZQUFXOzhDQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNekQsOERBQUMzRTs7Ozs7MEJBQ0QsOERBQUNYLDBDQUFNQTtnQkFBQWlFLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNwRCxJQUFJOzBCQUNWLDRFQUFDWCx5Q0FBSUE7b0JBQUE2RCxXQUFBO29CQUFBQyxXQUFBO29CQUFBQyxXQUFBOztzQ0FDSCw4REFBQ2pFLHlDQUFJQTs0QkFBQStELFdBQUE7NEJBQUFDLFdBQUE7NEJBQUFDLFdBQUE7NEJBQUNpQixPQUFNOzRCQUFRQyxVQUFVOzRCQUFJQyxZQUFXOzRCQUFPSixJQUFJO3NDQUFHOzs7Ozs7c0NBRzNELDhEQUFDakYsMENBQUtBOzRCQUFBZ0UsV0FBQTs0QkFBQUMsV0FBQTs0QkFBQUMsV0FBQTs0QkFBQ08sUUFBUWxFLG9FQUFZOzRCQUFFb0UsT0FBTztnQ0FBRWhFLE9BQU87Z0NBQVFDLFFBQVE7Z0NBQUtLLGNBQWM7NEJBQUc7Ozs7OztzQ0FDbkYsOERBQUNoQix5Q0FBSUE7NEJBQUErRCxXQUFBOzRCQUFBQyxXQUFBOzRCQUFBQyxXQUFBOzRCQUFDa0IsVUFBVTs0QkFBSUQsT0FBTTs0QkFBUUUsWUFBVzs0QkFBT3ZFLElBQUk7c0NBQUU7Ozs7OztzQ0FHMUQsOERBQUNiLHlDQUFJQTs0QkFBQStELFdBQUE7NEJBQUFDLFdBQUE7NEJBQUFDLFdBQUE7NEJBQUNpQixPQUFNOzRCQUFVQyxVQUFVOzRCQUFJQyxZQUFZOzRCQUFLdkUsSUFBSTtzQ0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBSy9ELDhEQUFDSjs7Ozs7MEJBQ0QsOERBQUNYLDBDQUFNQTtnQkFBQWlFLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNwRCxJQUFJOzBCQUNWLDRFQUFDYix5Q0FBSUE7b0JBQUErRCxXQUFBO29CQUFBQyxXQUFBO29CQUFBQyxXQUFBO29CQUFDaUIsT0FBTTtvQkFBUUMsVUFBVTs4QkFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNekM7R0FqVGdCeEQ7O1FBS0NwQix3REFBU0E7UUFDVkMsb0RBQWNBOzs7TUFOZG1CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hcHAvZmVhdHVyZXMvaG9tZS9ob21lUGFnZS50c3g/NzI0MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgWFN0YWNrLCBZU3RhY2sgfSBmcm9tICdAbXkvdWknXG5pbXBvcnQgeyBJbWFnZSwgVGV4dCwgc3R5bGVkLCBWaWV3LCBTY3JvbGxWaWV3IH0gZnJvbSAndGFtYWd1aSdcbmltcG9ydCBjbG9zZUljb24gZnJvbSAnLi4vLi4vLi4vYXNzZXRzL2ltYWdlcy9jbG9zZS5wbmcnXG5pbXBvcnQgbWFpbkNvbm5ldEljb24gZnJvbSAnLi4vLi4vLi4vYXNzZXRzL2ltYWdlcy9tYWluLWNvbm5lY3QucG5nJ1xuaW1wb3J0IG1pbnQxSW1nIGZyb20gJy4uLy4uLy4uL2Fzc2V0cy9pbWFnZXMvbWludDEucG5nJ1xuaW1wb3J0IG1pbnQySW1nIGZyb20gJy4uLy4uLy4uL2Fzc2V0cy9pbWFnZXMvbWludDIucG5nJ1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnc29saXRvL25hdmlnYXRpb24nXG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJ2FwcC9pMThuJ1xuXG5jb25zdCBVbmRlcmxpbmUgPSBzdHlsZWQoVmlldywge1xuICB3aWR0aDogJzEwMCUnLFxuICBoZWlnaHQ6IDEsXG4gIGJhY2tncm91bmRDb2xvcjogJyMyMTIyMjQnLFxuICBtdDogMTAsXG59KVxuXG5jb25zdCBBY3RpdmVCbG9jayA9IHN0eWxlZChWaWV3LCB7XG4gIHdpZHRoOiAyMixcbiAgaGVpZ2h0OiAyLFxuICBiYWNrZ3JvdW5kOiAnI2ZmZicsXG4gIGJvcmRlclJhZGl1czogMTAsXG59KVxuXG5jb25zdCBCbG9jayA9IHN0eWxlZChWaWV3LCB7XG4gIHdpZHRoOiAyMixcbiAgaGVpZ2h0OiAyLFxuICBiYWNrZ3JvdW5kOiAnIzI2MjcyOScsXG4gIGJvcmRlclJhZGl1czogMTAsXG59KVxuXG5jb25zdCBTd2lwZXJDb250YWluZXIgPSBzdHlsZWQoVmlldywge1xuICBwb3NpdGlvbjogJ3JlbGF0aXZlJyxcbiAgb3ZlcmZsb3c6ICdoaWRkZW4nLFxuICB3aWR0aDogJzEwMCUnLFxufSlcblxuY29uc3QgU3dpcGVyV3JhcHBlciA9IHN0eWxlZChWaWV3LCB7XG4gIGRpc3BsYXk6ICdmbGV4JyxcbiAgZmxleERpcmVjdGlvbjogJ3JvdycsXG4gIHRyYW5zaXRpb246ICd0cmFuc2Zvcm0gMC4zcyBlYXNlJyxcbiAgd2lkdGg6ICc0MDAlJywgLy8gNOmhteWGheWuue+8jOavj+mhtTEwMCVcbn0pXG5cbmNvbnN0IFN3aXBlclNsaWRlID0gc3R5bGVkKFZpZXcsIHtcbiAgd2lkdGg6ICcyNSUnLCAvLyDmr4/kuKpzbGlkZeWNoDI1Je+8iOWboOS4uuaAu+WuveW6puaYrzQwMCXvvIlcbiAgZmxleFNocmluazogMCxcbn0pXG5cbmV4cG9ydCBmdW5jdGlvbiBIb21lUGFnZSh7IHBhZ2VzTW9kZSA9IGZhbHNlIH06IHsgcGFnZXNNb2RlPzogYm9vbGVhbiB9KSB7XG4gIGNvbnN0IFtjdXJyZW50UGFnZSwgc2V0Q3VycmVudFBhZ2VdID0gdXNlU3RhdGUoMClcbiAgY29uc3QgdG90YWxQYWdlcyA9IDRcbiAgY29uc3QgW3RvdWNoU3RhcnQsIHNldFRvdWNoU3RhcnRdID0gdXNlU3RhdGUoMClcbiAgY29uc3QgW3RvdWNoRW5kLCBzZXRUb3VjaEVuZF0gPSB1c2VTdGF0ZSgwKVxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKClcblxuICAvLyDlpITnkIbop6bmkbjlvIDlp4tcbiAgY29uc3QgaGFuZGxlVG91Y2hTdGFydCA9IChlOiBhbnkpID0+IHtcbiAgICBzZXRUb3VjaEVuZCgwKSAvLyDph43nva50b3VjaEVuZFxuICAgIHNldFRvdWNoU3RhcnQoZS50YXJnZXRUb3VjaGVzWzBdLmNsaWVudFgpXG4gIH1cblxuICAvLyDlpITnkIbop6bmkbjnp7vliqhcbiAgY29uc3QgaGFuZGxlVG91Y2hNb3ZlID0gKGU6IGFueSkgPT4ge1xuICAgIHNldFRvdWNoRW5kKGUudGFyZ2V0VG91Y2hlc1swXS5jbGllbnRYKVxuICB9XG5cbiAgLy8g5aSE55CG6Kem5pG457uT5p2fXG4gIGNvbnN0IGhhbmRsZVRvdWNoRW5kID0gKCkgPT4ge1xuICAgIGlmICghdG91Y2hTdGFydCB8fCAhdG91Y2hFbmQpIHJldHVyblxuXG4gICAgY29uc3QgZGlzdGFuY2UgPSB0b3VjaFN0YXJ0IC0gdG91Y2hFbmRcbiAgICBjb25zdCBpc0xlZnRTd2lwZSA9IGRpc3RhbmNlID4gNTBcbiAgICBjb25zdCBpc1JpZ2h0U3dpcGUgPSBkaXN0YW5jZSA8IC01MFxuXG4gICAgaWYgKGlzTGVmdFN3aXBlICYmIGN1cnJlbnRQYWdlIDwgdG90YWxQYWdlcyAtIDEpIHtcbiAgICAgIHNldEN1cnJlbnRQYWdlKGN1cnJlbnRQYWdlICsgMSlcbiAgICB9XG4gICAgaWYgKGlzUmlnaHRTd2lwZSAmJiBjdXJyZW50UGFnZSA+IDApIHtcbiAgICAgIHNldEN1cnJlbnRQYWdlKGN1cnJlbnRQYWdlIC0gMSlcbiAgICB9XG4gIH1cblxuICAvLyDova7mkq3lhoXlrrnmlbDmja5cbiAgY29uc3Qgc3dpcGVyRGF0YSA9IFtcbiAgICB7XG4gICAgICBpZDogMSxcbiAgICAgIHRpdGxlOiB0KCdob21lLmVhcm5SZXdhcmRzJykucmVwbGFjZSgne3JhdGV9JywgJzQuMScpIHx8ICfojrflvpcgNC4xJSDnmoTlpZblirEnLFxuICAgICAgZGVzYzpcbiAgICAgICAgdCgnaG9tZS5hZGRUb1dhbGxldCcpLnJlcGxhY2UoJ3tuZXR3b3JrfScsICdCYXNlJykucmVwbGFjZSgne3Rva2VufScsICdVU0RDJykgK1xuICAgICAgICAn77yMJyArXG4gICAgICAgIHQoJ2hvbWUueWVhcmx5RWFybmluZ3MnKS5yZXBsYWNlKCd7cmF0ZX0nLCAnNC4xJykgfHxcbiAgICAgICAgJ+WwhiBCYXNlIOS4iueahCBVU0RDIOa3u+WKoOWIsOaCqOeahOmSseWMhe+8jOavj+W5tOWPr+i1muWPliA0LjElIOeahOWlluWKsScsXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogMixcbiAgICAgIHRpdGxlOiB0KCdob21lLmVhcm5SZXdhcmRzJykucmVwbGFjZSgne3JhdGV9JywgJzMuOCcpIHx8ICfojrflvpcgMy44JSDnmoTlpZblirEnLFxuICAgICAgZGVzYzpcbiAgICAgICAgdCgnaG9tZS5hZGRUb1dhbGxldCcpLnJlcGxhY2UoJ3tuZXR3b3JrfScsICdFdGhlcmV1bScpLnJlcGxhY2UoJ3t0b2tlbn0nLCAnVVNEVCcpICtcbiAgICAgICAgJ++8jCcgK1xuICAgICAgICB0KCdob21lLnllYXJseUVhcm5pbmdzJykucmVwbGFjZSgne3JhdGV9JywgJzMuOCcpIHx8XG4gICAgICAgICflsIYgRXRoZXJldW0g5LiK55qEIFVTRFQg5re75Yqg5Yiw5oKo55qE6ZKx5YyF77yM5q+P5bm05Y+v6LWa5Y+WIDMuOCUg55qE5aWW5YqxJyxcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAzLFxuICAgICAgdGl0bGU6IHQoJ2hvbWUuZWFyblJld2FyZHMnKS5yZXBsYWNlKCd7cmF0ZX0nLCAnNS4yJykgfHwgJ+iOt+W+lyA1LjIlIOeahOWlluWKsScsXG4gICAgICBkZXNjOlxuICAgICAgICB0KCdob21lLmFkZFRvV2FsbGV0JykucmVwbGFjZSgne25ldHdvcmt9JywgJ1BvbHlnb24nKS5yZXBsYWNlKCd7dG9rZW59JywgJ1VTREMnKSArXG4gICAgICAgICfvvIwnICtcbiAgICAgICAgdCgnaG9tZS55ZWFybHlFYXJuaW5ncycpLnJlcGxhY2UoJ3tyYXRlfScsICc1LjInKSB8fFxuICAgICAgICAn5bCGIFBvbHlnb24g5LiK55qEIFVTREMg5re75Yqg5Yiw5oKo55qE6ZKx5YyF77yM5q+P5bm05Y+v6LWa5Y+WIDUuMiUg55qE5aWW5YqxJyxcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiA0LFxuICAgICAgdGl0bGU6IHQoJ2hvbWUuZWFyblJld2FyZHMnKS5yZXBsYWNlKCd7cmF0ZX0nLCAnNC41JykgfHwgJ+iOt+W+lyA0LjUlIOeahOWlluWKsScsXG4gICAgICBkZXNjOlxuICAgICAgICB0KCdob21lLmFkZFRvV2FsbGV0JykucmVwbGFjZSgne25ldHdvcmt9JywgJ0FyYml0cnVtJykucmVwbGFjZSgne3Rva2VufScsICdVU0RDJykgK1xuICAgICAgICAn77yMJyArXG4gICAgICAgIHQoJ2hvbWUueWVhcmx5RWFybmluZ3MnKS5yZXBsYWNlKCd7cmF0ZX0nLCAnNC41JykgfHxcbiAgICAgICAgJ+WwhiBBcmJpdHJ1bSDkuIrnmoQgVVNEQyDmt7vliqDliLDmgqjnmoTpkrHljIXvvIzmr4/lubTlj6/otZrlj5YgNC41JSDnmoTlpZblirEnLFxuICAgIH0sXG4gIF1cblxuICAvLyBUcmVuZGluZyBzd2FwcyDmtYvor5XmlbDmja5cbiAgY29uc3QgdHJlbmRpbmdTd2Fwc0RhdGEgPSBbXG4gICAge1xuICAgICAgaWQ6IDEsXG4gICAgICBuYW1lOiAnS1RBJyxcbiAgICAgIHByaWNlOiAnVVMkMC41NCcsXG4gICAgICBjaGFuZ2U6ICcxNS41MSUnLFxuICAgICAgY2hhbmdlQ29sb3I6ICcjQzc1NDVFJyxcbiAgICAgIHN3YXBzOiA2ODEsXG4gICAgICBidXlQZXJjZW50YWdlOiA3MCxcbiAgICAgIHNlbGxQZXJjZW50YWdlOiAzMCxcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAyLFxuICAgICAgbmFtZTogJ0RFR0VOJyxcbiAgICAgIHByaWNlOiAnVVMkMC4wMTInLFxuICAgICAgY2hhbmdlOiAnKzguMjMlJyxcbiAgICAgIGNoYW5nZUNvbG9yOiAnIzJGQUI3NycsXG4gICAgICBzd2FwczogMTI0NyxcbiAgICAgIGJ1eVBlcmNlbnRhZ2U6IDY1LFxuICAgICAgc2VsbFBlcmNlbnRhZ2U6IDM1LFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IDMsXG4gICAgICBuYW1lOiAnSElHSEVSJyxcbiAgICAgIHByaWNlOiAnVVMkMC4wODknLFxuICAgICAgY2hhbmdlOiAnLTMuNDUlJyxcbiAgICAgIGNoYW5nZUNvbG9yOiAnI0M3NTQ1RScsXG4gICAgICBzd2FwczogODkyLFxuICAgICAgYnV5UGVyY2VudGFnZTogNDUsXG4gICAgICBzZWxsUGVyY2VudGFnZTogNTUsXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogNCxcbiAgICAgIG5hbWU6ICdCQUxEJyxcbiAgICAgIHByaWNlOiAnVVMkMC4wMDM0JyxcbiAgICAgIGNoYW5nZTogJysxMi42NyUnLFxuICAgICAgY2hhbmdlQ29sb3I6ICcjMkZBQjc3JyxcbiAgICAgIHN3YXBzOiAyMTU2LFxuICAgICAgYnV5UGVyY2VudGFnZTogNzgsXG4gICAgICBzZWxsUGVyY2VudGFnZTogMjIsXG4gICAgfSxcbiAgXVxuXG4gIGNvbnN0IGhhbmRsZUFjdGlvbiA9IChhY3Rpb246IHN0cmluZykgPT4ge1xuICAgIGlmIChhY3Rpb24gPT09ICd3YXRjaGxpc3QnKSB7XG4gICAgICByb3V0ZXIucHVzaCgnL3VzZXIvbXlBdHRlbnRpb24nKVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPFlTdGFjaz5cbiAgICAgIDxVbmRlcmxpbmUgLz5cbiAgICAgIDxZU3RhY2s+XG4gICAgICAgIDxYU3RhY2sgbXQ9ezIwfSBmbGV4PXsxfSBhbGlnbkNvbnRlbnQ9XCJjZW50ZXJcIiBqdXN0aWZ5Q29udGVudD1cInNwYWNlLWJldHdlZW5cIj5cbiAgICAgICAgICA8WFN0YWNrIGdhcD1cIiQyXCI+XG4gICAgICAgICAgICB7WzAsIDEsIDIsIDNdLm1hcCgoaW5kZXgpID0+XG4gICAgICAgICAgICAgIGluZGV4IDw9IGN1cnJlbnRQYWdlID8gPEFjdGl2ZUJsb2NrIGtleT17aW5kZXh9IC8+IDogPEJsb2NrIGtleT17aW5kZXh9IC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvWFN0YWNrPlxuICAgICAgICAgIDxJbWFnZSBzb3VyY2U9e2Nsb3NlSWNvbi5zcmN9IHN0eWxlPXt7IHdpZHRoOiAxMiwgaGVpZ2h0OiAxMiB9fSAvPlxuICAgICAgICA8L1hTdGFjaz5cbiAgICAgICAgPFN3aXBlckNvbnRhaW5lclxuICAgICAgICAgIG9uVG91Y2hTdGFydD17aGFuZGxlVG91Y2hTdGFydH1cbiAgICAgICAgICBvblRvdWNoTW92ZT17aGFuZGxlVG91Y2hNb3ZlfVxuICAgICAgICAgIG9uVG91Y2hFbmQ9e2hhbmRsZVRvdWNoRW5kfVxuICAgICAgICA+XG4gICAgICAgICAgPFN3aXBlcldyYXBwZXJcbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIHRyYW5zZm9ybTogYHRyYW5zbGF0ZVgoLSR7Y3VycmVudFBhZ2UgKiAyNX0lKWAsXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtzd2lwZXJEYXRhLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgPFN3aXBlclNsaWRlIGtleT17aXRlbS5pZH0+XG4gICAgICAgICAgICAgICAgPFhTdGFja1xuICAgICAgICAgICAgICAgICAgbXQ9ezEwfVxuICAgICAgICAgICAgICAgICAgZmxleD17MX1cbiAgICAgICAgICAgICAgICAgIGdhcD1cIiQ0XCJcbiAgICAgICAgICAgICAgICAgIGFsaWduQ29udGVudD1cImNlbnRlclwiXG4gICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudD1cInNwYWNlLWJldHdlZW5cIlxuICAgICAgICAgICAgICAgICAgbWI9ezEwfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxJbWFnZSBzb3VyY2U9e2Nsb3NlSWNvbi5zcmN9IHN0eWxlPXt7IHdpZHRoOiA1MCwgaGVpZ2h0OiA1MCB9fSAvPlxuICAgICAgICAgICAgICAgICAgPFlTdGFjayBmbGV4PXsxfSBmbGV4V3JhcD1cIndyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgPFRleHQgY29sb3I9XCIjZmZmXCIgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PVwiYm9sZFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgICAgIDxUZXh0IGNvbG9yPVwiIzhCOEY5QVwiIGZvbnRTaXplPXsxMn0+XG4gICAgICAgICAgICAgICAgICAgICAge2l0ZW0uZGVzY31cbiAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICAgICAgPC9ZU3RhY2s+XG4gICAgICAgICAgICAgICAgPC9YU3RhY2s+XG4gICAgICAgICAgICAgIDwvU3dpcGVyU2xpZGU+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L1N3aXBlcldyYXBwZXI+XG4gICAgICAgIDwvU3dpcGVyQ29udGFpbmVyPlxuICAgICAgPC9ZU3RhY2s+XG4gICAgICA8VW5kZXJsaW5lIC8+XG4gICAgICA8WVN0YWNrIG10PXsyMH0gbWI9ezIwfSBvblByZXNzPXsoKSA9PiBoYW5kbGVBY3Rpb24oJ3dhdGNobGlzdCcpfT5cbiAgICAgICAgPFRleHQgY29sb3I9XCJ3aGl0ZVwiIGZvbnRTaXplPXsxNn0gZm9udFdlaWdodD1cImJvbGRcIj5cbiAgICAgICAgICBXYXRjaGxpc3RcbiAgICAgICAgPC9UZXh0PlxuICAgICAgICA8WFN0YWNrXG4gICAgICAgICAgYmc9XCIjMTQxNTE5XCJcbiAgICAgICAgICBib3JkZXJSYWRpdXM9ezEwfVxuICAgICAgICAgIHA9ezEwfVxuICAgICAgICAgIG10PXsxMH1cbiAgICAgICAgICBoZWlnaHQ9ezcwfVxuICAgICAgICAgIGZsZXg9ezF9XG4gICAgICAgICAgYWxpZ25JdGVtcz1cImNlbnRlclwiXG4gICAgICAgICAganVzdGlmeUNvbnRlbnQ9XCJzcGFjZS1iZXR3ZWVuXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxZU3RhY2s+XG4gICAgICAgICAgICA8VGV4dCBjb2xvcj1cIndoaXRlXCIgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PVwiYm9sZFwiPlxuICAgICAgICAgICAgICDliJvlu7rigJzmiJHnmoTlhbPms6jigJ1cbiAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgIDxUZXh0IGNvbG9yPVwiIzhCOEY5QVwiIGZvbnRTaXplPXsxMn0gZm9udFdlaWdodD17NTAwfSBtdD17Nn0+XG4gICAgICAgICAgICAgIOiOt+WPluS7t+agvOaPkOmGkuW5tuS6huino+acgOaWsOS/oeaBr1xuICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgIDwvWVN0YWNrPlxuICAgICAgICAgIDxJbWFnZSBzb3VyY2U9e21haW5Db25uZXRJY29uLnNyY30gc3R5bGU9e3sgd2lkdGg6IDcwLCBoZWlnaHQ6IDM3IH19IC8+XG4gICAgICAgIDwvWFN0YWNrPlxuICAgICAgPC9ZU3RhY2s+XG4gICAgICA8VW5kZXJsaW5lIC8+XG4gICAgICA8WVN0YWNrIG10PXsyMH0gbWI9ezIwfT5cbiAgICAgICAgPFRleHQgZm9udFNpemU9ezE2fSBjb2xvcj1cIndoaXRlXCIgZm9udFdlaWdodD17J2JvbGQnfT5cbiAgICAgICAgICBUcmVuZGluZyBzd2FwcyBvbiBCYXNlXG4gICAgICAgIDwvVGV4dD5cbiAgICAgICAgPFhTdGFjayBtdD17MTB9IHdpZHRoPXsyNjB9IGhlaWdodD17MTgwfSBib3JkZXJSYWRpdXM9ezEwfSBiZz1cIiMxNDE1MTlcIiBweD17MTR9IHB5PXsxNH0+XG4gICAgICAgICAgPFlTdGFjaz5cbiAgICAgICAgICAgIDxYU3RhY2sgbWI9XCIkNFwiPlxuICAgICAgICAgICAgICA8SW1hZ2VcbiAgICAgICAgICAgICAgICBzb3VyY2U9e3sgdXJpOiAnJyB9fVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAyOCwgaGVpZ2h0OiAyOCwgYm9yZGVyUmFkaXVzOiAnNTAlJywgYmFja2dyb3VuZDogJyMyQjJCMkInIH19XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxWaWV3IG1sPVwiJDJcIj5cbiAgICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cIndoaXRlXCIgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PVwiYm9sZFwiPlxuICAgICAgICAgICAgICAgICAgS1RBXG4gICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICAgIDxUZXh0IGNvbG9yPVwiIzhCOEY5QVwiIGZvbnRTaXplPXsxMn0gZm9udFdlaWdodD17NTAwfSBtdD17Nn0+XG4gICAgICAgICAgICAgICAgICBVUyQwLjU0XG4gICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgICAgIDxUZXh0IGNvbG9yPVwiI0M3NTQ1RVwiIGZvbnRTaXplPXsxNH0gZm9udFdlaWdodD1cImJvbGRcIiBtbD1cIiQ0XCI+XG4gICAgICAgICAgICAgICAgMTUuNTElXG4gICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgIDwvWFN0YWNrPlxuICAgICAgICAgICAgPFhTdGFjayBhbGlnbkl0ZW1zPVwiY2VudGVyXCIgPlxuICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cIndoaXRlXCIgZm9udFNpemU9ezIwfSBmb250V2VpZ2h0PVwiYm9sZFwiPlxuICAgICAgICAgICAgICAgIDY4MVxuICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgIDxUZXh0IGNvbG9yPVwid2hpdGVcIiBmb250U2l6ZT17MTZ9IGZvbnRXZWlnaHQ9XCJib2xkXCIgbWw9XCIkMVwiPlxuICAgICAgICAgICAgICAgIOWFkeaNolxuICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgIDxWaWV3IGJnPVwiIzI4MkIzMlwiIHdpZHRoPXsxMzd9IGhlaWdodD17MzR9IGJvcmRlclJhZGl1cz17MjB9IG1sPVwiJDVcIiBvblByZXNzPXsoKSA9PiB7IHJvdXRlci5wdXNoKCcvd2FsbGV0L2NvbnZlcnQnKSB9fSBhbGlnbkl0ZW1zPVwiY2VudGVyXCIganVzdGlmeUNvbnRlbnQ9XCJjZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cIndoaXRlXCIgdGV4dD1cImNlbnRlclwiIGxpbmVIZWlnaHQ9ezM0fT5cbiAgICAgICAgICAgICAgICAgIOWFkeaNolxuICAgICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgICAgPC9YU3RhY2s+XG4gICAgICAgICAgICA8WFN0YWNrIG10PXsyMH0+XG4gICAgICAgICAgICAgIDxWaWV3IHdpZHRoPXsxMjR9IGhlaWdodD17NH0gYmc9XCIjMkZBQjc3XCIgYm9yZGVyUmFkaXVzPXsyMH0+PC9WaWV3PlxuICAgICAgICAgICAgICA8VmlldyB3aWR0aD17MTAwfSBoZWlnaHQ9ezR9IGJnPVwiI0M3NTQ1RVwiIGJvcmRlclJhZGl1cz17MjB9PjwvVmlldz5cbiAgICAgICAgICAgIDwvWFN0YWNrPlxuICAgICAgICAgICAgPFhTdGFjayBtdD17MjB9PlxuICAgICAgICAgICAgICA8WFN0YWNrIGZsZXg9ezF9IGFsaWduSXRlbXM9XCJjZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8VmlldyB3aWR0aD17Nn0gaGVpZ2h0PXs2fSBiZz1cIiMyRkFCNzdcIiBib3JkZXJSYWRpdXM9ezN9IC8+XG4gICAgICAgICAgICAgICAgPFRleHQgZm9udFNpemU9ezEyfSBjb2xvcj1cIiM4QjhGOUFcIiBtbD1cIiQyXCI+XG4gICAgICAgICAgICAgICAgICDlt7LotK3kubAgNzAlXG4gICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICA8L1hTdGFjaz5cbiAgICAgICAgICAgICAgPFhTdGFjayBmbGV4PXsxfSBhbGlnbkl0ZW1zPVwiY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPFZpZXcgd2lkdGg9ezZ9IGhlaWdodD17Nn0gYmc9XCIjQzc1NDVFXCIgYm9yZGVyUmFkaXVzPXszfSAvPlxuICAgICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxMn0gY29sb3I9XCIjOEI4RjlBXCIgbWw9XCIkMlwiPlxuICAgICAgICAgICAgICAgICAg5bey5ZSu5Ye6IDMwJXsnICd9XG4gICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICA8L1hTdGFjaz5cbiAgICAgICAgICAgIDwvWFN0YWNrPlxuICAgICAgICAgIDwvWVN0YWNrPlxuXG4gICAgICAgIDwvWFN0YWNrPlxuICAgICAgPC9ZU3RhY2s+XG4gICAgICA8VW5kZXJsaW5lIC8+XG4gICAgICA8WVN0YWNrIG10PXsyMH0+XG4gICAgICAgIDxWaWV3PlxuICAgICAgICAgIDxJbWFnZSBzb3VyY2U9e21pbnQxSW1nLnNyY30gc3R5bGU9e3sgd2lkdGg6ICcxMDAlJywgaGVpZ2h0OiAyMjgsIGJvcmRlclJhZGl1czogMTIgfX0gLz5cbiAgICAgICAgICA8VGV4dCBjb2xvcj1cIndoaXRlXCIgZm9udFNpemU9ezEyfSBmb250V2VpZ2h0PVwiYm9sZFwiIG10PXs2fT5cbiAgICAgICAgICAgIFNwb25zb3JlZFxuICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTZ9IGNvbG9yPVwid2hpdGVcIiBmb250V2VpZ2h0PVwiYm9sZFwiIG10PXs2fT5cbiAgICAgICAgICAgIEluLUFwcCBCcmlkZ2luZyBpcyBIZXJlXG4gICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgIDxUZXh0IGNvbG9yPVwiIzhCOEY5QVwiIGZvbnRTaXplPXsxMn0gZm9udFdlaWdodD17NTAwfSBtdD17Nn0+XG4gICAgICAgICAgICBGb3Igd2hlbiB5b3UgcmVhbGx5LCByZWFsbHkgd2FudCB0aGF0IG9uZSB0b2tlbi4gT250aGF0IG90aGVyIGNoYWluLlxuICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICA8WFN0YWNrIG10PVwiJDRcIiBhbGlnbkl0ZW1zPVwiY2VudGVyXCIgZ2FwPVwiJDVcIj5cbiAgICAgICAgICAgIDxWaWV3IGJnPVwiIzI4MkIzMlwiIHdpZHRoPXsxMzd9IGhlaWdodD17MzR9IGJvcmRlclJhZGl1cz17MjB9IGFsaWduSXRlbXM9XCJjZW50ZXJcIj5cbiAgICAgICAgICAgICAgPFRleHQgY29sb3I9XCJ3aGl0ZVwiIGZvbnRTaXplPXsxNH0gZm9udFdlaWdodD1cImJvbGRcIiBsaW5lSGVpZ2h0PXszNH0+XG4gICAgICAgICAgICAgICAgTGVhcm4gTW9yZVxuICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgICA8VGV4dCBjb2xvcj1cIndoaXRlXCIgZm9udFNpemU9ezEyfSBmb250V2VpZ2h0PVwiYm9sZFwiPlxuICAgICAgICAgICAgICBEaXNtaXNzXG4gICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgPC9YU3RhY2s+XG4gICAgICAgIDwvVmlldz5cbiAgICAgIDwvWVN0YWNrPlxuICAgICAgPFVuZGVybGluZSAvPlxuICAgICAgPFlTdGFjayBtdD17MjB9PlxuICAgICAgICA8Vmlldz5cbiAgICAgICAgICA8VGV4dCBjb2xvcj1cIndoaXRlXCIgZm9udFNpemU9ezE2fSBmb250V2VpZ2h0PVwiYm9sZFwiIG1iPXsxMH0+XG4gICAgICAgICAgICBUcmVuZGluZyBvbmNoYWluXG4gICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgIDxJbWFnZSBzb3VyY2U9e21pbnQySW1nLnNyY30gc3R5bGU9e3sgd2lkdGg6ICcxMDAlJywgaGVpZ2h0OiAyMjgsIGJvcmRlclJhZGl1czogMTIgfX0gLz5cbiAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTZ9IGNvbG9yPVwid2hpdGVcIiBmb250V2VpZ2h0PVwiYm9sZFwiIG10PXs2fT5cbiAgICAgICAgICAgIERyaWZ0ZXJzXG4gICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgIDxUZXh0IGNvbG9yPVwiIzhCOEY5QVwiIGZvbnRTaXplPXsxMn0gZm9udFdlaWdodD17NTAwfSBtdD17Nn0+XG4gICAgICAgICAgICBEcmlmdGVycyBhcmUgaGFuZGNyYWZ0ZWQsIGZ1bGx5IGN1c3RvbWl6Li5cbiAgICAgICAgICA8L1RleHQ+XG4gICAgICAgIDwvVmlldz5cbiAgICAgIDwvWVN0YWNrPlxuICAgICAgPFVuZGVybGluZSAvPlxuICAgICAgPFlTdGFjayBtdD17MjB9PlxuICAgICAgICA8VGV4dCBjb2xvcj1cIndoaXRlXCIgZm9udFNpemU9ezE2fT5cbiAgICAgICAgICBORlQgbWludHMgZm9yIHlvdVxuICAgICAgICA8L1RleHQ+XG4gICAgICA8L1lTdGFjaz5cbiAgICA8L1lTdGFjaz5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiWFN0YWNrIiwiWVN0YWNrIiwiSW1hZ2UiLCJUZXh0Iiwic3R5bGVkIiwiVmlldyIsImNsb3NlSWNvbiIsIm1haW5Db25uZXRJY29uIiwibWludDFJbWciLCJtaW50MkltZyIsInVzZVJvdXRlciIsInVzZVRyYW5zbGF0aW9uIiwiVW5kZXJsaW5lIiwid2lkdGgiLCJoZWlnaHQiLCJiYWNrZ3JvdW5kQ29sb3IiLCJtdCIsIkFjdGl2ZUJsb2NrIiwiYmFja2dyb3VuZCIsImJvcmRlclJhZGl1cyIsIkJsb2NrIiwiU3dpcGVyQ29udGFpbmVyIiwicG9zaXRpb24iLCJvdmVyZmxvdyIsIlN3aXBlcldyYXBwZXIiLCJkaXNwbGF5IiwiZmxleERpcmVjdGlvbiIsInRyYW5zaXRpb24iLCJTd2lwZXJTbGlkZSIsImZsZXhTaHJpbmsiLCJIb21lUGFnZSIsInBhZ2VzTW9kZSIsImN1cnJlbnRQYWdlIiwic2V0Q3VycmVudFBhZ2UiLCJ0b3RhbFBhZ2VzIiwidG91Y2hTdGFydCIsInNldFRvdWNoU3RhcnQiLCJ0b3VjaEVuZCIsInNldFRvdWNoRW5kIiwicm91dGVyIiwidCIsImhhbmRsZVRvdWNoU3RhcnQiLCJlIiwidGFyZ2V0VG91Y2hlcyIsImNsaWVudFgiLCJoYW5kbGVUb3VjaE1vdmUiLCJoYW5kbGVUb3VjaEVuZCIsImRpc3RhbmNlIiwiaXNMZWZ0U3dpcGUiLCJpc1JpZ2h0U3dpcGUiLCJzd2lwZXJEYXRhIiwiaWQiLCJ0aXRsZSIsInJlcGxhY2UiLCJkZXNjIiwidHJlbmRpbmdTd2Fwc0RhdGEiLCJuYW1lIiwicHJpY2UiLCJjaGFuZ2UiLCJjaGFuZ2VDb2xvciIsInN3YXBzIiwiYnV5UGVyY2VudGFnZSIsInNlbGxQZXJjZW50YWdlIiwiaGFuZGxlQWN0aW9uIiwiYWN0aW9uIiwicHVzaCIsImRhdGEtYXQiLCJkYXRhLWluIiwiZGF0YS1pcyIsImZsZXgiLCJhbGlnbkNvbnRlbnQiLCJqdXN0aWZ5Q29udGVudCIsImdhcCIsIm1hcCIsImluZGV4Iiwic291cmNlIiwic3JjIiwic3R5bGUiLCJvblRvdWNoU3RhcnQiLCJvblRvdWNoTW92ZSIsIm9uVG91Y2hFbmQiLCJ0cmFuc2Zvcm0iLCJpdGVtIiwibWIiLCJmbGV4V3JhcCIsImNvbG9yIiwiZm9udFNpemUiLCJmb250V2VpZ2h0Iiwib25QcmVzcyIsImJnIiwicCIsImFsaWduSXRlbXMiLCJweCIsInB5IiwidXJpIiwibWwiLCJ0ZXh0IiwibGluZUhlaWdodCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/app/features/home/<USER>"));

/***/ })

});