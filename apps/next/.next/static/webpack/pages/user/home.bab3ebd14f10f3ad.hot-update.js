"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/user/home",{

/***/ "../../packages/app/features/home/<USER>":
/*!***************************************************!*\
  !*** ../../packages/app/features/home/<USER>
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FooterNavBar: function() { return /* binding */ FooterNavBar; },\n/* harmony export */   FotIconContainer: function() { return /* binding */ FotIconContainer; },\n/* harmony export */   HomeScreen: function() { return /* binding */ HomeScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/search.png */ \"../../packages/assets/images/search.png\");\n/* harmony import */ var _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/copy.png */ \"../../packages/assets/images/copy.png\");\n/* harmony import */ var _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/setting.png */ \"../../packages/assets/images/setting.png\");\n/* harmony import */ var _assets_images_fot_icon_1_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/fot-icon-1.png */ \"../../packages/assets/images/fot-icon-1.png\");\n/* harmony import */ var _assets_images_fot_icon_2_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../assets/images/fot-icon-2.png */ \"../../packages/assets/images/fot-icon-2.png\");\n/* harmony import */ var _assets_images_fot_icon_3_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../assets/images/fot-icon-3.png */ \"../../packages/assets/images/fot-icon-3.png\");\n/* harmony import */ var _assets_images_fot_icon_4_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../assets/images/fot-icon-4.png */ \"../../packages/assets/images/fot-icon-4.png\");\n/* harmony import */ var _assets_images_fot_icon_5_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../assets/images/fot-icon-5.png */ \"../../packages/assets/images/fot-icon-5.png\");\n/* harmony import */ var _assets_images_fot_icon_1_active_png__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../../assets/images/fot-icon-1-active.png */ \"../../packages/assets/images/fot-icon-1-active.png\");\n/* harmony import */ var _assets_images_fot_icon_2_active_png__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../../assets/images/fot-icon-2-active.png */ \"../../packages/assets/images/fot-icon-2-active.png\");\n/* harmony import */ var _assets_images_fot_icon_3_active_png__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../../assets/images/fot-icon-3-active.png */ \"../../packages/assets/images/fot-icon-3-active.png\");\n/* harmony import */ var _assets_images_fot_icon_4_active_png__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../../assets/images/fot-icon-4-active.png */ \"../../packages/assets/images/fot-icon-4-active.png\");\n/* harmony import */ var _assets_images_fot_icon_5_active_png__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../../assets/images/fot-icon-5-active.png */ \"../../packages/assets/images/fot-icon-5-active.png\");\n/* harmony import */ var _homePage__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./homePage */ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_15__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c = ActiveText;\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_15__.styled)(react_native__WEBPACK_IMPORTED_MODULE_16__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c1 = Underline;\nconst FotIconList = [\n    _assets_images_fot_icon_1_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    _assets_images_fot_icon_2_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    _assets_images_fot_icon_3_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    _assets_images_fot_icon_4_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    _assets_images_fot_icon_5_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n];\nconst FotIconListActive = [\n    _assets_images_fot_icon_1_active_png__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    _assets_images_fot_icon_2_active_png__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    _assets_images_fot_icon_3_active_png__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    _assets_images_fot_icon_4_active_png__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    _assets_images_fot_icon_5_active_png__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n];\nconst FotIconContainer = (0,tamagui__WEBPACK_IMPORTED_MODULE_15__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n    maxWidth: 640,\n    margin: \"auto\",\n    alignItems: \"center\",\n    position: \"absolute\",\n    bottom: 0,\n    left: 0,\n    right: 0,\n    height: 80,\n    backgroundColor: \"#131518\",\n    paddingHorizontal: 20,\n    cursor: \"pointer\",\n    zIndex: 100\n});\n_c2 = FotIconContainer;\nconst FooterNavBar = ()=>{\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useRouter)();\n    const pathname = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_17__.usePathname)();\n    const fotLinks = [\n        \"/\",\n        \"/wallet/network\",\n        \"/wallet/buy\",\n        \"/wallet/exchange\",\n        \"/user/home\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FotIconContainer, {\n        justifyContent: \"space-between\",\n        children: FotIconList.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_16__.Pressable, {\n                onPress: ()=>{\n                    router.push(fotLinks[index]);\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Image, {\n                    \"data-at\": \"screen.tsx:92-95\",\n                    \"data-in\": \"FooterNavBar\",\n                    \"data-is\": \"Image\",\n                    source: pathname === fotLinks[index] ? FotIconListActive[index].src : item.src,\n                    style: {\n                        width: 40,\n                        height: 40\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, undefined)\n            }, index, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 58,\n                columnNumber: 41\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n        lineNumber: 57,\n        columnNumber: 10\n    }, undefined);\n};\n_s(FooterNavBar, \"gA9e4WsoP6a20xDgQgrFkfMP8lc=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useRouter,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_17__.usePathname\n    ];\n});\n_c3 = FooterNavBar;\nfunction HomeScreen(param) {\n    let { pagesMode = false } = param;\n    _s1();\n    const linkTarget = pagesMode ? \"/user\" : \"/user\";\n    const toast = (0,_my_ui__WEBPACK_IMPORTED_MODULE_18__.useToastController)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_19__.useTranslation)();\n    const linkProps = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useLink)({\n        href: \"\".concat(linkTarget, \"/nate\")\n    });\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentAccount, setCurrentAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_20__.useWalletStore)();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useRouter)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWalletId, setSelectedWalletId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        walletStore.init();\n        // 如果没有钱包数据，加载测试数据\n        if (walletStore.walletList.length === 0) {\n            console.log(\"没有钱包数据，加载测试数据\");\n            // const testWalletData = [\n            //   {\n            //     \"mnemonic\": \"below neutral satoshi inhale hotel inhale humor forum visual citizen element seat\",\n            //     \"walletId\": \"cb1bfc86-1d02-4f3f-b52a-dca1988297c2\",\n            //     \"accounts\": [\n            //       {\n            //         \"walletId\": \"cb1bfc86-1d02-4f3f-b52a-dca1988297c2\",\n            //         \"accountId\": \"61053e03-2dbb-4e39-ac12-173dc80da9a6\",\n            //         \"name\": \"主钱包\",\n            //         \"btc\": {\n            //           \"uid\": \"97cba0ac-5708-44b7-be93-bebbb2939c94\",\n            //           \"address\": \"**********************************\",\n            //           \"privateKey\": \"97ca01b8eb25fc932f875df3acd2b3dc4b7f65090d575d4953f4432fad054bcb\",\n            //           \"accountType\": \"btc\"\n            //         },\n            //         \"eth\": {\n            //           \"uid\": \"c3aff85b-0f2a-40db-abf7-d14d8e6299a8\",\n            //           \"address\": \"******************************************\",\n            //           \"privateKey\": \"0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18c\",\n            //           \"accountType\": \"eth\"\n            //         },\n            //         \"bsc\": {\n            //           \"uid\": \"7b1e48d5-4c27-48c9-b0c2-70f4a7f4c10e\",\n            //           \"address\": \"******************************************\",\n            //           \"privateKey\": \"0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18c\",\n            //           \"accountType\": \"bsc\"\n            //         },\n            //         \"solana\": {\n            //           \"uid\": \"e1e06ec5-c947-4a21-b516-0743596c8064\",\n            //           \"address\": \"C5ThiTSKBAhYufh6uqLwzp1SGqWSHtTuE7guRKyVcaXx\",\n            //           \"privateKey\": \"71143f84a0b1a3beb45cd2882bc1f4a13692465e407aac824207fa170ba781c6a495124a84be84ca5b12032ab0b89950ce0fcc570ec5d545221193ea669cab5f\",\n            //           \"accountType\": \"solana\"\n            //         }\n            //       },\n            //       {\n            //         \"walletId\": \"cb1bfc86-1d02-4f3f-b52a-dca1988297c2\",\n            //         \"accountId\": \"61053e03-2dbb-4e39-ac12-173dc80da9a7\",\n            //         \"name\": \"备用钱包\",\n            //         \"btc\": {\n            //           \"uid\": \"97cba0ac-5708-44b7-be93-bebbb2939c95\",\n            //           \"address\": \"**********************************\",\n            //           \"privateKey\": \"97ca01b8eb25fc932f875df3acd2b3dc4b7f65090d575d4953f4432fad054bcd\",\n            //           \"accountType\": \"btc\"\n            //         },\n            //         \"eth\": {\n            //           \"uid\": \"c3aff85b-0f2a-40db-abf7-d14d8e6299a9\",\n            //           \"address\": \"******************************************\",\n            //           \"privateKey\": \"0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18d\",\n            //           \"accountType\": \"eth\"\n            //         },\n            //         \"bsc\": {\n            //           \"uid\": \"7b1e48d5-4c27-48c9-b0c2-70f4a7f4c10f\",\n            //           \"address\": \"******************************************\",\n            //           \"privateKey\": \"0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18d\",\n            //           \"accountType\": \"bsc\"\n            //         },\n            //         \"solana\": {\n            //           \"uid\": \"e1e06ec5-c947-4a21-b516-0743596c8065\",\n            //           \"address\": \"D5ThiTSKBAhYufh6uqLwzp1SGqWSHtTuE7guRKyVcaXy\",\n            //           \"privateKey\": \"71143f84a0b1a3beb45cd2882bc1f4a13692465e407aac824207fa170ba781c6a495124a84be84ca5b12032ab0b89950ce0fcc570ec5d545221193ea669cab60\",\n            //           \"accountType\": \"solana\"\n            //         }\n            //       }\n            //     ]\n            //   }\n            // ]\n            // localStorage.setItem('WALLET_LIST', JSON.stringify(testWalletData))\n            walletStore.init(); // 重新初始化\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (walletStore.currentAccount && walletStore.currentAccount.accountId) {\n            // 从 store 中获取当前账户，并设置显示名称\n            const account = walletStore.currentAccount;\n            // 如果账户有自定义名称，使用自定义名称；否则使用默认格式\n            let accountName = account.name;\n            if (!accountName) {\n                // 查找账户在钱包列表中的索引来生成默认名称\n                let accountIndex = 1;\n                for (const wallet of walletStore.walletList){\n                    const foundIndex = wallet.accounts.findIndex((acc)=>acc.accountId === account.accountId);\n                    if (foundIndex !== -1) {\n                        accountIndex = foundIndex + 1;\n                        break;\n                    }\n                }\n                accountName = (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(accountIndex));\n            }\n            setCurrentAccount({\n                ...account,\n                accountName\n            });\n            setSelectedWalletId(account.accountId);\n        }\n    }, [\n        walletStore.currentAccount,\n        walletStore.walletList,\n        t\n    ]);\n    const handleAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (action)=>{\n        if (action === \"search\") {\n            router.push(\"/wallet/search\");\n        }\n        if (action === \"copy\") {\n            try {\n                await navigator.clipboard.writeText(currentAccount.eth.address);\n                toast.show(t(\"success.addressCopied\") || \"地址已复制到剪贴板\", {\n                    duration: 2000\n                });\n            } catch (err) {\n                toast.show(t(\"home.copyFailed\") || \"复制失败，请手动复制\", {\n                    duration: 2000\n                });\n            }\n        }\n        if (action === \"setting\") {\n            router.push(\"/wallet/setting\");\n        }\n    }, [\n        currentAccount\n    ]);\n    // 手动刷新余额\n    const handleRefreshBalance = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (isRefreshing) return;\n        setIsRefreshing(true);\n        try {\n            await walletStore.fetchAllBalances();\n            toast.show(t(\"success.balanceRefreshed\") || \"余额已刷新\", {\n                duration: 2000\n            });\n        } catch (error) {\n            toast.show(t(\"error.refreshFailed\") || \"刷新失败，请稍后重试\", {\n                duration: 2000\n            });\n        } finally{\n            setIsRefreshing(false);\n        }\n    }, [\n        isRefreshing,\n        walletStore,\n        toast,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n        \"data-at\": \"screen.tsx:263\",\n        \"data-in\": \"HomeScreen\",\n        \"data-is\": \"YStack\",\n        height: \"100vh\",\n        bg: \"#0A0B0D\",\n        width: \"100%\",\n        maxW: 640,\n        margin: \"auto\",\n        overflow: \"hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n                \"data-at\": \"screen.tsx:264\",\n                \"data-in\": \"HomeScreen\",\n                \"data-is\": \"YStack\",\n                flex: 1,\n                gap: \"$3\",\n                p: \"$4\",\n                overflow: \"scroll\",\n                pb: 100,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.XStack, {\n                        \"data-at\": \"screen.tsx:265\",\n                        \"data-in\": \"HomeScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        space: \"$2\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.XStack, {\n                                \"data-at\": \"screen.tsx:266-272\",\n                                \"data-in\": \"HomeScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                space: \"$2\",\n                                onPress: ()=>{\n                                    setIsOpen(true);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Avatar, {\n                                        circular: true,\n                                        size: 24,\n                                        mr: 6,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Avatar.Image, {\n                                                src: \"https://api.dicebear.com/7.x/identicon/svg?seed=\".concat(currentAccount.accountId),\n                                                accessibilityLabel: currentAccount.accountId\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Avatar.Fallback, {\n                                                backgroundColor: \"$blue10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                        \"data-at\": \"screen.tsx:280\",\n                                        \"data-in\": \"HomeScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#8B8F9A\",\n                                        fontSize: 14,\n                                        children: currentAccount.accountName\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_21__.ChevronDown, {\n                                        color: \"#8B8F9A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.XStack, {\n                                \"data-at\": \"screen.tsx:285\",\n                                \"data-in\": \"HomeScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                gap: \"$3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n                                        \"data-at\": \"screen.tsx:286\",\n                                        \"data-in\": \"HomeScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"search\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Image, {\n                                            \"data-at\": \"screen.tsx:287\",\n                                            \"data-in\": \"HomeScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n                                        \"data-at\": \"screen.tsx:289\",\n                                        \"data-in\": \"HomeScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"copy\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Image, {\n                                            \"data-at\": \"screen.tsx:290\",\n                                            \"data-in\": \"HomeScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20,\n                                                marginHorizontal: 8\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n                                        \"data-at\": \"screen.tsx:292\",\n                                        \"data-in\": \"HomeScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"setting\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Image, {\n                                            \"data-at\": \"screen.tsx:293\",\n                                            \"data-in\": \"HomeScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                                            width: 18,\n                                            style: {\n                                                width: 18,\n                                                height: 18\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.XStack, {\n                        \"data-at\": \"screen.tsx:297\",\n                        \"data-in\": \"HomeScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$2\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.H2, {\n                            \"data-at\": \"screen.tsx:298\",\n                            \"data-in\": \"HomeScreen\",\n                            \"data-is\": \"H2\",\n                            textAlign: \"left\",\n                            color: \"#fff\",\n                            children: [\n                                \"$ \",\n                                walletStore.getCurrentAccountBalance().toFixed(4)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_homePage__WEBPACK_IMPORTED_MODULE_22__.HomePage, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.WalletSheet, {\n                open: isOpen,\n                onOpenChange: setIsOpen,\n                wallets: walletStore.walletList,\n                selectedId: selectedWalletId,\n                onSelect: (wallet, index)=>{\n                    if (wallet === \"addWallet\") {\n                        router.push(\"/wallet/manager\");\n                    } else {\n                        // 使用 store 的方法设置当前账户\n                        walletStore.setCurrentAccount(wallet);\n                        setSelectedWalletId(wallet.accountId);\n                        // 设置显示名称\n                        const accountName = wallet.name || (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(Number(index) + 1));\n                        setCurrentAccount({\n                            ...wallet,\n                            accountName\n                        });\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n        lineNumber: 230,\n        columnNumber: 10\n    }, this);\n}\n_s1(HomeScreen, \"Dv8Je33w+g6qB19H0e0sfZ7Y2kY=\", false, function() {\n    return [\n        _my_ui__WEBPACK_IMPORTED_MODULE_18__.useToastController,\n        app_i18n__WEBPACK_IMPORTED_MODULE_19__.useTranslation,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useLink,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_20__.useWalletStore,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useRouter\n    ];\n});\n_c4 = HomeScreen;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ActiveText\");\n$RefreshReg$(_c1, \"Underline\");\n$RefreshReg$(_c2, \"FotIconContainer\");\n$RefreshReg$(_c3, \"FooterNavBar\");\n$RefreshReg$(_c4, \"HomeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/home/<USER>"));

/***/ })

});