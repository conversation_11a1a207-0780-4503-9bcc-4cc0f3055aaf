"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/user/home",{

/***/ "../../packages/app/features/user/screen.tsx":
/*!***************************************************!*\
  !*** ../../packages/app/features/user/screen.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserScreen: function() { return /* binding */ UserScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/search.png */ \"../../packages/assets/images/search.png\");\n/* harmony import */ var _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/copy.png */ \"../../packages/assets/images/copy.png\");\n/* harmony import */ var _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/setting.png */ \"../../packages/assets/images/setting.png\");\n/* harmony import */ var _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/main-connect.png */ \"../../packages/assets/images/main-connect.png\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n/* harmony import */ var _assets_images_wallet_bitcoin_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../assets/images/wallet/bitcoin.png */ \"../../packages/assets/images/wallet/bitcoin.png\");\n/* harmony import */ var _assets_images_wallet_bnb_smart_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../assets/images/wallet/bnb_smart.png */ \"../../packages/assets/images/wallet/bnb_smart.png\");\n/* harmony import */ var _assets_images_wallet_solana_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../assets/images/wallet/solana.png */ \"../../packages/assets/images/wallet/solana.png\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_utils_storage__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! app/utils/storage */ \"../../packages/app/utils/storage.ts\");\n/* harmony import */ var app_utils_constants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! app/utils/constants */ \"../../packages/app/utils/constants.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_10__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c = ActiveText;\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_10__.styled)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c1 = Underline;\nfunction UserScreen(param) {\n    let { pagesMode = false } = param;\n    _s();\n    const toast = (0,_my_ui__WEBPACK_IMPORTED_MODULE_12__.useToastController)();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_14__.useTranslation)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__.useWalletStore)();\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(4);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWalletId, setSelectedWalletId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentAccount, setCurrentAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        walletStore.init();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (walletStore.currentAccount && walletStore.currentAccount.accountId) {\n            // 从 store 中获取当前账户，并设置显示名称\n            const account = walletStore.currentAccount;\n            // 如果账户有自定义名称，使用自定义名称；否则使用默认格式\n            let accountName = account.name;\n            if (!accountName) {\n                // 查找账户在钱包列表中的索引来生成默认名称\n                let accountIndex = 1;\n                for (const wallet of walletStore.walletList){\n                    const foundIndex = wallet.accounts.findIndex((acc)=>acc.accountId === account.accountId);\n                    if (foundIndex !== -1) {\n                        accountIndex = foundIndex + 1;\n                        break;\n                    }\n                }\n                accountName = (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(accountIndex));\n            }\n            setCurrentAccount({\n                ...account,\n                accountName\n            });\n            setSelectedWalletId(account.accountId);\n        }\n    }, [\n        walletStore.currentAccount,\n        walletStore.walletList,\n        t,\n        app_i18n__WEBPACK_IMPORTED_MODULE_14__.useI18nStore.getState().translations\n    ]);\n    // 手动刷新余额\n    const handleRefreshBalance = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (isRefreshing) return;\n        setIsRefreshing(true);\n        try {\n            await walletStore.fetchAllBalances();\n            toast.show(t(\"success.balanceRefreshed\") || \"余额已刷新\", {\n                duration: 2000\n            });\n        } catch (error) {\n            toast.show(t(\"error.refreshFailed\") || \"刷新失败，请稍后重试\", {\n                duration: 2000\n            });\n        } finally{\n            setIsRefreshing(false);\n        }\n    }, [\n        isRefreshing,\n        walletStore,\n        toast,\n        t\n    ]);\n    const handleAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function(action) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        if (action === \"search\") {\n            router.push(\"/wallet/search\");\n        }\n        if (action === \"copy\") {\n            try {\n                await navigator.clipboard.writeText(currentAccount.eth.address);\n                toast.show(t(\"success.addressCopied\") || \"地址已复制到剪贴板\", {\n                    duration: 2000\n                });\n            } catch (err) {\n                toast.show(t(\"home.copyFailed\") || \"复制失败，请手动复制\", {\n                    duration: 2000\n                });\n            }\n        }\n        if (action === \"setting\") {\n            router.push(\"/wallet/setting\");\n        }\n        if (action === \"exchange\") {\n            router.push(\"/wallet/convert\");\n        }\n        if (action === \"buy\") {\n            router.push(\"/wallet/buy\");\n        }\n        if (action === \"buyRise\") {\n            app_utils_storage__WEBPACK_IMPORTED_MODULE_16__[\"default\"].setItem(\"buyRiseAccount\", JSON.stringify(data));\n            router.push(\"/wallet/buyRise\");\n        }\n        if (action === \"tixian\") {\n            window.open(app_utils_constants__WEBPACK_IMPORTED_MODULE_17__.TIXIAN_URL, \"_blank\");\n        }\n    }, [\n        currentAccount\n    ]);\n    // 获取当前地址有余额的链\n    const balanceFilterList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const keys = Object.keys(walletStore.currentAccount);\n        let _accountList = [];\n        keys.forEach((key)=>{\n            const _item = walletStore.currentAccount[key];\n            if (Number(_item === null || _item === void 0 ? void 0 : _item.balance) > 0) {\n                const _account = walletStore.currentAccount[key];\n                if (_account.accountType === \"btc\") {\n                    _account.name = \"Bitcoin\";\n                    _account.logo = _assets_images_wallet_bitcoin_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n                }\n                if (_account.accountType === \"eth\") {\n                    _account.name = \"Ethereum\";\n                    _account.logo = _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                }\n                if (_account.accountType === \"bsc\") {\n                    _account.name = \"BNB Smart\";\n                    _account.logo = _assets_images_wallet_bnb_smart_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n                }\n                if (_account.accountType === \"solana\") {\n                    _account.name = \"Solana\";\n                    _account.logo = _assets_images_wallet_solana_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                }\n                _accountList.push(_account);\n            }\n        });\n        return _accountList;\n    }, [\n        walletStore.currentAccount\n    ]);\n    console.log(balanceFilterList);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n        \"data-at\": \"screen.tsx:177\",\n        \"data-in\": \"UserScreen\",\n        \"data-is\": \"YStack\",\n        height: \"100vh\",\n        bg: \"#0A0B0D\",\n        width: \"100%\",\n        maxW: 640,\n        margin: \"auto\",\n        overflow: \"hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                \"data-at\": \"screen.tsx:178\",\n                \"data-in\": \"UserScreen\",\n                \"data-is\": \"YStack\",\n                flex: 1,\n                gap: \"$3\",\n                p: \"$4\",\n                overflow: \"scroll\",\n                pb: 100,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:179\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        space: \"$2\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                \"data-at\": \"screen.tsx:180-186\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                space: \"$2\",\n                                onPress: ()=>{\n                                    setIsOpen(true);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                        circular: true,\n                                        size: 24,\n                                        mr: 6,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar.Image, {\n                                                src: \"https://api.dicebear.com/7.x/identicon/svg?seed=\".concat(currentAccount.accountId),\n                                                accessibilityLabel: currentAccount.accountId\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar.Fallback, {\n                                                backgroundColor: \"$blue10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                        \"data-at\": \"screen.tsx:195\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#8B8F9A\",\n                                        fontSize: 14,\n                                        children: currentAccount.accountName\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__.ChevronDown, {\n                                        color: \"#8B8F9A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                \"data-at\": \"screen.tsx:200\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                gap: \"$3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:201\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"search\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:202\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:204\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"copy\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:205\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20,\n                                                marginHorizontal: 8\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:207\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"setting\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:208\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                                            width: 18,\n                                            style: {\n                                                width: 18,\n                                                height: 18\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:212\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$2\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.H2, {\n                            \"data-at\": \"screen.tsx:213\",\n                            \"data-in\": \"UserScreen\",\n                            \"data-is\": \"H2\",\n                            textAlign: \"left\",\n                            color: \"#fff\",\n                            children: [\n                                \"$ \",\n                                walletStore.getCurrentAccountBalance().toFixed(4)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:238\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        gap: \"$2\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:239-250\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#333\",\n                                    background: \"linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)\"\n                                },\n                                onPress: ()=>handleAction(\"buy\"),\n                                children: \"买入\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:253-261\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#fff\",\n                                    background: \"#282B32\"\n                                },\n                                onPress: ()=>handleAction(\"exchange\"),\n                                children: \"兑换\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:264-272\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#8B8F9A\",\n                                    background: \"#15161A\"\n                                },\n                                onPress: ()=>handleAction(\"tixian\"),\n                                children: \"提现\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:276\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        height: 1,\n                        bg: \"#212224\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:277\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$5\",\n                        height: 26,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(0),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"加密货币\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:283\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"加密货币\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 67\n                                        }, this),\n                                        currentTab === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(1),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:290\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 66\n                                        }, this),\n                                        currentTab === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(2),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 2 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"DeFi\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:296\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"DeFi\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 67\n                                        }, this),\n                                        currentTab === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    currentTab === 0 && balanceFilterList.length > 0 ? balanceFilterList.map((item)=>{\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                            \"data-at\": \"screen.tsx:304\",\n                            \"data-in\": \"UserScreen\",\n                            \"data-is\": \"XStack\",\n                            mt: 30,\n                            items: \"center\",\n                            justifyContent: \"space-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                    \"data-at\": \"screen.tsx:305\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"XStack\",\n                                    items: \"center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:306\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: item.logo.src,\n                                            width: 32,\n                                            height: 32,\n                                            mr: 6\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:307\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"white\",\n                                            fontSize: 14,\n                                            fontWeight: \"bold\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                    \"data-at\": \"screen.tsx:311\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"XStack\",\n                                    justifyContent: \"flex-end\",\n                                    onPress: ()=>handleAction(\"buyRise\", item),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                    \"data-at\": \"screen.tsx:313\",\n                                                    \"data-in\": \"UserScreen\",\n                                                    \"data-is\": \"Text\",\n                                                    fontSize: 12,\n                                                    fontWeight: \"bold\",\n                                                    children: item.balance ? parseFloat(item.balance).toFixed(4) : \"0.0000\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                    \"data-at\": \"screen.tsx:316\",\n                                                    \"data-in\": \"UserScreen\",\n                                                    \"data-is\": \"Text\",\n                                                    fontSize: 12,\n                                                    color: \"#8B8F9A\",\n                                                    children: \"可用\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__.ChevronRight, {\n                                            size: 20,\n                                            color: \"$white6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 16\n                        }, this);\n                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                        \"data-at\": \"screen.tsx:327-335\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"YStack\",\n                        cursor: \"pointer\",\n                        margin: \"auto\",\n                        width: 300,\n                        flex: 1,\n                        alignContent: \"center\",\n                        alignItems: \"center\",\n                        mt: \"$15\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentPage(4),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                    \"data-at\": \"screen.tsx:337\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                                    style: {\n                                        width: 174,\n                                        height: 91\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                \"data-at\": \"screen.tsx:339\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 16,\n                                color: \"#fff\",\n                                fontWeight: \"bold\",\n                                mt: \"$4\",\n                                text: \"center\",\n                                children: \"添加加密货币以开始使用\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                \"data-at\": \"screen.tsx:342\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                color: \"#8B8F9A\",\n                                mt: \"$2\",\n                                text: \"center\",\n                                children: \"您可以使用您的 Coinbase 账户或其他钱包添加资产。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 12\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_19__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.WalletSheet, {\n                open: isOpen,\n                onOpenChange: setIsOpen,\n                wallets: walletStore.walletList,\n                selectedId: selectedWalletId,\n                onSelect: (wallet, index)=>{\n                    if (wallet === \"addWallet\") {\n                        router.push(\"/wallet/manager\");\n                    } else {\n                        // 使用 store 的方法设置当前账户\n                        walletStore.setCurrentAccount(wallet);\n                        setSelectedWalletId(wallet.accountId);\n                        // 设置显示名称\n                        const accountName = wallet.name || (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(Number(index) + 1));\n                        setCurrentAccount({\n                            ...wallet,\n                            accountName\n                        });\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n        lineNumber: 160,\n        columnNumber: 10\n    }, this);\n}\n_s(UserScreen, \"qAALGoezLjG3tNjoH0lvTn3Zvik=\", false, function() {\n    return [\n        _my_ui__WEBPACK_IMPORTED_MODULE_12__.useToastController,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_14__.useTranslation,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__.useWalletStore\n    ];\n});\n_c2 = UserScreen;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ActiveText\");\n$RefreshReg$(_c1, \"Underline\");\n$RefreshReg$(_c2, \"UserScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/user/screen.tsx\n"));

/***/ })

});