"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "../../packages/app/features/home/<USER>":
/*!*****************************************************!*\
  !*** ../../packages/app/features/home/<USER>
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HomePage: function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/close.png */ \"../../packages/assets/images/close.png\");\n/* harmony import */ var _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/main-connect.png */ \"../../packages/assets/images/main-connect.png\");\n/* harmony import */ var _assets_images_mint1_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/mint1.png */ \"../../packages/assets/images/mint1.png\");\n/* harmony import */ var _assets_images_mint2_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/mint2.png */ \"../../packages/assets/images/mint2.png\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 10\n});\n_c = Underline;\nconst ActiveBlock = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: 22,\n    height: 2,\n    background: \"#fff\",\n    borderRadius: 10\n});\n_c1 = ActiveBlock;\nconst Block = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: 22,\n    height: 2,\n    background: \"#262729\",\n    borderRadius: 10\n});\n_c2 = Block;\nconst SwiperContainer = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    position: \"relative\",\n    overflow: \"hidden\",\n    width: \"100%\"\n});\n_c3 = SwiperContainer;\nconst SwiperWrapper = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    display: \"flex\",\n    flexDirection: \"row\",\n    transition: \"transform 0.3s ease\",\n    width: \"400%\" // 4页内容，每页100%\n});\n_c4 = SwiperWrapper;\nconst SwiperSlide = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: \"25%\",\n    // 每个slide占25%（因为总宽度是400%）\n    flexShrink: 0\n});\n_c5 = SwiperSlide;\nfunction HomePage(param) {\n    let { pagesMode = false } = param;\n    _s();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const totalPages = 4;\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_8__.useTranslation)();\n    // 处理触摸开始\n    const handleTouchStart = (e)=>{\n        setTouchEnd(0); // 重置touchEnd\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    // 处理触摸移动\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    // 处理触摸结束\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isLeftSwipe = distance > 50;\n        const isRightSwipe = distance < -50;\n        if (isLeftSwipe && currentPage < totalPages - 1) {\n            setCurrentPage(currentPage + 1);\n        }\n        if (isRightSwipe && currentPage > 0) {\n            setCurrentPage(currentPage - 1);\n        }\n    };\n    // 轮播内容数据\n    const swiperData = [\n        {\n            id: 1,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"4.1\") || \"获得 4.1% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Base\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"4.1\") || \"将 Base 上的 USDC 添加到您的钱包，每年可赚取 4.1% 的奖励\"\n        },\n        {\n            id: 2,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"3.8\") || \"获得 3.8% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Ethereum\").replace(\"{token}\", \"USDT\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"3.8\") || \"将 Ethereum 上的 USDT 添加到您的钱包，每年可赚取 3.8% 的奖励\"\n        },\n        {\n            id: 3,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"5.2\") || \"获得 5.2% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Polygon\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"5.2\") || \"将 Polygon 上的 USDC 添加到您的钱包，每年可赚取 5.2% 的奖励\"\n        },\n        {\n            id: 4,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"4.5\") || \"获得 4.5% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Arbitrum\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"4.5\") || \"将 Arbitrum 上的 USDC 添加到您的钱包，每年可赚取 4.5% 的奖励\"\n        }\n    ];\n    const handleAction = (action)=>{\n        if (action === \"watchlist\") {\n            router.push(\"/user/myAttention\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n        \"data-at\": \"homePage.tsx:132\",\n        \"data-in\": \"HomePage\",\n        \"data-is\": \"YStack\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:134\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"homePage.tsx:135\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"XStack\",\n                        mt: 20,\n                        flex: 1,\n                        alignContent: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                \"data-at\": \"homePage.tsx:136\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"XStack\",\n                                gap: \"$2\",\n                                children: [\n                                    0,\n                                    1,\n                                    2,\n                                    3\n                                ].map((index)=>index <= currentPage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveBlock, {}, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 111,\n                                        columnNumber: 63\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Block, {}, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 111,\n                                        columnNumber: 93\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                \"data-at\": \"homePage.tsx:141\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                style: {\n                                    width: 12,\n                                    height: 12\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperContainer, {\n                        onTouchStart: handleTouchStart,\n                        onTouchMove: handleTouchMove,\n                        onTouchEnd: handleTouchEnd,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperWrapper, {\n                            style: {\n                                transform: \"translateX(-\".concat(currentPage * 25, \"%)\")\n                            },\n                            children: swiperData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperSlide, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                        \"data-at\": \"homePage.tsx:155-162\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"XStack\",\n                                        mt: 10,\n                                        flex: 1,\n                                        gap: \"$4\",\n                                        alignContent: \"center\",\n                                        justifyContent: \"space-between\",\n                                        mb: 10,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                                \"data-at\": \"homePage.tsx:163\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Image\",\n                                                source: _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                                style: {\n                                                    width: 50,\n                                                    height: 50\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                                \"data-at\": \"homePage.tsx:164\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"YStack\",\n                                                flex: 1,\n                                                flexWrap: \"wrap\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:165\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"#fff\",\n                                                        fontSize: 14,\n                                                        fontWeight: \"bold\",\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:168\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"#8B8F9A\",\n                                                        fontSize: 12,\n                                                        children: item.desc\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this)\n                                }, item.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 122,\n                                    columnNumber: 46\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:179\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                mb: 20,\n                onPress: ()=>handleAction(\"watchlist\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                        \"data-at\": \"homePage.tsx:180\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"Text\",\n                        color: \"white\",\n                        fontSize: 16,\n                        fontWeight: \"bold\",\n                        children: \"Watchlist\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"homePage.tsx:183-192\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"XStack\",\n                        bg: \"#141519\",\n                        borderRadius: 10,\n                        p: 10,\n                        mt: 10,\n                        height: 70,\n                        flex: 1,\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                \"data-at\": \"homePage.tsx:193\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"YStack\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:194\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"white\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"创建“我的关注”\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:197\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#8B8F9A\",\n                                        fontSize: 12,\n                                        fontWeight: 500,\n                                        mt: 6,\n                                        children: \"获取价格提醒并了解最新信息\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                \"data-at\": \"homePage.tsx:201\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                style: {\n                                    width: 70,\n                                    height: 37\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:205\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                        \"data-at\": \"homePage.tsx:206\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"Text\",\n                        fontSize: 16,\n                        color: \"white\",\n                        fontWeight: \"bold\",\n                        children: \"Trending swaps on Base\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"homePage.tsx:209\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"XStack\",\n                        mt: 10,\n                        width: 260,\n                        height: 180,\n                        borderRadius: 10,\n                        bg: \"#141519\",\n                        px: 14,\n                        py: 14,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                \"data-at\": \"homePage.tsx:210\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"YStack\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                        \"data-at\": \"homePage.tsx:211\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"XStack\",\n                                        mb: \"$4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                                \"data-at\": \"homePage.tsx:212-215\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Image\",\n                                                source: {\n                                                    uri: \"\"\n                                                },\n                                                style: {\n                                                    width: 28,\n                                                    height: 28,\n                                                    borderRadius: \"50%\",\n                                                    background: \"#2B2B2B\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                \"data-at\": \"homePage.tsx:216\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"View\",\n                                                ml: \"$2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:217\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"white\",\n                                                        fontSize: 14,\n                                                        fontWeight: \"bold\",\n                                                        children: \"KTA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 178,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:220\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"#8B8F9A\",\n                                                        fontSize: 12,\n                                                        fontWeight: 500,\n                                                        mt: 6,\n                                                        children: \"US$0.54\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 181,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                \"data-at\": \"homePage.tsx:224\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Text\",\n                                                color: \"#C7545E\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                ml: \"$4\",\n                                                children: \"15.51%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                        \"data-at\": \"homePage.tsx:228\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"XStack\",\n                                        alignItems: \"center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                \"data-at\": \"homePage.tsx:229\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Text\",\n                                                color: \"white\",\n                                                fontSize: 20,\n                                                fontWeight: \"bold\",\n                                                children: \"681\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                \"data-at\": \"homePage.tsx:232\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Text\",\n                                                color: \"white\",\n                                                fontSize: 16,\n                                                fontWeight: \"bold\",\n                                                ml: \"$1\",\n                                                children: \"兑换\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                \"data-at\": \"homePage.tsx:235\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"View\",\n                                                bg: \"#282B32\",\n                                                width: 137,\n                                                height: 34,\n                                                borderRadius: 20,\n                                                ml: \"$5\",\n                                                onPress: ()=>{\n                                                    router.push(\"/wallet/convert\");\n                                                },\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    \"data-at\": \"homePage.tsx:236\",\n                                                    \"data-in\": \"HomePage\",\n                                                    \"data-is\": \"Text\",\n                                                    color: \"white\",\n                                                    text: \"center\",\n                                                    lineHeight: 34,\n                                                    children: \"兑换\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 196,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                        \"data-at\": \"homePage.tsx:241\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"XStack\",\n                                        mt: 20,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                \"data-at\": \"homePage.tsx:242\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"View\",\n                                                width: 124,\n                                                height: 4,\n                                                bg: \"#2FAB77\",\n                                                borderRadius: 20\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                \"data-at\": \"homePage.tsx:243\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"View\",\n                                                width: 100,\n                                                height: 4,\n                                                bg: \"#C7545E\",\n                                                borderRadius: 20\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                        \"data-at\": \"homePage.tsx:245\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"XStack\",\n                                        mt: 20,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                                \"data-at\": \"homePage.tsx:246\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"XStack\",\n                                                flex: 1,\n                                                alignItems: \"center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                        \"data-at\": \"homePage.tsx:247\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"View\",\n                                                        width: 6,\n                                                        height: 6,\n                                                        bg: \"#2FAB77\",\n                                                        borderRadius: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 210,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:248\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 12,\n                                                        color: \"#8B8F9A\",\n                                                        ml: \"$2\",\n                                                        children: \"已购买 70%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 211,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 209,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                                \"data-at\": \"homePage.tsx:252\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"XStack\",\n                                                flex: 1,\n                                                alignItems: \"center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                        \"data-at\": \"homePage.tsx:253\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"View\",\n                                                        width: 6,\n                                                        height: 6,\n                                                        bg: \"#C7545E\",\n                                                        borderRadius: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 216,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:254\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 12,\n                                                        color: \"#8B8F9A\",\n                                                        ml: \"$2\",\n                                                        children: [\n                                                            \"已售出 30%\",\n                                                            \" \"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 217,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                \"data-at\": \"homePage.tsx:260\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"YStack\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                        \"data-at\": \"homePage.tsx:261\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"XStack\",\n                                        mb: \"$4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                                \"data-at\": \"homePage.tsx:262-265\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Image\",\n                                                source: {\n                                                    uri: \"\"\n                                                },\n                                                style: {\n                                                    width: 28,\n                                                    height: 28,\n                                                    borderRadius: \"50%\",\n                                                    background: \"#2B2B2B\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                \"data-at\": \"homePage.tsx:266\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"View\",\n                                                ml: \"$2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:267\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"white\",\n                                                        fontSize: 14,\n                                                        fontWeight: \"bold\",\n                                                        children: \"KTA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 234,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:270\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"#8B8F9A\",\n                                                        fontSize: 12,\n                                                        fontWeight: 500,\n                                                        mt: 6,\n                                                        children: \"US$0.54\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 237,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 233,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                \"data-at\": \"homePage.tsx:274\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Text\",\n                                                color: \"#C7545E\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                ml: \"$4\",\n                                                children: \"15.51%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                        \"data-at\": \"homePage.tsx:278\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"XStack\",\n                                        alignItems: \"center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                \"data-at\": \"homePage.tsx:279\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Text\",\n                                                color: \"white\",\n                                                fontSize: 20,\n                                                fontWeight: \"bold\",\n                                                children: \"681\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                \"data-at\": \"homePage.tsx:282\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Text\",\n                                                color: \"white\",\n                                                fontSize: 16,\n                                                fontWeight: \"bold\",\n                                                ml: \"$1\",\n                                                children: \"兑换\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                \"data-at\": \"homePage.tsx:285\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"View\",\n                                                bg: \"#282B32\",\n                                                width: 137,\n                                                height: 34,\n                                                borderRadius: 20,\n                                                ml: \"$5\",\n                                                onPress: ()=>{\n                                                    router.push(\"/wallet/convert\");\n                                                },\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    \"data-at\": \"homePage.tsx:286\",\n                                                    \"data-in\": \"HomePage\",\n                                                    \"data-is\": \"Text\",\n                                                    color: \"white\",\n                                                    text: \"center\",\n                                                    lineHeight: 34,\n                                                    children: \"兑换\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                        \"data-at\": \"homePage.tsx:291\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"XStack\",\n                                        mt: 20,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                \"data-at\": \"homePage.tsx:292\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"View\",\n                                                width: 124,\n                                                height: 4,\n                                                bg: \"#2FAB77\",\n                                                borderRadius: 20\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                \"data-at\": \"homePage.tsx:293\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"View\",\n                                                width: 100,\n                                                height: 4,\n                                                bg: \"#C7545E\",\n                                                borderRadius: 20\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 262,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                        \"data-at\": \"homePage.tsx:295\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"XStack\",\n                                        mt: 20,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                                \"data-at\": \"homePage.tsx:296\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"XStack\",\n                                                flex: 1,\n                                                alignItems: \"center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                        \"data-at\": \"homePage.tsx:297\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"View\",\n                                                        width: 6,\n                                                        height: 6,\n                                                        bg: \"#2FAB77\",\n                                                        borderRadius: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 266,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:298\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 12,\n                                                        color: \"#8B8F9A\",\n                                                        ml: \"$2\",\n                                                        children: \"已购买 70%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 267,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 265,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                                \"data-at\": \"homePage.tsx:302\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"XStack\",\n                                                flex: 1,\n                                                alignItems: \"center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                        \"data-at\": \"homePage.tsx:303\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"View\",\n                                                        width: 6,\n                                                        height: 6,\n                                                        bg: \"#C7545E\",\n                                                        borderRadius: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 272,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:304\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 12,\n                                                        color: \"#8B8F9A\",\n                                                        ml: \"$2\",\n                                                        children: [\n                                                            \"已售出 30%\",\n                                                            \" \"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 273,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 271,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:313\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                    \"data-at\": \"homePage.tsx:314\",\n                    \"data-in\": \"HomePage\",\n                    \"data-is\": \"View\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                            \"data-at\": \"homePage.tsx:315\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_mint1_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                            style: {\n                                width: \"100%\",\n                                height: 228,\n                                borderRadius: 12\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:316\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"white\",\n                            fontSize: 12,\n                            fontWeight: \"bold\",\n                            mt: 6,\n                            children: \"Sponsored\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:319\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            fontSize: 16,\n                            color: \"white\",\n                            fontWeight: \"bold\",\n                            mt: 6,\n                            children: \"In-App Bridging is Here\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:322\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"#8B8F9A\",\n                            fontSize: 12,\n                            fontWeight: 500,\n                            mt: 6,\n                            children: \"For when you really, really want that one token. Onthat other chain.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                            \"data-at\": \"homePage.tsx:325\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"XStack\",\n                            mt: \"$4\",\n                            alignItems: \"center\",\n                            gap: \"$5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                    \"data-at\": \"homePage.tsx:326\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"View\",\n                                    bg: \"#282B32\",\n                                    width: 137,\n                                    height: 34,\n                                    borderRadius: 20,\n                                    alignItems: \"center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:327\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"white\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        lineHeight: 34,\n                                        children: \"Learn More\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                    \"data-at\": \"homePage.tsx:331\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"Text\",\n                                    color: \"white\",\n                                    fontSize: 12,\n                                    fontWeight: \"bold\",\n                                    children: \"Dismiss\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 310,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:338\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                    \"data-at\": \"homePage.tsx:339\",\n                    \"data-in\": \"HomePage\",\n                    \"data-is\": \"View\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:340\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"white\",\n                            fontSize: 16,\n                            fontWeight: \"bold\",\n                            mb: 10,\n                            children: \"Trending onchain\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                            \"data-at\": \"homePage.tsx:343\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_mint2_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                            style: {\n                                width: \"100%\",\n                                height: 228,\n                                borderRadius: 12\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:344\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            fontSize: 16,\n                            color: \"white\",\n                            fontWeight: \"bold\",\n                            mt: 6,\n                            children: \"Drifters\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:347\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"#8B8F9A\",\n                            fontSize: 12,\n                            fontWeight: 500,\n                            mt: 6,\n                            children: \"Drifters are handcrafted, fully customiz..\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:353\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                    \"data-at\": \"homePage.tsx:354\",\n                    \"data-in\": \"HomePage\",\n                    \"data-is\": \"Text\",\n                    color: \"white\",\n                    fontSize: 16,\n                    children: \"NFT mints for you\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 331,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 330,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n        lineNumber: 106,\n        columnNumber: 10\n    }, this);\n}\n_s(HomePage, \"W+W+6QFeS0A5s7b4o2uYY16EQGU=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_8__.useTranslation\n    ];\n});\n_c6 = HomePage;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"Underline\");\n$RefreshReg$(_c1, \"ActiveBlock\");\n$RefreshReg$(_c2, \"Block\");\n$RefreshReg$(_c3, \"SwiperContainer\");\n$RefreshReg$(_c4, \"SwiperWrapper\");\n$RefreshReg$(_c5, \"SwiperSlide\");\n$RefreshReg$(_c6, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/home/<USER>"));

/***/ })

});