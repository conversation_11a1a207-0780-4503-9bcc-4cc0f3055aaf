"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "packages_app_i18n_locales_zh_ts";
exports.ids = ["packages_app_i18n_locales_zh_ts"];
exports.modules = {

/***/ "../../packages/app/i18n/locales/zh.ts":
/*!*********************************************!*\
  !*** ../../packages/app/i18n/locales/zh.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst zh = {\n    common: {\n        confirm: \"确认\",\n        cancel: \"取消\",\n        save: \"保存\",\n        delete: \"删除\",\n        edit: \"编辑\",\n        copy: \"复制\",\n        share: \"分享\",\n        loading: \"加载中...\",\n        error: \"错误\",\n        success: \"成功\",\n        back: \"返回\",\n        next: \"下一步\",\n        done: \"完成\",\n        close: \"关闭\"\n    },\n    wallet: {\n        balance: \"余额\",\n        send: \"发送\",\n        receive: \"接收\",\n        transaction: \"交易\",\n        address: \"地址\",\n        amount: \"金额\",\n        gasPrice: \"Gas价格\",\n        gasLimit: \"Gas限制\",\n        networkFee: \"网络费用\",\n        totalBalance: \"总余额\",\n        availableBalance: \"可用余额\",\n        sendTransaction: \"发送交易\",\n        receiveTokens: \"接收代币\",\n        transactionHistory: \"交易历史\",\n        copyAddress: \"复制地址\",\n        shareAddress: \"分享\",\n        insufficientBalance: \"余额不足\",\n        transactionFailed: \"交易失败\",\n        transactionSuccess: \"交易成功\",\n        pending: \"待确认\",\n        confirmed: \"已确认\",\n        failed: \"失败\",\n        yourAddress: \"您的地址\",\n        enterRecipientAddress: \"输入接收地址\",\n        max: \"最大\",\n        sending: \"发送中...\",\n        usingWallet: \"使用的钱包\",\n        network: \"网络\"\n    },\n    navigation: {\n        home: \"首页\",\n        wallet: \"钱包\",\n        settings: \"设置\",\n        security: \"安全\",\n        about: \"关于\",\n        language: \"语言\",\n        network: \"网络\",\n        backup: \"备份\",\n        import: \"导入\",\n        export: \"导出\",\n        display: \"显示\"\n    },\n    form: {\n        enterAmount: \"输入金额\",\n        enterAddress: \"输入地址\",\n        selectNetwork: \"选择网络\",\n        selectLanguage: \"选择语言\",\n        password: \"密码\",\n        confirmPassword: \"确认密码\",\n        mnemonic: \"助记词\",\n        privateKey: \"私钥\",\n        walletName: \"钱包名称\",\n        required: \"请填写完整信息\",\n        invalid: \"无效\",\n        tooShort: \"太短\",\n        tooLong: \"太长\",\n        passwordMismatch: \"密码不匹配\"\n    },\n    errors: {\n        networkError: \"网络错误\",\n        invalidAddress: \"无效地址\",\n        invalidAmount: \"无效金额\",\n        insufficientFunds: \"资金不足\",\n        transactionFailed: \"交易失败\",\n        walletNotFound: \"钱包未找到\",\n        invalidMnemonic: \"无效助记词\",\n        invalidPrivateKey: \"无效私钥\",\n        passwordRequired: \"需要密码\",\n        confirmationFailed: \"确认失败\",\n        copyFailed: \"复制失败\"\n    },\n    success: {\n        transactionSent: \"交易已发送\",\n        addressCopied: \"地址已复制到剪贴板\",\n        walletCreated: \"钱包已创建\",\n        walletImported: \"钱包已导入\",\n        settingsSaved: \"设置已保存\",\n        backupCompleted: \"备份已完成\",\n        copySuccess: \"复制成功\",\n        balanceRefreshed: \"余额已刷新\"\n    },\n    error: {\n        refreshFailed: \"刷新失败，请稍后重试\"\n    },\n    // 首页相关\n    home: {\n        rewards: \"奖励\",\n        earnRewards: \"获得 {rate}% 的奖励\",\n        addToWallet: \"将 {network} 上的 {token} 添加到您的钱包\",\n        yearlyEarnings: \"每年可赚取 {rate}% 的奖励\",\n        watchlist: \"关注列表\",\n        addressLabel: \"地址{number}\",\n        copyFailed: \"复制失败，请手动复制\",\n        createWatchlist: '创建\"我的关注\"',\n        getPriceAlerts: \"获取价格提醒并了解最新信息\",\n        swapCount: \"兑换\",\n        swapAction: \"兑换\",\n        boughtPercent: \"已购买 {percent}%\",\n        soldPercent: \"已售出 {percent}%\"\n    },\n    // 钱包管理\n    walletManagement: {\n        addAndManage: \"添加和管理钱包\",\n        backupWallet: \"备份您的钱包\",\n        securityWarning: \"绝对不要分享这些词。任何得知它们的人都可以窃取您所有的加密货币。Coinbase 绝不会要求您提供这些信息。\",\n        recoveryPhrase: \"以下 12 个单词是您钱包的恢复短语。该短语可让您在丢失设备时恢复钱包。将其备份到iCloud(推荐)或记下来。或同时采用这两种方式。\",\n        copyToClipboard: \"复制到剪贴板\",\n        neverShare: \"绝不分享\",\n        writeDown: \"记下来\",\n        backupToiCloud: \"备份到iCloud\",\n        importMnemonic: \"导入助记词\",\n        importDescription: \"输入助记词来添加或恢复你的钱包。导入的助记词将被加密并安全存储在你的设备上。为了你的资产安全，不会存储你的助记词。\",\n        enterMnemonic: \"输入助记词\",\n        mnemonicPlaceholder: \"输入助记词单词，并使用空格分隔\",\n        yourAddress: \"您的\",\n        address: \"地址\",\n        shareAddress: \"分享地址\",\n        editLabel: \"编辑标签\",\n        addressLabel: \"地址标签\",\n        labelDescription: \"为您的地址提供标签，以便轻松识别。标签存储在本地，仅限您可以看到。\",\n        enterWalletName: \"输入钱包名称\",\n        saving: \"保存中...\",\n        save: \"保存\"\n    },\n    // 交易相关\n    trading: {\n        all: \"全部\",\n        exchange: \"交换\",\n        earn: \"赚取\",\n        socialMedia: \"社交媒体\",\n        manage: \"管理\",\n        listen: \"监听\",\n        tradeAssets: \"交易资产\",\n        buy: \"买入\"\n    },\n    // 时间相关\n    time: {\n        today: \"今天\",\n        yesterday: \"昨天\",\n        filter: \"筛选\"\n    },\n    settings: {\n        languageNote: \"语言设置将在下次启动应用时生效。某些功能可能需要重新加载。\",\n        languageSubtitle: \"选择应用语言\",\n        securitySubtitle: \"密码和安全设置\",\n        notifications: \"通知\",\n        notificationsSubtitle: \"管理通知设置\",\n        aboutSubtitle: \"版本信息和帮助\",\n        version: \"版本\",\n        copyright: \"\\xa9 2024 Coinbase Wallet\"\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (zh);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/i18n/locales/zh.ts\n");

/***/ })

};
;