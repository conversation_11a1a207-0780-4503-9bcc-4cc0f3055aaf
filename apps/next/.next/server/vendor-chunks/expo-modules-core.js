"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/expo-modules-core";
exports.ids = ["vendor-chunks/expo-modules-core"];
exports.modules = {

/***/ "../../node_modules/expo-modules-core/src/EventEmitter.ts":
/*!****************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/EventEmitter.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ensureNativeModulesAreInstalled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ensureNativeModulesAreInstalled */ \"../../node_modules/expo-modules-core/src/ensureNativeModulesAreInstalled.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n(0,_ensureNativeModulesAreInstalled__WEBPACK_IMPORTED_MODULE_0__.ensureNativeModulesAreInstalled)();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (globalThis.expo.EventEmitter);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9FdmVudEVtaXR0ZXIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7NkRBRW9GO0FBR3BGQSxpR0FBK0JBO0FBYS9CLGlFQUFlQyxXQUFXQyxJQUFJLENBQUNDLFlBQVksRUFBd0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9ub2RlX21vZHVsZXMvZXhwby1tb2R1bGVzLWNvcmUvc3JjL0V2ZW50RW1pdHRlci50cz9lN2VkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgZW5zdXJlTmF0aXZlTW9kdWxlc0FyZUluc3RhbGxlZCB9IGZyb20gJy4vZW5zdXJlTmF0aXZlTW9kdWxlc0FyZUluc3RhbGxlZCc7XG5pbXBvcnQgdHlwZSB7IEV2ZW50RW1pdHRlciB9IGZyb20gJy4vdHMtZGVjbGFyYXRpb25zL0V2ZW50RW1pdHRlcic7XG5cbmVuc3VyZU5hdGl2ZU1vZHVsZXNBcmVJbnN0YWxsZWQoKTtcblxuLyoqXG4gKiBBIHN1YnNjcmlwdGlvbiBvYmplY3QgdGhhdCBhbGxvd3MgdG8gY29udmVuaWVudGx5IHJlbW92ZSBhbiBldmVudCBsaXN0ZW5lciBmcm9tIHRoZSBlbWl0dGVyLlxuICovXG5leHBvcnQgaW50ZXJmYWNlIEV2ZW50U3Vic2NyaXB0aW9uIHtcbiAgLyoqXG4gICAqIFJlbW92ZXMgYW4gZXZlbnQgbGlzdGVuZXIgZm9yIHdoaWNoIHRoZSBzdWJzY3JpcHRpb24gaGFzIGJlZW4gY3JlYXRlZC5cbiAgICogQWZ0ZXIgY2FsbGluZyB0aGlzIGZ1bmN0aW9uLCB0aGUgbGlzdGVuZXIgd2lsbCBubyBsb25nZXIgcmVjZWl2ZSBhbnkgZXZlbnRzIGZyb20gdGhlIGVtaXR0ZXIuXG4gICAqL1xuICByZW1vdmUoKTogdm9pZDtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZ2xvYmFsVGhpcy5leHBvLkV2ZW50RW1pdHRlciBhcyB0eXBlb2YgRXZlbnRFbWl0dGVyO1xuIl0sIm5hbWVzIjpbImVuc3VyZU5hdGl2ZU1vZHVsZXNBcmVJbnN0YWxsZWQiLCJnbG9iYWxUaGlzIiwiZXhwbyIsIkV2ZW50RW1pdHRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/EventEmitter.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/LegacyEventEmitter.ts":
/*!**********************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/LegacyEventEmitter.ts ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LegacyEventEmitter: () => (/* binding */ LegacyEventEmitter)\n/* harmony export */ });\n/* harmony import */ var invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! invariant */ \"invariant\");\n/* harmony import */ var invariant__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(invariant__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n\n\nconst nativeEmitterSubscriptionKey = \"@@nativeEmitterSubscription@@\";\n/**\n * @deprecated Deprecated in favor of `EventEmitter`.\n */ class LegacyEventEmitter {\n    constructor(nativeModule){\n        this._listenerCount = 0;\n        // If the native module is a new module, just return it back as it's already an event emitter.\n        // This is for backwards compatibility until we stop using this legacy class in other packages.\n        if (nativeModule.__expo_module_name__) {\n            // @ts-expect-error\n            return nativeModule;\n        }\n        this._nativeModule = nativeModule;\n        this._eventEmitter = new react_native__WEBPACK_IMPORTED_MODULE_1__.NativeEventEmitter(nativeModule);\n    }\n    addListener(eventName, listener) {\n        if (!this._listenerCount && react_native__WEBPACK_IMPORTED_MODULE_1__.Platform.OS !== \"ios\" && this._nativeModule.startObserving) {\n            this._nativeModule.startObserving();\n        }\n        this._listenerCount++;\n        const nativeEmitterSubscription = this._eventEmitter.addListener(eventName, listener);\n        const subscription = {\n            [nativeEmitterSubscriptionKey]: nativeEmitterSubscription,\n            remove: ()=>{\n                this.removeSubscription(subscription);\n            }\n        };\n        return subscription;\n    }\n    removeAllListeners(eventName) {\n        // @ts-ignore: the EventEmitter interface has been changed in react-native@0.64.0\n        const removedListenerCount = this._eventEmitter.listenerCount ? this._eventEmitter.listenerCount(eventName) : this._eventEmitter.listeners(eventName).length;\n        this._eventEmitter.removeAllListeners(eventName);\n        this._listenerCount -= removedListenerCount;\n        invariant__WEBPACK_IMPORTED_MODULE_0___default()(this._listenerCount >= 0, `EventEmitter must have a non-negative number of listeners`);\n        if (!this._listenerCount && react_native__WEBPACK_IMPORTED_MODULE_1__.Platform.OS !== \"ios\" && this._nativeModule.stopObserving) {\n            this._nativeModule.stopObserving();\n        }\n    }\n    removeSubscription(subscription) {\n        const state = subscription;\n        const nativeEmitterSubscription = state[nativeEmitterSubscriptionKey];\n        if (!nativeEmitterSubscription) {\n            return;\n        }\n        if (\"remove\" in nativeEmitterSubscription) {\n            nativeEmitterSubscription.remove?.();\n        }\n        this._listenerCount--;\n        // Ensure that the emitter's internal state remains correct even if `removeSubscription` is\n        // called again with the same subscription\n        delete state[nativeEmitterSubscriptionKey];\n        // Release closed-over references to the emitter\n        subscription.remove = ()=>{};\n        if (!this._listenerCount && react_native__WEBPACK_IMPORTED_MODULE_1__.Platform.OS !== \"ios\" && this._nativeModule.stopObserving) {\n            this._nativeModule.stopObserving();\n        }\n    }\n    emit(eventName, ...params) {\n        this._eventEmitter.emit(eventName, ...params);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/LegacyEventEmitter.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/NativeModule.ts":
/*!****************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/NativeModule.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ensureNativeModulesAreInstalled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ensureNativeModulesAreInstalled */ \"../../node_modules/expo-modules-core/src/ensureNativeModulesAreInstalled.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n(0,_ensureNativeModulesAreInstalled__WEBPACK_IMPORTED_MODULE_0__.ensureNativeModulesAreInstalled)();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (globalThis.expo.NativeModule);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9OYXRpdmVNb2R1bGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7NkRBRW9GO0FBR3BGQSxpR0FBK0JBO0FBRS9CLGlFQUFlQyxXQUFXQyxJQUFJLENBQUNDLFlBQVksRUFBd0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9ub2RlX21vZHVsZXMvZXhwby1tb2R1bGVzLWNvcmUvc3JjL05hdGl2ZU1vZHVsZS50cz8yM2RhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgZW5zdXJlTmF0aXZlTW9kdWxlc0FyZUluc3RhbGxlZCB9IGZyb20gJy4vZW5zdXJlTmF0aXZlTW9kdWxlc0FyZUluc3RhbGxlZCc7XG5pbXBvcnQgdHlwZSB7IE5hdGl2ZU1vZHVsZSB9IGZyb20gJy4vdHMtZGVjbGFyYXRpb25zL05hdGl2ZU1vZHVsZSc7XG5cbmVuc3VyZU5hdGl2ZU1vZHVsZXNBcmVJbnN0YWxsZWQoKTtcblxuZXhwb3J0IGRlZmF1bHQgZ2xvYmFsVGhpcy5leHBvLk5hdGl2ZU1vZHVsZSBhcyB0eXBlb2YgTmF0aXZlTW9kdWxlO1xuIl0sIm5hbWVzIjpbImVuc3VyZU5hdGl2ZU1vZHVsZXNBcmVJbnN0YWxsZWQiLCJnbG9iYWxUaGlzIiwiZXhwbyIsIk5hdGl2ZU1vZHVsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/NativeModule.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/NativeModulesProxy.ts":
/*!**********************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/NativeModulesProxy.ts ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// We default to an empty object shim wherever we don't have an environment-specific implementation\n/**\n * @deprecated `NativeModulesProxy` is deprecated and might be removed in the future releases.\n * Use `requireNativeModule` or `requireOptionalNativeModule` instead.\n */ /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9OYXRpdmVNb2R1bGVzUHJveHkudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUVBLG1HQUFtRztBQUVuRzs7O0NBR0MsR0FDRCxpRUFBZSxDQUFDLENBQUMsRUFBc0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9ub2RlX21vZHVsZXMvZXhwby1tb2R1bGVzLWNvcmUvc3JjL05hdGl2ZU1vZHVsZXNQcm94eS50cz85MjY2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgUHJveHlOYXRpdmVNb2R1bGUgfSBmcm9tICcuL05hdGl2ZU1vZHVsZXNQcm94eS50eXBlcyc7XG5cbi8vIFdlIGRlZmF1bHQgdG8gYW4gZW1wdHkgb2JqZWN0IHNoaW0gd2hlcmV2ZXIgd2UgZG9uJ3QgaGF2ZSBhbiBlbnZpcm9ubWVudC1zcGVjaWZpYyBpbXBsZW1lbnRhdGlvblxuXG4vKipcbiAqIEBkZXByZWNhdGVkIGBOYXRpdmVNb2R1bGVzUHJveHlgIGlzIGRlcHJlY2F0ZWQgYW5kIG1pZ2h0IGJlIHJlbW92ZWQgaW4gdGhlIGZ1dHVyZSByZWxlYXNlcy5cbiAqIFVzZSBgcmVxdWlyZU5hdGl2ZU1vZHVsZWAgb3IgYHJlcXVpcmVPcHRpb25hbE5hdGl2ZU1vZHVsZWAgaW5zdGVhZC5cbiAqL1xuZXhwb3J0IGRlZmF1bHQge30gYXMgUmVjb3JkPHN0cmluZywgUHJveHlOYXRpdmVNb2R1bGU+O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/NativeModulesProxy.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/NativeViewManagerAdapter.tsx":
/*!*****************************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/NativeViewManagerAdapter.tsx ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   requireNativeViewManager: () => (/* binding */ requireNativeViewManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _errors_UnavailabilityError__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./errors/UnavailabilityError */ \"../../node_modules/expo-modules-core/src/errors/UnavailabilityError.ts\");\n\n\n/**\n * A drop-in replacement for `requireNativeComponent`.\n */ function requireNativeViewManager(moduleName, viewName) {\n    throw new _errors_UnavailabilityError__WEBPACK_IMPORTED_MODULE_1__.UnavailabilityError(\"expo-modules-core\", \"requireNativeViewManager\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9OYXRpdmVWaWV3TWFuYWdlckFkYXB0ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEI7QUFFeUM7QUFFbkU7O0NBRUMsR0FDTSxTQUFTRSx5QkFDZEMsVUFBa0IsRUFDbEJDLFFBQWlCO0lBRWpCLE1BQU0sSUFBSUgsNEVBQW1CQSxDQUFDLHFCQUFxQjtBQUNyRCIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL25vZGVfbW9kdWxlcy9leHBvLW1vZHVsZXMtY29yZS9zcmMvTmF0aXZlVmlld01hbmFnZXJBZGFwdGVyLnRzeD9jODFmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmltcG9ydCB7IFVuYXZhaWxhYmlsaXR5RXJyb3IgfSBmcm9tICcuL2Vycm9ycy9VbmF2YWlsYWJpbGl0eUVycm9yJztcblxuLyoqXG4gKiBBIGRyb3AtaW4gcmVwbGFjZW1lbnQgZm9yIGByZXF1aXJlTmF0aXZlQ29tcG9uZW50YC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJlcXVpcmVOYXRpdmVWaWV3TWFuYWdlcjxQID0gYW55PihcbiAgbW9kdWxlTmFtZTogc3RyaW5nLFxuICB2aWV3TmFtZT86IHN0cmluZ1xuKTogUmVhY3QuQ29tcG9uZW50VHlwZTxQPiB7XG4gIHRocm93IG5ldyBVbmF2YWlsYWJpbGl0eUVycm9yKCdleHBvLW1vZHVsZXMtY29yZScsICdyZXF1aXJlTmF0aXZlVmlld01hbmFnZXInKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlVuYXZhaWxhYmlsaXR5RXJyb3IiLCJyZXF1aXJlTmF0aXZlVmlld01hbmFnZXIiLCJtb2R1bGVOYW1lIiwidmlld05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/NativeViewManagerAdapter.tsx\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/PermissionsHook.ts":
/*!*******************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/PermissionsHook.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPermissionHook: () => (/* binding */ createPermissionHook)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n// Copyright © 2024 650 Industries.\n/* __next_internal_client_entry_do_not_use__ createPermissionHook auto */ \n/**\n * Get or request permission for protected functionality within the app.\n * It uses separate permission requesters to interact with a single permission.\n * By default, the hook will only retrieve the permission status.\n */ function usePermission(methods, options) {\n    const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const { get = true, request = false, ...permissionOptions } = options || {};\n    const getPermission = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        const response = await methods.getMethod(Object.keys(permissionOptions).length > 0 ? permissionOptions : undefined);\n        if (isMounted.current) setStatus(response);\n        return response;\n    }, [\n        methods.getMethod\n    ]);\n    const requestPermission = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        const response = await methods.requestMethod(Object.keys(permissionOptions).length > 0 ? permissionOptions : undefined);\n        if (isMounted.current) setStatus(response);\n        return response;\n    }, [\n        methods.requestMethod\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function runMethods() {\n        if (request) requestPermission();\n        if (!request && get) getPermission();\n    }, [\n        get,\n        request,\n        requestPermission,\n        getPermission\n    ]);\n    // Workaround for unmounting components receiving state updates\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function didMount() {\n        isMounted.current = true;\n        return ()=>{\n            isMounted.current = false;\n        };\n    }, []);\n    return [\n        status,\n        requestPermission,\n        getPermission\n    ];\n}\n/**\n * Create a new permission hook with the permission methods built-in.\n * This can be used to quickly create specific permission hooks in every module.\n */ function createPermissionHook(methods) {\n    return (options)=>usePermission(methods, options);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/PermissionsHook.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/PermissionsInterface.ts":
/*!************************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/PermissionsInterface.ts ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PermissionStatus: () => (/* binding */ PermissionStatus)\n/* harmony export */ });\nvar PermissionStatus;\n(function(PermissionStatus) {\n    /**\n   * User has granted the permission.\n   */ PermissionStatus[\"GRANTED\"] = \"granted\";\n    /**\n   * User hasn't granted or denied the permission yet.\n   */ PermissionStatus[\"UNDETERMINED\"] = \"undetermined\";\n    /**\n   * User has denied the permission.\n   */ PermissionStatus[\"DENIED\"] = \"denied\";\n})(PermissionStatus || (PermissionStatus = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9QZXJtaXNzaW9uc0ludGVyZmFjZS50cyIsIm1hcHBpbmdzIjoiOzs7OztVQUFZQTtJQUNWOztHQUVDO0lBRUQ7O0dBRUM7SUFFRDs7R0FFQztHQVhTQSxxQkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9ub2RlX21vZHVsZXMvZXhwby1tb2R1bGVzLWNvcmUvc3JjL1Blcm1pc3Npb25zSW50ZXJmYWNlLnRzP2RmMmEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGVudW0gUGVybWlzc2lvblN0YXR1cyB7XG4gIC8qKlxuICAgKiBVc2VyIGhhcyBncmFudGVkIHRoZSBwZXJtaXNzaW9uLlxuICAgKi9cbiAgR1JBTlRFRCA9ICdncmFudGVkJyxcbiAgLyoqXG4gICAqIFVzZXIgaGFzbid0IGdyYW50ZWQgb3IgZGVuaWVkIHRoZSBwZXJtaXNzaW9uIHlldC5cbiAgICovXG4gIFVOREVURVJNSU5FRCA9ICd1bmRldGVybWluZWQnLFxuICAvKipcbiAgICogVXNlciBoYXMgZGVuaWVkIHRoZSBwZXJtaXNzaW9uLlxuICAgKi9cbiAgREVOSUVEID0gJ2RlbmllZCcsXG59XG5cbi8qKlxuICogUGVybWlzc2lvbiBleHBpcmF0aW9uIHRpbWUuIEN1cnJlbnRseSwgYWxsIHBlcm1pc3Npb25zIGFyZSBncmFudGVkIHBlcm1hbmVudGx5LlxuICovXG5leHBvcnQgdHlwZSBQZXJtaXNzaW9uRXhwaXJhdGlvbiA9ICduZXZlcicgfCBudW1iZXI7XG5cbi8qKlxuICogQW4gb2JqZWN0IG9idGFpbmVkIGJ5IHBlcm1pc3Npb25zIGdldCBhbmQgcmVxdWVzdCBmdW5jdGlvbnMuXG4gKi9cbmV4cG9ydCB0eXBlIFBlcm1pc3Npb25SZXNwb25zZSA9IHtcbiAgLyoqXG4gICAqIERldGVybWluZXMgdGhlIHN0YXR1cyBvZiB0aGUgcGVybWlzc2lvbi5cbiAgICovXG4gIHN0YXR1czogUGVybWlzc2lvblN0YXR1cztcbiAgLyoqXG4gICAqIERldGVybWluZXMgdGltZSB3aGVuIHRoZSBwZXJtaXNzaW9uIGV4cGlyZXMuXG4gICAqL1xuICBleHBpcmVzOiBQZXJtaXNzaW9uRXhwaXJhdGlvbjtcbiAgLyoqXG4gICAqIEEgY29udmVuaWVuY2UgYm9vbGVhbiB0aGF0IGluZGljYXRlcyBpZiB0aGUgcGVybWlzc2lvbiBpcyBncmFudGVkLlxuICAgKi9cbiAgZ3JhbnRlZDogYm9vbGVhbjtcbiAgLyoqXG4gICAqIEluZGljYXRlcyBpZiB1c2VyIGNhbiBiZSBhc2tlZCBhZ2FpbiBmb3Igc3BlY2lmaWMgcGVybWlzc2lvbi5cbiAgICogSWYgbm90LCBvbmUgc2hvdWxkIGJlIGRpcmVjdGVkIHRvIHRoZSBTZXR0aW5ncyBhcHBcbiAgICogaW4gb3JkZXIgdG8gZW5hYmxlL2Rpc2FibGUgdGhlIHBlcm1pc3Npb24uXG4gICAqL1xuICBjYW5Bc2tBZ2FpbjogYm9vbGVhbjtcbn07XG4iXSwibmFtZXMiOlsiUGVybWlzc2lvblN0YXR1cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/PermissionsInterface.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/Platform.ts":
/*!************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/Platform.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var _environment_browser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./environment/browser */ \"../../node_modules/expo-modules-core/src/environment/browser.web.ts\");\n\n\nif ( true && typeof process.env.EXPO_OS === \"undefined\") {\n    console.warn(`The global process.env.EXPO_OS is not defined. This should be inlined by babel-preset-expo during transformation.`);\n}\nconst nativeSelect =  false ? 0 : // Opt to use the env var when possible, and fallback to the React Native Platform module when it's not (arbitrary bundlers and transformers).\nfunction select(specifics) {\n    if (!process.env.EXPO_OS) return undefined;\n    if (specifics.hasOwnProperty(process.env.EXPO_OS)) {\n        return specifics[process.env.EXPO_OS];\n    } else if (process.env.EXPO_OS !== \"web\" && specifics.hasOwnProperty(\"native\")) {\n        return specifics.native;\n    } else if (specifics.hasOwnProperty(\"default\")) {\n        return specifics.default;\n    }\n    // do nothing...\n    return undefined;\n};\nconst Platform = {\n    /**\n   * Denotes the currently running platform.\n   * Can be one of ios, android, web.\n   */ OS: process.env.EXPO_OS || react_native__WEBPACK_IMPORTED_MODULE_0__.Platform.OS,\n    /**\n   * Returns the value with the matching platform.\n   * Object keys can be any of ios, android, native, web, default.\n   *\n   * @ios ios, native, default\n   * @android android, native, default\n   * @web web, default\n   */ select: nativeSelect,\n    /**\n   * Denotes if the DOM API is available in the current environment.\n   * The DOM is not available in native React runtimes and Node.js.\n   */ isDOMAvailable: _environment_browser__WEBPACK_IMPORTED_MODULE_1__.isDOMAvailable,\n    /**\n   * Denotes if the current environment can attach event listeners\n   * to the window. This will return false in native React\n   * runtimes and Node.js.\n   */ canUseEventListeners: _environment_browser__WEBPACK_IMPORTED_MODULE_1__.canUseEventListeners,\n    /**\n   * Denotes if the current environment can inspect properties of the\n   * screen on which the current window is being rendered. This will\n   * return false in native React runtimes and Node.js.\n   */ canUseViewport: _environment_browser__WEBPACK_IMPORTED_MODULE_1__.canUseViewport,\n    /**\n   * If the JavaScript is being executed in a remote JavaScript environment.\n   * When `true`, synchronous native invocations cannot be executed.\n   */ isAsyncDebugging: _environment_browser__WEBPACK_IMPORTED_MODULE_1__.isAsyncDebugging\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Platform);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9QbGF0Zm9ybS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0U7QUFPaEQ7QUFNL0IsSUFBSU0sS0FBT0EsSUFBSSxPQUFPQyxRQUFRQyxHQUFHLENBQUNDLE9BQU8sS0FBSyxhQUFhO0lBQ3pEQyxRQUFRQyxJQUFJLENBQ1YsQ0FBQyxpSEFBaUgsQ0FBQztBQUV2SDtBQUVBLE1BQU1DLGVBQ0osTUFBa0IsR0FDZFgsQ0FBMEIsR0FFMUIsOElBQThJO0FBQzlJLFNBQVNZLE9BQVVDLFNBQXFEO0lBQ3RFLElBQUksQ0FBQ1AsUUFBUUMsR0FBRyxDQUFDQyxPQUFPLEVBQUUsT0FBT007SUFDakMsSUFBSUQsVUFBVUUsY0FBYyxDQUFDVCxRQUFRQyxHQUFHLENBQUNDLE9BQU8sR0FBRztRQUNqRCxPQUFPSyxTQUFTLENBQUNQLFFBQVFDLEdBQUcsQ0FBQ0MsT0FBTyxDQUF5QjtJQUMvRCxPQUFPLElBQUlGLFFBQVFDLEdBQUcsQ0FBQ0MsT0FBTyxLQUFLLFNBQVNLLFVBQVVFLGNBQWMsQ0FBQyxXQUFXO1FBQzlFLE9BQU9GLFVBQVVHLE1BQU07SUFDekIsT0FBTyxJQUFJSCxVQUFVRSxjQUFjLENBQUMsWUFBWTtRQUM5QyxPQUFPRixVQUFVSSxPQUFPO0lBQzFCO0lBQ0EsZ0JBQWdCO0lBQ2hCLE9BQU9IO0FBQ1Q7QUFFTixNQUFNZixXQUFXO0lBQ2Y7OztHQUdDLEdBQ0RtQixJQUFJWixRQUFRQyxHQUFHLENBQUNDLE9BQU8sSUFBSVIsa0RBQW1CQSxDQUFDa0IsRUFBRTtJQUNqRDs7Ozs7OztHQU9DLEdBQ0ROLFFBQVFEO0lBQ1I7OztHQUdDLEdBQ0RWLGNBQWNBLGtFQUFBQTtJQUNkOzs7O0dBSUMsR0FDREMsb0JBQW9CQSx3RUFBQUE7SUFDcEI7Ozs7R0FJQyxHQUNEQyxjQUFjQSxrRUFBQUE7SUFDZDs7O0dBR0MsR0FDREMsZ0JBQWdCQSxvRUFBQUE7QUFDbEI7QUFFQSxpRUFBZUwsUUFBUUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL25vZGVfbW9kdWxlcy9leHBvLW1vZHVsZXMtY29yZS9zcmMvUGxhdGZvcm0udHM/ZTgxMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQbGF0Zm9ybU9TVHlwZSwgUGxhdGZvcm0gYXMgUmVhY3ROYXRpdmVQbGF0Zm9ybSB9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5cbmltcG9ydCB7XG4gIGlzRE9NQXZhaWxhYmxlLFxuICBjYW5Vc2VFdmVudExpc3RlbmVycyxcbiAgY2FuVXNlVmlld3BvcnQsXG4gIGlzQXN5bmNEZWJ1Z2dpbmcsXG59IGZyb20gJy4vZW52aXJvbm1lbnQvYnJvd3Nlcic7XG5cbmV4cG9ydCB0eXBlIFBsYXRmb3JtU2VsZWN0T1NUeXBlID0gUGxhdGZvcm1PU1R5cGUgfCAnbmF0aXZlJyB8ICdlbGVjdHJvbicgfCAnZGVmYXVsdCc7XG5cbmV4cG9ydCB0eXBlIFBsYXRmb3JtU2VsZWN0ID0gPFQ+KHNwZWNpZmljczogeyBbcGxhdGZvcm0gaW4gUGxhdGZvcm1TZWxlY3RPU1R5cGVdPzogVCB9KSA9PiBUO1xuXG5pZiAoX19ERVZfXyAmJiB0eXBlb2YgcHJvY2Vzcy5lbnYuRVhQT19PUyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgY29uc29sZS53YXJuKFxuICAgIGBUaGUgZ2xvYmFsIHByb2Nlc3MuZW52LkVYUE9fT1MgaXMgbm90IGRlZmluZWQuIFRoaXMgc2hvdWxkIGJlIGlubGluZWQgYnkgYmFiZWwtcHJlc2V0LWV4cG8gZHVyaW5nIHRyYW5zZm9ybWF0aW9uLmBcbiAgKTtcbn1cblxuY29uc3QgbmF0aXZlU2VsZWN0ID1cbiAgdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCdcbiAgICA/IFJlYWN0TmF0aXZlUGxhdGZvcm0uc2VsZWN0XG4gICAgOiAvLyBwcm9jZXNzLmVudi5FWFBPX09TIGlzIGluamVjdGVkIGJ5IGBiYWJlbC1wcmVzZXQtZXhwb2AgYW5kIGF2YWlsYWJsZSBpbiBib3RoIGNsaWVudCBhbmQgYHJlYWN0LXNlcnZlcmAgZW52aXJvbm1lbnRzLlxuICAgICAgLy8gT3B0IHRvIHVzZSB0aGUgZW52IHZhciB3aGVuIHBvc3NpYmxlLCBhbmQgZmFsbGJhY2sgdG8gdGhlIFJlYWN0IE5hdGl2ZSBQbGF0Zm9ybSBtb2R1bGUgd2hlbiBpdCdzIG5vdCAoYXJiaXRyYXJ5IGJ1bmRsZXJzIGFuZCB0cmFuc2Zvcm1lcnMpLlxuICAgICAgZnVuY3Rpb24gc2VsZWN0PFQ+KHNwZWNpZmljczogeyBbcGxhdGZvcm0gaW4gUGxhdGZvcm1TZWxlY3RPU1R5cGVdPzogVCB9KTogVCB8IHVuZGVmaW5lZCB7XG4gICAgICAgIGlmICghcHJvY2Vzcy5lbnYuRVhQT19PUykgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICAgICAgaWYgKHNwZWNpZmljcy5oYXNPd25Qcm9wZXJ0eShwcm9jZXNzLmVudi5FWFBPX09TKSkge1xuICAgICAgICAgIHJldHVybiBzcGVjaWZpY3NbcHJvY2Vzcy5lbnYuRVhQT19PUyBhcyBQbGF0Zm9ybVNlbGVjdE9TVHlwZV0hO1xuICAgICAgICB9IGVsc2UgaWYgKHByb2Nlc3MuZW52LkVYUE9fT1MgIT09ICd3ZWInICYmIHNwZWNpZmljcy5oYXNPd25Qcm9wZXJ0eSgnbmF0aXZlJykpIHtcbiAgICAgICAgICByZXR1cm4gc3BlY2lmaWNzLm5hdGl2ZSE7XG4gICAgICAgIH0gZWxzZSBpZiAoc3BlY2lmaWNzLmhhc093blByb3BlcnR5KCdkZWZhdWx0JykpIHtcbiAgICAgICAgICByZXR1cm4gc3BlY2lmaWNzLmRlZmF1bHQhO1xuICAgICAgICB9XG4gICAgICAgIC8vIGRvIG5vdGhpbmcuLi5cbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICAgIH07XG5cbmNvbnN0IFBsYXRmb3JtID0ge1xuICAvKipcbiAgICogRGVub3RlcyB0aGUgY3VycmVudGx5IHJ1bm5pbmcgcGxhdGZvcm0uXG4gICAqIENhbiBiZSBvbmUgb2YgaW9zLCBhbmRyb2lkLCB3ZWIuXG4gICAqL1xuICBPUzogcHJvY2Vzcy5lbnYuRVhQT19PUyB8fCBSZWFjdE5hdGl2ZVBsYXRmb3JtLk9TLFxuICAvKipcbiAgICogUmV0dXJucyB0aGUgdmFsdWUgd2l0aCB0aGUgbWF0Y2hpbmcgcGxhdGZvcm0uXG4gICAqIE9iamVjdCBrZXlzIGNhbiBiZSBhbnkgb2YgaW9zLCBhbmRyb2lkLCBuYXRpdmUsIHdlYiwgZGVmYXVsdC5cbiAgICpcbiAgICogQGlvcyBpb3MsIG5hdGl2ZSwgZGVmYXVsdFxuICAgKiBAYW5kcm9pZCBhbmRyb2lkLCBuYXRpdmUsIGRlZmF1bHRcbiAgICogQHdlYiB3ZWIsIGRlZmF1bHRcbiAgICovXG4gIHNlbGVjdDogbmF0aXZlU2VsZWN0IGFzIFBsYXRmb3JtU2VsZWN0LFxuICAvKipcbiAgICogRGVub3RlcyBpZiB0aGUgRE9NIEFQSSBpcyBhdmFpbGFibGUgaW4gdGhlIGN1cnJlbnQgZW52aXJvbm1lbnQuXG4gICAqIFRoZSBET00gaXMgbm90IGF2YWlsYWJsZSBpbiBuYXRpdmUgUmVhY3QgcnVudGltZXMgYW5kIE5vZGUuanMuXG4gICAqL1xuICBpc0RPTUF2YWlsYWJsZSxcbiAgLyoqXG4gICAqIERlbm90ZXMgaWYgdGhlIGN1cnJlbnQgZW52aXJvbm1lbnQgY2FuIGF0dGFjaCBldmVudCBsaXN0ZW5lcnNcbiAgICogdG8gdGhlIHdpbmRvdy4gVGhpcyB3aWxsIHJldHVybiBmYWxzZSBpbiBuYXRpdmUgUmVhY3RcbiAgICogcnVudGltZXMgYW5kIE5vZGUuanMuXG4gICAqL1xuICBjYW5Vc2VFdmVudExpc3RlbmVycyxcbiAgLyoqXG4gICAqIERlbm90ZXMgaWYgdGhlIGN1cnJlbnQgZW52aXJvbm1lbnQgY2FuIGluc3BlY3QgcHJvcGVydGllcyBvZiB0aGVcbiAgICogc2NyZWVuIG9uIHdoaWNoIHRoZSBjdXJyZW50IHdpbmRvdyBpcyBiZWluZyByZW5kZXJlZC4gVGhpcyB3aWxsXG4gICAqIHJldHVybiBmYWxzZSBpbiBuYXRpdmUgUmVhY3QgcnVudGltZXMgYW5kIE5vZGUuanMuXG4gICAqL1xuICBjYW5Vc2VWaWV3cG9ydCxcbiAgLyoqXG4gICAqIElmIHRoZSBKYXZhU2NyaXB0IGlzIGJlaW5nIGV4ZWN1dGVkIGluIGEgcmVtb3RlIEphdmFTY3JpcHQgZW52aXJvbm1lbnQuXG4gICAqIFdoZW4gYHRydWVgLCBzeW5jaHJvbm91cyBuYXRpdmUgaW52b2NhdGlvbnMgY2Fubm90IGJlIGV4ZWN1dGVkLlxuICAgKi9cbiAgaXNBc3luY0RlYnVnZ2luZyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFBsYXRmb3JtO1xuIl0sIm5hbWVzIjpbIlBsYXRmb3JtIiwiUmVhY3ROYXRpdmVQbGF0Zm9ybSIsImlzRE9NQXZhaWxhYmxlIiwiY2FuVXNlRXZlbnRMaXN0ZW5lcnMiLCJjYW5Vc2VWaWV3cG9ydCIsImlzQXN5bmNEZWJ1Z2dpbmciLCJfX0RFVl9fIiwicHJvY2VzcyIsImVudiIsIkVYUE9fT1MiLCJjb25zb2xlIiwid2FybiIsIm5hdGl2ZVNlbGVjdCIsInNlbGVjdCIsInNwZWNpZmljcyIsInVuZGVmaW5lZCIsImhhc093blByb3BlcnR5IiwibmF0aXZlIiwiZGVmYXVsdCIsIk9TIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/Platform.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/Refs.ts":
/*!********************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/Refs.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSnapshotFriendlyRef: () => (/* binding */ createSnapshotFriendlyRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Create a React ref object that is friendly for snapshots.\n * It will be represented as `[React.ref]` in snapshots.\n * @returns A React ref object.\n */ function createSnapshotFriendlyRef() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createRef)();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9SZWZzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRDtBQUVsRDs7OztDQUlDLEdBQ00sU0FBU0M7SUFDZCxPQUFPRCxnREFBU0E7QUFDbEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9ub2RlX21vZHVsZXMvZXhwby1tb2R1bGVzLWNvcmUvc3JjL1JlZnMudHM/Y2ExNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVSZWYsIHR5cGUgUmVmT2JqZWN0IH0gZnJvbSAncmVhY3QnO1xuXG4vKipcbiAqIENyZWF0ZSBhIFJlYWN0IHJlZiBvYmplY3QgdGhhdCBpcyBmcmllbmRseSBmb3Igc25hcHNob3RzLlxuICogSXQgd2lsbCBiZSByZXByZXNlbnRlZCBhcyBgW1JlYWN0LnJlZl1gIGluIHNuYXBzaG90cy5cbiAqIEByZXR1cm5zIEEgUmVhY3QgcmVmIG9iamVjdC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZVNuYXBzaG90RnJpZW5kbHlSZWY8VD4oKTogUmVmT2JqZWN0PFQgfCBudWxsPiB7XG4gIHJldHVybiBjcmVhdGVSZWY8VD4oKTtcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVSZWYiLCJjcmVhdGVTbmFwc2hvdEZyaWVuZGx5UmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/Refs.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/SharedObject.ts":
/*!****************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/SharedObject.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ensureNativeModulesAreInstalled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ensureNativeModulesAreInstalled */ \"../../node_modules/expo-modules-core/src/ensureNativeModulesAreInstalled.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n(0,_ensureNativeModulesAreInstalled__WEBPACK_IMPORTED_MODULE_0__.ensureNativeModulesAreInstalled)();\nconst SharedObject = globalThis.expo.SharedObject;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SharedObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9TaGFyZWRPYmplY3QudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7NkRBRW9GO0FBR3BGQSxpR0FBK0JBO0FBRS9CLE1BQU1DLGVBQWVDLFdBQVdDLElBQUksQ0FBQ0YsWUFBWTtBQUVqRCxpRUFBZUEsWUFBWUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL25vZGVfbW9kdWxlcy9leHBvLW1vZHVsZXMtY29yZS9zcmMvU2hhcmVkT2JqZWN0LnRzPzNmYzgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBlbnN1cmVOYXRpdmVNb2R1bGVzQXJlSW5zdGFsbGVkIH0gZnJvbSAnLi9lbnN1cmVOYXRpdmVNb2R1bGVzQXJlSW5zdGFsbGVkJztcbmltcG9ydCB0eXBlIHsgU2hhcmVkT2JqZWN0IGFzIFNoYXJlZE9iamVjdFR5cGUgfSBmcm9tICcuL3RzLWRlY2xhcmF0aW9ucy9TaGFyZWRPYmplY3QnO1xuXG5lbnN1cmVOYXRpdmVNb2R1bGVzQXJlSW5zdGFsbGVkKCk7XG5cbmNvbnN0IFNoYXJlZE9iamVjdCA9IGdsb2JhbFRoaXMuZXhwby5TaGFyZWRPYmplY3QgYXMgdHlwZW9mIFNoYXJlZE9iamVjdFR5cGU7XG5cbmV4cG9ydCBkZWZhdWx0IFNoYXJlZE9iamVjdDtcbiJdLCJuYW1lcyI6WyJlbnN1cmVOYXRpdmVNb2R1bGVzQXJlSW5zdGFsbGVkIiwiU2hhcmVkT2JqZWN0IiwiZ2xvYmFsVGhpcyIsImV4cG8iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/SharedObject.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/SharedRef.ts":
/*!*************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/SharedRef.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ensureNativeModulesAreInstalled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ensureNativeModulesAreInstalled */ \"../../node_modules/expo-modules-core/src/ensureNativeModulesAreInstalled.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n(0,_ensureNativeModulesAreInstalled__WEBPACK_IMPORTED_MODULE_0__.ensureNativeModulesAreInstalled)();\nconst SharedRef = globalThis.expo.SharedRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SharedRef);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9TaGFyZWRSZWYudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7NkRBRW9GO0FBR3BGQSxpR0FBK0JBO0FBRS9CLE1BQU1DLFlBQVlDLFdBQVdDLElBQUksQ0FBQ0YsU0FBUztBQUUzQyxpRUFBZUEsU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL25vZGVfbW9kdWxlcy9leHBvLW1vZHVsZXMtY29yZS9zcmMvU2hhcmVkUmVmLnRzPzk4ODIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBlbnN1cmVOYXRpdmVNb2R1bGVzQXJlSW5zdGFsbGVkIH0gZnJvbSAnLi9lbnN1cmVOYXRpdmVNb2R1bGVzQXJlSW5zdGFsbGVkJztcbmltcG9ydCB0eXBlIHsgU2hhcmVkUmVmIGFzIFNoYXJlZFJlZlR5cGUgfSBmcm9tICcuL3RzLWRlY2xhcmF0aW9ucy9TaGFyZWRSZWYnO1xuXG5lbnN1cmVOYXRpdmVNb2R1bGVzQXJlSW5zdGFsbGVkKCk7XG5cbmNvbnN0IFNoYXJlZFJlZiA9IGdsb2JhbFRoaXMuZXhwby5TaGFyZWRSZWYgYXMgdHlwZW9mIFNoYXJlZFJlZlR5cGU7XG5cbmV4cG9ydCBkZWZhdWx0IFNoYXJlZFJlZjtcbiJdLCJuYW1lcyI6WyJlbnN1cmVOYXRpdmVNb2R1bGVzQXJlSW5zdGFsbGVkIiwiU2hhcmVkUmVmIiwiZ2xvYmFsVGhpcyIsImV4cG8iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/SharedRef.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/TypedArrays.types.ts":
/*!*********************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/TypedArrays.types.ts ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/** A union type for all integer based [`TypedArray` objects](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypedArray#typedarray_objects). */ /** A [`TypedArray`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypedArray) describes an array-like view of an underlying binary data buffer. */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9UeXBlZEFycmF5cy50eXBlcy50cyIsIm1hcHBpbmdzIjoiO0FBQUEsK0tBQStLLEdBUy9LLG9MQUFvTCxHQUMzRiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL25vZGVfbW9kdWxlcy9leHBvLW1vZHVsZXMtY29yZS9zcmMvVHlwZWRBcnJheXMudHlwZXMudHM/MGNhMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogQSB1bmlvbiB0eXBlIGZvciBhbGwgaW50ZWdlciBiYXNlZCBbYFR5cGVkQXJyYXlgIG9iamVjdHNdKGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0phdmFTY3JpcHQvUmVmZXJlbmNlL0dsb2JhbF9PYmplY3RzL1R5cGVkQXJyYXkjdHlwZWRhcnJheV9vYmplY3RzKS4gKi9cbmV4cG9ydCB0eXBlIEludEJhc2VkVHlwZWRBcnJheSA9IEludDhBcnJheSB8IEludDE2QXJyYXkgfCBJbnQzMkFycmF5O1xuXG4vKiogQSB1bmlvbiB0eXBlIGZvciBhbGwgdW5zaWduZWQgaW50ZWdlciBiYXNlZCBbYFR5cGVkQXJyYXlgIG9iamVjdHNdKGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0phdmFTY3JpcHQvUmVmZXJlbmNlL0dsb2JhbF9PYmplY3RzL1R5cGVkQXJyYXkjdHlwZWRhcnJheV9vYmplY3RzKS4gKi9cbmV4cG9ydCB0eXBlIFVpbnRCYXNlZFR5cGVkQXJyYXkgPSBVaW50OEFycmF5IHwgVWludDhDbGFtcGVkQXJyYXkgfCBVaW50MTZBcnJheSB8IFVpbnQzMkFycmF5O1xuXG4vKiogQSB1bmlvbiB0eXBlIGZvciBhbGwgZmxvYXRpbmcgcG9pbnQgYmFzZWQgW2BUeXBlZEFycmF5YCBvYmplY3RzXShodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9KYXZhU2NyaXB0L1JlZmVyZW5jZS9HbG9iYWxfT2JqZWN0cy9UeXBlZEFycmF5I3R5cGVkYXJyYXlfb2JqZWN0cykuICovXG5leHBvcnQgdHlwZSBGbG9hdEJhc2VkVHlwZWRBcnJheSA9IEZsb2F0MzJBcnJheSB8IEZsb2F0NjRBcnJheTtcblxuLyoqIEEgW2BUeXBlZEFycmF5YF0oaHR0cHM6Ly9kZXZlbG9wZXIubW96aWxsYS5vcmcvZW4tVVMvZG9jcy9XZWIvSmF2YVNjcmlwdC9SZWZlcmVuY2UvR2xvYmFsX09iamVjdHMvVHlwZWRBcnJheSkgZGVzY3JpYmVzIGFuIGFycmF5LWxpa2UgdmlldyBvZiBhbiB1bmRlcmx5aW5nIGJpbmFyeSBkYXRhIGJ1ZmZlci4gKi9cbmV4cG9ydCB0eXBlIFR5cGVkQXJyYXkgPSBJbnRCYXNlZFR5cGVkQXJyYXkgfCBVaW50QmFzZWRUeXBlZEFycmF5IHwgRmxvYXRCYXNlZFR5cGVkQXJyYXk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/TypedArrays.types.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/ensureNativeModulesAreInstalled.ts":
/*!***********************************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/ensureNativeModulesAreInstalled.ts ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureNativeModulesAreInstalled: () => (/* binding */ ensureNativeModulesAreInstalled)\n/* harmony export */ });\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var _web__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./web */ \"../../node_modules/expo-modules-core/src/web/index.web.ts\");\n\n\n/**\n * Ensures that the native modules are installed in the current runtime.\n * Otherwise, it synchronously calls a native function that installs them.\n */ function ensureNativeModulesAreInstalled() {\n    if (globalThis.expo) {\n        return;\n    }\n    try {\n        if (react_native__WEBPACK_IMPORTED_MODULE_0__.Platform.OS === \"web\") {\n            // Requiring web folder sets up the `globalThis.expo` object.\n            (0,_web__WEBPACK_IMPORTED_MODULE_1__.registerWebGlobals)();\n        } else {\n            // TODO: ExpoModulesCore shouldn't be optional here,\n            // but to keep backwards compatibility let's just ignore it in SDK 50.\n            // In most cases the modules were already installed from the native side.\n            react_native__WEBPACK_IMPORTED_MODULE_0__.NativeModules.ExpoModulesCore?.installModules();\n        }\n    } catch (error) {\n        console.error(`Unable to install Expo modules: ${error}`);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/ensureNativeModulesAreInstalled.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/environment/browser.web.ts":
/*!***************************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/environment/browser.web.ts ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canUseEventListeners: () => (/* binding */ canUseEventListeners),\n/* harmony export */   canUseViewport: () => (/* binding */ canUseViewport),\n/* harmony export */   isAsyncDebugging: () => (/* binding */ isAsyncDebugging),\n/* harmony export */   isDOMAvailable: () => (/* binding */ isDOMAvailable)\n/* harmony export */ });\n// Used for delegating node actions when browser APIs aren't available\n// like in SSR websites.\nconst isDOMAvailable =  false && 0;\nconst canUseEventListeners = isDOMAvailable && !!(window.addEventListener || window.attachEvent);\nconst canUseViewport = isDOMAvailable && !!window.screen;\nconst isAsyncDebugging = false;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9lbnZpcm9ubWVudC9icm93c2VyLndlYi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBT0Esc0VBQXNFO0FBQ3RFLHdCQUF3QjtBQUNqQixNQUFNQSxpQkFBaUIsTUFBa0IsSUFBZSxDQUFnQ0csQ0FBQztBQUN6RixNQUFNQyx1QkFDWEosa0JBQWtCLENBQUMsQ0FBRUMsQ0FBQUEsT0FBT0ksZ0JBQWdCLElBQUlKLE9BQU9LLFdBQVcsRUFBRTtBQUMvRCxNQUFNQyxpQkFBaUJQLGtCQUFrQixDQUFDLENBQUNDLE9BQU9PLE1BQU0sQ0FBQztBQUN6RCxNQUFNQyxtQkFBbUIsTUFBTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL25vZGVfbW9kdWxlcy9leHBvLW1vZHVsZXMtY29yZS9zcmMvZW52aXJvbm1lbnQvYnJvd3Nlci53ZWIudHM/MGI0MyJdLCJzb3VyY2VzQ29udGVudCI6WyJkZWNsYXJlIGdsb2JhbCB7XG4gIC8vIEFkZCBJRS1zcGVjaWZpYyBpbnRlcmZhY2UgdG8gV2luZG93XG4gIGludGVyZmFjZSBXaW5kb3cge1xuICAgIGF0dGFjaEV2ZW50KGV2ZW50OiBzdHJpbmcsIGxpc3RlbmVyOiBFdmVudExpc3RlbmVyKTogYm9vbGVhbjtcbiAgfVxufVxuXG4vLyBVc2VkIGZvciBkZWxlZ2F0aW5nIG5vZGUgYWN0aW9ucyB3aGVuIGJyb3dzZXIgQVBJcyBhcmVuJ3QgYXZhaWxhYmxlXG4vLyBsaWtlIGluIFNTUiB3ZWJzaXRlcy5cbmV4cG9ydCBjb25zdCBpc0RPTUF2YWlsYWJsZSA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmICEhd2luZG93LmRvY3VtZW50Py5jcmVhdGVFbGVtZW50O1xuZXhwb3J0IGNvbnN0IGNhblVzZUV2ZW50TGlzdGVuZXJzID1cbiAgaXNET01BdmFpbGFibGUgJiYgISEod2luZG93LmFkZEV2ZW50TGlzdGVuZXIgfHwgd2luZG93LmF0dGFjaEV2ZW50KTtcbmV4cG9ydCBjb25zdCBjYW5Vc2VWaWV3cG9ydCA9IGlzRE9NQXZhaWxhYmxlICYmICEhd2luZG93LnNjcmVlbjtcbmV4cG9ydCBjb25zdCBpc0FzeW5jRGVidWdnaW5nID0gZmFsc2U7XG4iXSwibmFtZXMiOlsiaXNET01BdmFpbGFibGUiLCJ3aW5kb3ciLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJjYW5Vc2VFdmVudExpc3RlbmVycyIsImFkZEV2ZW50TGlzdGVuZXIiLCJhdHRhY2hFdmVudCIsImNhblVzZVZpZXdwb3J0Iiwic2NyZWVuIiwiaXNBc3luY0RlYnVnZ2luZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/environment/browser.web.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/errors/CodedError.ts":
/*!*********************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/errors/CodedError.ts ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CodedError: () => (/* binding */ CodedError)\n/* harmony export */ });\n/**\n * A general error class that should be used for all errors in Expo modules.\n * Guarantees a `code` field that can be used to differentiate between different\n * types of errors without further subclassing Error.\n */ class CodedError extends Error {\n    constructor(code, message){\n        super(message);\n        this.code = code;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9lcnJvcnMvQ29kZWRFcnJvci50cyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7Q0FJQyxHQUNNLE1BQU1BLG1CQUFtQkM7SUFJOUJDLFlBQVlDLElBQVksRUFBRUMsT0FBZSxDQUFFO1FBQ3pDLEtBQUssQ0FBQ0E7UUFDTixJQUFJLENBQUNELElBQUksR0FBR0E7SUFDZDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9lcnJvcnMvQ29kZWRFcnJvci50cz8xYWFkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQSBnZW5lcmFsIGVycm9yIGNsYXNzIHRoYXQgc2hvdWxkIGJlIHVzZWQgZm9yIGFsbCBlcnJvcnMgaW4gRXhwbyBtb2R1bGVzLlxuICogR3VhcmFudGVlcyBhIGBjb2RlYCBmaWVsZCB0aGF0IGNhbiBiZSB1c2VkIHRvIGRpZmZlcmVudGlhdGUgYmV0d2VlbiBkaWZmZXJlbnRcbiAqIHR5cGVzIG9mIGVycm9ycyB3aXRob3V0IGZ1cnRoZXIgc3ViY2xhc3NpbmcgRXJyb3IuXG4gKi9cbmV4cG9ydCBjbGFzcyBDb2RlZEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBjb2RlOiBzdHJpbmc7XG4gIGluZm8/OiBhbnk7XG5cbiAgY29uc3RydWN0b3IoY29kZTogc3RyaW5nLCBtZXNzYWdlOiBzdHJpbmcpIHtcbiAgICBzdXBlcihtZXNzYWdlKTtcbiAgICB0aGlzLmNvZGUgPSBjb2RlO1xuICB9XG59XG4iXSwibmFtZXMiOlsiQ29kZWRFcnJvciIsIkVycm9yIiwiY29uc3RydWN0b3IiLCJjb2RlIiwibWVzc2FnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/errors/CodedError.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/errors/UnavailabilityError.ts":
/*!******************************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/errors/UnavailabilityError.ts ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnavailabilityError: () => (/* binding */ UnavailabilityError)\n/* harmony export */ });\n/* harmony import */ var _CodedError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CodedError */ \"../../node_modules/expo-modules-core/src/errors/CodedError.ts\");\n/* harmony import */ var _Platform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../Platform */ \"../../node_modules/expo-modules-core/src/Platform.ts\");\n\n\n/**\n * A class for errors to be thrown when a property is accessed which is\n * unavailable, unsupported, or not currently implemented on the running\n * platform.\n */ class UnavailabilityError extends _CodedError__WEBPACK_IMPORTED_MODULE_0__.CodedError {\n    constructor(moduleName, propertyName){\n        super(\"ERR_UNAVAILABLE\", `The method or property ${moduleName}.${propertyName} is not available on ${_Platform__WEBPACK_IMPORTED_MODULE_1__[\"default\"].OS}, are you sure you've linked all the native dependencies properly?`);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9lcnJvcnMvVW5hdmFpbGFiaWxpdHlFcnJvci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMEM7QUFDUDtBQUVuQzs7OztDQUlDLEdBQ00sTUFBTUUsNEJBQTRCRixtREFBVUE7SUFDakRHLFlBQVlDLFVBQWtCLEVBQUVDLFlBQW9CLENBQUU7UUFDcEQsS0FBSyxDQUNILG1CQUNBLENBQUMsdUJBQXVCLEVBQUVELFdBQVcsQ0FBQyxFQUFFQyxhQUFhLHFCQUFxQixFQUFFSixvREFBVyxDQUFDLGtFQUFrRSxDQUFDO0lBRS9KO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9ub2RlX21vZHVsZXMvZXhwby1tb2R1bGVzLWNvcmUvc3JjL2Vycm9ycy9VbmF2YWlsYWJpbGl0eUVycm9yLnRzPzBlNjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ29kZWRFcnJvciB9IGZyb20gJy4vQ29kZWRFcnJvcic7XG5pbXBvcnQgUGxhdGZvcm0gZnJvbSAnLi4vUGxhdGZvcm0nO1xuXG4vKipcbiAqIEEgY2xhc3MgZm9yIGVycm9ycyB0byBiZSB0aHJvd24gd2hlbiBhIHByb3BlcnR5IGlzIGFjY2Vzc2VkIHdoaWNoIGlzXG4gKiB1bmF2YWlsYWJsZSwgdW5zdXBwb3J0ZWQsIG9yIG5vdCBjdXJyZW50bHkgaW1wbGVtZW50ZWQgb24gdGhlIHJ1bm5pbmdcbiAqIHBsYXRmb3JtLlxuICovXG5leHBvcnQgY2xhc3MgVW5hdmFpbGFiaWxpdHlFcnJvciBleHRlbmRzIENvZGVkRXJyb3Ige1xuICBjb25zdHJ1Y3Rvcihtb2R1bGVOYW1lOiBzdHJpbmcsIHByb3BlcnR5TmFtZTogc3RyaW5nKSB7XG4gICAgc3VwZXIoXG4gICAgICAnRVJSX1VOQVZBSUxBQkxFJyxcbiAgICAgIGBUaGUgbWV0aG9kIG9yIHByb3BlcnR5ICR7bW9kdWxlTmFtZX0uJHtwcm9wZXJ0eU5hbWV9IGlzIG5vdCBhdmFpbGFibGUgb24gJHtQbGF0Zm9ybS5PU30sIGFyZSB5b3Ugc3VyZSB5b3UndmUgbGlua2VkIGFsbCB0aGUgbmF0aXZlIGRlcGVuZGVuY2llcyBwcm9wZXJseT9gXG4gICAgKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIkNvZGVkRXJyb3IiLCJQbGF0Zm9ybSIsIlVuYXZhaWxhYmlsaXR5RXJyb3IiLCJjb25zdHJ1Y3RvciIsIm1vZHVsZU5hbWUiLCJwcm9wZXJ0eU5hbWUiLCJPUyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/errors/UnavailabilityError.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/hooks/useReleasingSharedObject.ts":
/*!**********************************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/hooks/useReleasingSharedObject.ts ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useReleasingSharedObject: () => (/* binding */ useReleasingSharedObject)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useReleasingSharedObject auto */ \n/**\n * Returns a shared object, which is automatically cleaned up when the component is unmounted.\n */ function useReleasingSharedObject(factory, dependencies) {\n    const objectRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const isFastRefresh = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const previousDependencies = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(dependencies);\n    if (objectRef.current == null) {\n        objectRef.current = factory();\n    }\n    const object = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        let newObject = objectRef.current;\n        const dependenciesAreEqual = previousDependencies.current?.length === dependencies.length && dependencies.every((value, index)=>value === previousDependencies.current[index]);\n        // If the dependencies have changed, release the previous object and create a new one, otherwise this has been called\n        // because of a fast refresh, and we don't want to release the object.\n        if (!newObject || !dependenciesAreEqual) {\n            objectRef.current?.release();\n            newObject = factory();\n            objectRef.current = newObject;\n            previousDependencies.current = dependencies;\n        } else {\n            isFastRefresh.current = true;\n        }\n        return newObject;\n    }, dependencies);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        isFastRefresh.current = false;\n        return ()=>{\n            // This will be called on every fast refresh and on unmount, but we only want to release the object on unmount.\n            if (!isFastRefresh.current && objectRef.current) {\n                objectRef.current.release();\n            }\n        };\n    }, []);\n    return object;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/hooks/useReleasingSharedObject.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/index.ts":
/*!*********************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/index.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CodedError: () => (/* reexport safe */ _errors_CodedError__WEBPACK_IMPORTED_MODULE_15__.CodedError),\n/* harmony export */   EventEmitter: () => (/* reexport safe */ _EventEmitter__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   LegacyEventEmitter: () => (/* reexport safe */ _LegacyEventEmitter__WEBPACK_IMPORTED_MODULE_17__.LegacyEventEmitter),\n/* harmony export */   NativeModule: () => (/* reexport safe */ _NativeModule__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   NativeModulesProxy: () => (/* reexport safe */ _NativeModulesProxy__WEBPACK_IMPORTED_MODULE_18__[\"default\"]),\n/* harmony export */   Platform: () => (/* reexport safe */ _Platform__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   SharedObject: () => (/* reexport safe */ _SharedObject__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   SharedRef: () => (/* reexport safe */ _SharedRef__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   UnavailabilityError: () => (/* reexport safe */ _errors_UnavailabilityError__WEBPACK_IMPORTED_MODULE_16__.UnavailabilityError),\n/* harmony export */   requireNativeViewManager: () => (/* reexport safe */ _NativeViewManagerAdapter__WEBPACK_IMPORTED_MODULE_6__.requireNativeViewManager),\n/* harmony export */   uuid: () => (/* reexport safe */ _uuid__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _NativeModule__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NativeModule */ \"../../node_modules/expo-modules-core/src/NativeModule.ts\");\n/* harmony import */ var _SharedObject__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SharedObject */ \"../../node_modules/expo-modules-core/src/SharedObject.ts\");\n/* harmony import */ var _SharedRef__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SharedRef */ \"../../node_modules/expo-modules-core/src/SharedRef.ts\");\n/* harmony import */ var _Platform__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Platform */ \"../../node_modules/expo-modules-core/src/Platform.ts\");\n/* harmony import */ var _uuid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./uuid */ \"../../node_modules/expo-modules-core/src/uuid/index.ts\");\n/* harmony import */ var _EventEmitter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./EventEmitter */ \"../../node_modules/expo-modules-core/src/EventEmitter.ts\");\n/* harmony import */ var _NativeViewManagerAdapter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./NativeViewManagerAdapter */ \"../../node_modules/expo-modules-core/src/NativeViewManagerAdapter.tsx\");\n/* harmony import */ var _requireNativeModule__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./requireNativeModule */ \"../../node_modules/expo-modules-core/src/requireNativeModule.web.ts\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _requireNativeModule__WEBPACK_IMPORTED_MODULE_7__) if([\"default\",\"NativeModule\",\"SharedObject\",\"SharedRef\",\"Platform\",\"uuid\",\"EventEmitter\",\"requireNativeViewManager\",\"CodedError\",\"UnavailabilityError\",\"LegacyEventEmitter\",\"NativeModulesProxy\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _requireNativeModule__WEBPACK_IMPORTED_MODULE_7__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _registerWebModule__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./registerWebModule */ \"../../node_modules/expo-modules-core/src/registerWebModule.ts\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _registerWebModule__WEBPACK_IMPORTED_MODULE_8__) if([\"default\",\"NativeModule\",\"SharedObject\",\"SharedRef\",\"Platform\",\"uuid\",\"EventEmitter\",\"requireNativeViewManager\",\"CodedError\",\"UnavailabilityError\",\"LegacyEventEmitter\",\"NativeModulesProxy\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _registerWebModule__WEBPACK_IMPORTED_MODULE_8__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _TypedArrays_types__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./TypedArrays.types */ \"../../node_modules/expo-modules-core/src/TypedArrays.types.ts\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _TypedArrays_types__WEBPACK_IMPORTED_MODULE_9__) if([\"default\",\"NativeModule\",\"SharedObject\",\"SharedRef\",\"Platform\",\"uuid\",\"EventEmitter\",\"requireNativeViewManager\",\"CodedError\",\"UnavailabilityError\",\"LegacyEventEmitter\",\"NativeModulesProxy\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _TypedArrays_types__WEBPACK_IMPORTED_MODULE_9__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _PermissionsInterface__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PermissionsInterface */ \"../../node_modules/expo-modules-core/src/PermissionsInterface.ts\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _PermissionsInterface__WEBPACK_IMPORTED_MODULE_10__) if([\"default\",\"NativeModule\",\"SharedObject\",\"SharedRef\",\"Platform\",\"uuid\",\"EventEmitter\",\"requireNativeViewManager\",\"CodedError\",\"UnavailabilityError\",\"LegacyEventEmitter\",\"NativeModulesProxy\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _PermissionsInterface__WEBPACK_IMPORTED_MODULE_10__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _PermissionsHook__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./PermissionsHook */ \"../../node_modules/expo-modules-core/src/PermissionsHook.ts\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _PermissionsHook__WEBPACK_IMPORTED_MODULE_11__) if([\"default\",\"NativeModule\",\"SharedObject\",\"SharedRef\",\"Platform\",\"uuid\",\"EventEmitter\",\"requireNativeViewManager\",\"CodedError\",\"UnavailabilityError\",\"LegacyEventEmitter\",\"NativeModulesProxy\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _PermissionsHook__WEBPACK_IMPORTED_MODULE_11__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _Refs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Refs */ \"../../node_modules/expo-modules-core/src/Refs.ts\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _Refs__WEBPACK_IMPORTED_MODULE_12__) if([\"default\",\"NativeModule\",\"SharedObject\",\"SharedRef\",\"Platform\",\"uuid\",\"EventEmitter\",\"requireNativeViewManager\",\"CodedError\",\"UnavailabilityError\",\"LegacyEventEmitter\",\"NativeModulesProxy\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _Refs__WEBPACK_IMPORTED_MODULE_12__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _hooks_useReleasingSharedObject__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hooks/useReleasingSharedObject */ \"../../node_modules/expo-modules-core/src/hooks/useReleasingSharedObject.ts\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _hooks_useReleasingSharedObject__WEBPACK_IMPORTED_MODULE_13__) if([\"default\",\"NativeModule\",\"SharedObject\",\"SharedRef\",\"Platform\",\"uuid\",\"EventEmitter\",\"requireNativeViewManager\",\"CodedError\",\"UnavailabilityError\",\"LegacyEventEmitter\",\"NativeModulesProxy\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _hooks_useReleasingSharedObject__WEBPACK_IMPORTED_MODULE_13__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _reload__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./reload */ \"../../node_modules/expo-modules-core/src/reload.ts\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _reload__WEBPACK_IMPORTED_MODULE_14__) if([\"default\",\"NativeModule\",\"SharedObject\",\"SharedRef\",\"Platform\",\"uuid\",\"EventEmitter\",\"requireNativeViewManager\",\"CodedError\",\"UnavailabilityError\",\"LegacyEventEmitter\",\"NativeModulesProxy\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _reload__WEBPACK_IMPORTED_MODULE_14__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _errors_CodedError__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./errors/CodedError */ \"../../node_modules/expo-modules-core/src/errors/CodedError.ts\");\n/* harmony import */ var _errors_UnavailabilityError__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./errors/UnavailabilityError */ \"../../node_modules/expo-modules-core/src/errors/UnavailabilityError.ts\");\n/* harmony import */ var _LegacyEventEmitter__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./LegacyEventEmitter */ \"../../node_modules/expo-modules-core/src/LegacyEventEmitter.ts\");\n/* harmony import */ var _NativeModulesProxy__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./NativeModulesProxy */ \"../../node_modules/expo-modules-core/src/NativeModulesProxy.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Errors\n\n\n// Deprecated\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/index.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/registerWebModule.ts":
/*!*********************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/registerWebModule.ts ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerWebModule: () => (/* binding */ registerWebModule)\n/* harmony export */ });\n/* harmony import */ var _ensureNativeModulesAreInstalled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ensureNativeModulesAreInstalled */ \"../../node_modules/expo-modules-core/src/ensureNativeModulesAreInstalled.ts\");\n\n/**\n * Registers a web module.\n * @param moduleImplementation A class that extends `NativeModule`. The class is registered under `globalThis.expo.modules[className]`.\n * @param moduleName – a name to register the module under `globalThis.expo.modules[className]`.\n * @returns A singleton instance of the class passed into arguments.\n */ function registerWebModule(moduleImplementation, moduleName) {\n    (0,_ensureNativeModulesAreInstalled__WEBPACK_IMPORTED_MODULE_0__.ensureNativeModulesAreInstalled)();\n    moduleName = moduleName ?? moduleImplementation.name;\n    if (!moduleName) {\n        throw new Error(\"Web module implementation is missing a name - it is either not a class or has been minified. Pass the name as a second argument to the `registerWebModule` function.\");\n    }\n    if (!globalThis?.expo?.modules) {\n        globalThis.expo.modules = {};\n    }\n    if (globalThis.expo.modules[moduleName]) {\n        return globalThis.expo.modules[moduleName];\n    }\n    globalThis.expo.modules[moduleName] = new moduleImplementation();\n    return globalThis.expo.modules[moduleName];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/registerWebModule.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/reload.ts":
/*!**********************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/reload.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reloadAppAsync: () => (/* binding */ reloadAppAsync)\n/* harmony export */ });\n/**\n * Reloads the app. This method works for both release and debug builds.\n *\n * Unlike [`Updates.reloadAsync()`](/versions/latest/sdk/updates/#updatesreloadasync),\n * this function does not use a new update even if one is available. It only reloads the app using the same JavaScript bundle that is currently running.\n *\n * @param reason The reason for reloading the app. This is used only for some platforms.\n */ async function reloadAppAsync(reason = \"Reloaded from JS call\") {\n    await globalThis.expo?.reloadAppAsync(reason);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9yZWxvYWQudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7Ozs7O0NBT0MsR0FDTSxlQUFlQSxlQUFlQyxTQUFpQix1QkFBdUI7SUFDM0UsTUFBTUMsV0FBV0MsSUFBSSxFQUFFSCxlQUFlQztBQUN4QyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL25vZGVfbW9kdWxlcy9leHBvLW1vZHVsZXMtY29yZS9zcmMvcmVsb2FkLnRzP2QwYWEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZWxvYWRzIHRoZSBhcHAuIFRoaXMgbWV0aG9kIHdvcmtzIGZvciBib3RoIHJlbGVhc2UgYW5kIGRlYnVnIGJ1aWxkcy5cbiAqXG4gKiBVbmxpa2UgW2BVcGRhdGVzLnJlbG9hZEFzeW5jKClgXSgvdmVyc2lvbnMvbGF0ZXN0L3Nkay91cGRhdGVzLyN1cGRhdGVzcmVsb2FkYXN5bmMpLFxuICogdGhpcyBmdW5jdGlvbiBkb2VzIG5vdCB1c2UgYSBuZXcgdXBkYXRlIGV2ZW4gaWYgb25lIGlzIGF2YWlsYWJsZS4gSXQgb25seSByZWxvYWRzIHRoZSBhcHAgdXNpbmcgdGhlIHNhbWUgSmF2YVNjcmlwdCBidW5kbGUgdGhhdCBpcyBjdXJyZW50bHkgcnVubmluZy5cbiAqXG4gKiBAcGFyYW0gcmVhc29uIFRoZSByZWFzb24gZm9yIHJlbG9hZGluZyB0aGUgYXBwLiBUaGlzIGlzIHVzZWQgb25seSBmb3Igc29tZSBwbGF0Zm9ybXMuXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiByZWxvYWRBcHBBc3luYyhyZWFzb246IHN0cmluZyA9ICdSZWxvYWRlZCBmcm9tIEpTIGNhbGwnKTogUHJvbWlzZTx2b2lkPiB7XG4gIGF3YWl0IGdsb2JhbFRoaXMuZXhwbz8ucmVsb2FkQXBwQXN5bmMocmVhc29uKTtcbn1cbiJdLCJuYW1lcyI6WyJyZWxvYWRBcHBBc3luYyIsInJlYXNvbiIsImdsb2JhbFRoaXMiLCJleHBvIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/reload.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/requireNativeModule.web.ts":
/*!***************************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/requireNativeModule.web.ts ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   requireNativeModule: () => (/* binding */ requireNativeModule),\n/* harmony export */   requireOptionalNativeModule: () => (/* binding */ requireOptionalNativeModule)\n/* harmony export */ });\nfunction requireNativeModule(moduleName) {\n    const nativeModule = requireOptionalNativeModule(moduleName);\n    if (nativeModule != null) {\n        return nativeModule;\n    }\n    if (true) {\n        // For SSR, we expect not to have native modules available, but to avoid crashing from SSR resolutions, we return an empty object.\n        return {};\n    }\n    throw new Error(`Cannot find native module '${moduleName}'`);\n}\nfunction requireOptionalNativeModule(moduleName) {\n    if (typeof globalThis.ExpoDomWebView === \"object\" && globalThis?.expo?.modules != null) {\n        return globalThis.expo?.modules?.[moduleName] ?? null;\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/requireNativeModule.web.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/ts-declarations/global.ts":
/*!**************************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/ts-declarations/global.ts ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/ts-declarations/global.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/uuid/index.ts":
/*!**************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/uuid/index.ts ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _uuid__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./uuid */ \"../../node_modules/expo-modules-core/src/uuid/uuid.web.ts\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy91dWlkL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy91dWlkL2luZGV4LnRzPzRmMTciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gJy4vdXVpZCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/uuid/index.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/uuid/lib/bytesToUuid.ts":
/*!************************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/uuid/lib/bytesToUuid.ts ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */ const byteToHex = [];\nfor(let i = 0; i < 256; ++i){\n    byteToHex[i] = (i + 0x100).toString(16).substr(1);\n}\nfunction bytesToUuid(buf, offset) {\n    let i = offset || 0;\n    const bth = byteToHex;\n    // join used to fix memory issue caused by concatenation: https://bugs.chromium.org/p/v8/issues/detail?id=3175#c4\n    return [\n        bth[buf[i++]],\n        bth[buf[i++]],\n        bth[buf[i++]],\n        bth[buf[i++]],\n        \"-\",\n        bth[buf[i++]],\n        bth[buf[i++]],\n        \"-\",\n        bth[buf[i++]],\n        bth[buf[i++]],\n        \"-\",\n        bth[buf[i++]],\n        bth[buf[i++]],\n        \"-\",\n        bth[buf[i++]],\n        bth[buf[i++]],\n        bth[buf[i++]],\n        bth[buf[i++]],\n        bth[buf[i++]],\n        bth[buf[i++]]\n    ].join(\"\");\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (bytesToUuid);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/uuid/lib/bytesToUuid.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/uuid/lib/sha1.ts":
/*!*****************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/uuid/lib/sha1.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Adapted from Chris Veness' SHA1 code at\n// http://www.movable-type.co.uk/scripts/sha1.html\n\nfunction f(s, x, y, z) {\n    switch(s){\n        case 0:\n            return x & y ^ ~x & z;\n        case 1:\n            return x ^ y ^ z;\n        case 2:\n            return x & y ^ x & z ^ y & z;\n        case 3:\n            return x ^ y ^ z;\n        default:\n            return 0;\n    }\n}\nfunction ROTL(x, n) {\n    return x << n | x >>> 32 - n;\n}\nfunction sha1(bytes) {\n    const K = [\n        0x5a827999,\n        0x6ed9eba1,\n        0x8f1bbcdc,\n        0xca62c1d6\n    ];\n    const H = [\n        0x67452301,\n        0xefcdab89,\n        0x98badcfe,\n        0x10325476,\n        0xc3d2e1f0\n    ];\n    if (typeof bytes == \"string\") {\n        const msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n        bytes = new Array(msg.length);\n        for(let i = 0; i < msg.length; i++)bytes[i] = msg.charCodeAt(i);\n    }\n    bytes.push(0x80);\n    const l = bytes.length / 4 + 2;\n    const N = Math.ceil(l / 16);\n    const M = new Array(N);\n    for(let i = 0; i < N; i++){\n        M[i] = new Array(16);\n        for(let j = 0; j < 16; j++){\n            M[i][j] = bytes[i * 64 + j * 4] << 24 | bytes[i * 64 + j * 4 + 1] << 16 | bytes[i * 64 + j * 4 + 2] << 8 | bytes[i * 64 + j * 4 + 3];\n        }\n    }\n    M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);\n    M[N - 1][14] = Math.floor(M[N - 1][14]);\n    M[N - 1][15] = (bytes.length - 1) * 8 & 0xffffffff;\n    for(let i = 0; i < N; i++){\n        const W = new Array(80);\n        for(let t = 0; t < 16; t++)W[t] = M[i][t];\n        for(let t = 16; t < 80; t++){\n            W[t] = ROTL(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);\n        }\n        let a = H[0];\n        let b = H[1];\n        let c = H[2];\n        let d = H[3];\n        let e = H[4];\n        for(let t = 0; t < 80; t++){\n            const s = Math.floor(t / 20);\n            const T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[t] >>> 0;\n            e = d;\n            d = c;\n            c = ROTL(b, 30) >>> 0;\n            b = a;\n            a = T;\n        }\n        H[0] = H[0] + a >>> 0;\n        H[1] = H[1] + b >>> 0;\n        H[2] = H[2] + c >>> 0;\n        H[3] = H[3] + d >>> 0;\n        H[4] = H[4] + e >>> 0;\n    }\n    return [\n        H[0] >> 24 & 0xff,\n        H[0] >> 16 & 0xff,\n        H[0] >> 8 & 0xff,\n        H[0] & 0xff,\n        H[1] >> 24 & 0xff,\n        H[1] >> 16 & 0xff,\n        H[1] >> 8 & 0xff,\n        H[1] & 0xff,\n        H[2] >> 24 & 0xff,\n        H[2] >> 16 & 0xff,\n        H[2] >> 8 & 0xff,\n        H[2] & 0xff,\n        H[3] >> 24 & 0xff,\n        H[3] >> 16 & 0xff,\n        H[3] >> 8 & 0xff,\n        H[3] & 0xff,\n        H[4] >> 24 & 0xff,\n        H[4] >> 16 & 0xff,\n        H[4] >> 8 & 0xff,\n        H[4] & 0xff\n    ];\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (sha1);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/uuid/lib/sha1.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/uuid/lib/v35.ts":
/*!****************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/uuid/lib/v35.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _bytesToUuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bytesToUuid */ \"../../node_modules/expo-modules-core/src/uuid/lib/bytesToUuid.ts\");\n\nfunction uuidToBytes(uuid) {\n    // Note: We assume we're being passed a valid uuid string\n    const bytes = [];\n    uuid.replace(/[a-fA-F0-9]{2}/g, (hex)=>{\n        bytes.push(parseInt(hex, 16));\n        return \"\";\n    });\n    return bytes;\n}\nfunction stringToBytes(str) {\n    str = unescape(encodeURIComponent(str)); // UTF8 escape\n    const bytes = new Array(str.length);\n    for(let i = 0; i < str.length; i++){\n        bytes[i] = str.charCodeAt(i);\n    }\n    return bytes;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(name, version, hashfunc) {\n    const generateUUID = function(value, namespace, buf, offset) {\n        const off = buf && offset || 0;\n        if (typeof value == \"string\") value = stringToBytes(value);\n        if (typeof namespace == \"string\") namespace = uuidToBytes(namespace);\n        if (!Array.isArray(value)) throw TypeError(\"value must be an array of bytes\");\n        if (!Array.isArray(namespace) || namespace.length !== 16) throw TypeError(\"namespace must be uuid string or an Array of 16 byte values\");\n        // Per 4.3\n        const bytes = hashfunc(namespace.concat(value));\n        bytes[6] = bytes[6] & 0x0f | version;\n        bytes[8] = bytes[8] & 0x3f | 0x80;\n        if (buf) {\n            for(let idx = 0; idx < 16; ++idx){\n                buf[off + idx] = bytes[idx];\n            }\n        }\n        return (0,_bytesToUuid__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(bytes);\n    };\n    // Function#name is not settable on some platforms (#270)\n    try {\n        generateUUID.name = name;\n    } catch  {}\n    // Pre-defined namespaces, per Appendix C\n    generateUUID.DNS = \"6ba7b810-9dad-11d1-80b4-00c04fd430c8\";\n    generateUUID.URL = \"6ba7b811-9dad-11d1-80b4-00c04fd430c8\";\n    return generateUUID;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/uuid/lib/v35.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/uuid/uuid.types.ts":
/*!*******************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/uuid/uuid.types.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Uuidv5Namespace: () => (/* binding */ Uuidv5Namespace)\n/* harmony export */ });\n/**\n * Collection of utilities used for generating Universally Unique Identifiers.\n */ var Uuidv5Namespace;\n(function(Uuidv5Namespace) {\n    // Source of the UUIDs: https://datatracker.ietf.org/doc/html/rfc4122\n    Uuidv5Namespace[\"dns\"] = \"6ba7b810-9dad-11d1-80b4-00c04fd430c8\";\n    Uuidv5Namespace[\"url\"] = \"6ba7b811-9dad-11d1-80b4-00c04fd430c8\";\n    Uuidv5Namespace[\"oid\"] = \"6ba7b812-9dad-11d1-80b4-00c04fd430c8\";\n    Uuidv5Namespace[\"x500\"] = \"6ba7b814-9dad-11d1-80b4-00c04fd430c8\";\n})(Uuidv5Namespace || (Uuidv5Namespace = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy91dWlkL3V1aWQudHlwZXMudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDO1VBZ0JXQTtJQUNWLHFFQUFxRTs7Ozs7R0FEM0RBLG9CQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL25vZGVfbW9kdWxlcy9leHBvLW1vZHVsZXMtY29yZS9zcmMvdXVpZC91dWlkLnR5cGVzLnRzPzU2OTAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb2xsZWN0aW9uIG9mIHV0aWxpdGllcyB1c2VkIGZvciBnZW5lcmF0aW5nIFVuaXZlcnNhbGx5IFVuaXF1ZSBJZGVudGlmaWVycy5cbiAqL1xuZXhwb3J0IHR5cGUgVVVJRCA9IHtcbiAgLyoqXG4gICAqIEEgVVVJRCBnZW5lcmF0ZWQgcmFuZG9tbHkuXG4gICAqL1xuICB2NDogKCkgPT4gc3RyaW5nO1xuICAvKipcbiAgICogQSBVVUlEIGdlbmVyYXRlZCBiYXNlZCBvbiB0aGUgYHZhbHVlYCBhbmQgYG5hbWVzcGFjZWAgcGFyYW1ldGVycywgd2hpY2ggYWx3YXlzIHByb2R1Y2VzIHRoZSBzYW1lIHJlc3VsdCBmb3IgdGhlIHNhbWUgaW5wdXRzLlxuICAgKi9cbiAgdjU6IChuYW1lOiBzdHJpbmcsIG5hbWVzcGFjZTogc3RyaW5nIHwgbnVtYmVyW10pID0+IHN0cmluZztcbiAgbmFtZXNwYWNlOiB0eXBlb2YgVXVpZHY1TmFtZXNwYWNlO1xufTtcblxuLyoqXG4gKiBEZWZhdWx0IG5hbWVzcGFjZXMgZm9yIFVVSUQgdjUgZGVmaW5lZCBpbiBSRkMgNDEyMlxuICovXG5leHBvcnQgZW51bSBVdWlkdjVOYW1lc3BhY2Uge1xuICAvLyBTb3VyY2Ugb2YgdGhlIFVVSURzOiBodHRwczovL2RhdGF0cmFja2VyLmlldGYub3JnL2RvYy9odG1sL3JmYzQxMjJcbiAgZG5zID0gJzZiYTdiODEwLTlkYWQtMTFkMS04MGI0LTAwYzA0ZmQ0MzBjOCcsXG4gIHVybCA9ICc2YmE3YjgxMS05ZGFkLTExZDEtODBiNC0wMGMwNGZkNDMwYzgnLFxuICBvaWQgPSAnNmJhN2I4MTItOWRhZC0xMWQxLTgwYjQtMDBjMDRmZDQzMGM4JyxcbiAgeDUwMCA9ICc2YmE3YjgxNC05ZGFkLTExZDEtODBiNC0wMGMwNGZkNDMwYzgnLFxufVxuIl0sIm5hbWVzIjpbIlV1aWR2NU5hbWVzcGFjZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/uuid/uuid.types.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/uuid/uuid.web.ts":
/*!*****************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/uuid/uuid.web.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_sha1__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/sha1 */ \"../../node_modules/expo-modules-core/src/uuid/lib/sha1.ts\");\n/* harmony import */ var _lib_v35__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/v35 */ \"../../node_modules/expo-modules-core/src/uuid/lib/v35.ts\");\n/* harmony import */ var _uuid_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./uuid.types */ \"../../node_modules/expo-modules-core/src/uuid/uuid.types.ts\");\n\n\n\nfunction uuidv4() {\n    if (// We use this code path in jest-expo.\n     false || // Node.js has supported global crypto since v15.\n    typeof crypto === \"undefined\" && // Only use abstract imports in server environments.\n    \"undefined\" === \"undefined\") {\n        // NOTE: Metro statically extracts all `require` statements to resolve them for environments\n        // that don't support `require` natively. Here we check if we're running in a server environment\n        // by using the standard `typeof window` check, then running `eval` to skip Metro's static\n        // analysis and keep the `require` statement intact for runtime evaluation.\n        // eslint-disable-next-line no-eval\n        return eval(\"require\")(\"node:crypto\").randomUUID();\n    }\n    return crypto.randomUUID();\n}\nconst uuid = {\n    v4: uuidv4,\n    v5: (0,_lib_v35__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"v5\", 0x50, _lib_sha1__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n    namespace: _uuid_types__WEBPACK_IMPORTED_MODULE_2__.Uuidv5Namespace\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (uuid);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/uuid/uuid.web.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/web/CoreModule.ts":
/*!******************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/web/CoreModule.ts ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventEmitter: () => (/* binding */ EventEmitter),\n/* harmony export */   NativeModule: () => (/* binding */ NativeModule),\n/* harmony export */   SharedObject: () => (/* binding */ SharedObject),\n/* harmony export */   SharedRef: () => (/* binding */ SharedRef)\n/* harmony export */ });\nclass EventEmitter {\n    addListener(eventName, listener) {\n        if (!this.listeners) {\n            this.listeners = new Map();\n        }\n        if (!this.listeners?.has(eventName)) {\n            this.listeners?.set(eventName, new Set());\n        }\n        const previousListenerCount = this.listenerCount(eventName);\n        this.listeners?.get(eventName)?.add(listener);\n        if (previousListenerCount === 0 && this.listenerCount(eventName) === 1) {\n            this.startObserving(eventName);\n        }\n        return {\n            remove: ()=>{\n                this.removeListener(eventName, listener);\n            }\n        };\n    }\n    removeListener(eventName, listener) {\n        const hasRemovedListener = this.listeners?.get(eventName)?.delete(listener);\n        if (this.listenerCount(eventName) === 0 && hasRemovedListener) {\n            this.stopObserving(eventName);\n        }\n    }\n    removeAllListeners(eventName) {\n        const previousListenerCount = this.listenerCount(eventName);\n        this.listeners?.get(eventName)?.clear();\n        if (previousListenerCount > 0) {\n            this.stopObserving(eventName);\n        }\n    }\n    emit(eventName, ...args) {\n        const listeners = new Set(this.listeners?.get(eventName));\n        listeners.forEach((listener)=>{\n            // When the listener throws an error, don't stop the execution of subsequent listeners and\n            // don't propagate the error to the `emit` function. The motivation behind this is that\n            // errors thrown from a module or user's code shouldn't affect other modules' behavior.\n            try {\n                listener(...args);\n            } catch (error) {\n                console.error(error);\n            }\n        });\n    }\n    listenerCount(eventName) {\n        return this.listeners?.get(eventName)?.size ?? 0;\n    }\n    startObserving(eventName) {}\n    stopObserving(eventName) {}\n}\nclass NativeModule extends EventEmitter {\n}\nclass SharedObject extends EventEmitter {\n    release() {\n    // no-op on Web, but subclasses can override it if needed.\n    }\n}\nclass SharedRef extends SharedObject {\n    constructor(...args){\n        super(...args);\n        this.nativeRefType = \"unknown\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/web/CoreModule.ts\n");

/***/ }),

/***/ "../../node_modules/expo-modules-core/src/web/index.web.ts":
/*!*****************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/web/index.web.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerWebGlobals: () => (/* binding */ registerWebGlobals)\n/* harmony export */ });\n/* harmony import */ var _CoreModule__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CoreModule */ \"../../node_modules/expo-modules-core/src/web/CoreModule.ts\");\n/* harmony import */ var _uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../uuid */ \"../../node_modules/expo-modules-core/src/uuid/index.ts\");\n/* harmony import */ var _ts_declarations_global__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../ts-declarations/global */ \"../../node_modules/expo-modules-core/src/ts-declarations/global.ts\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _ts_declarations_global__WEBPACK_IMPORTED_MODULE_0__) if([\"default\",\"registerWebGlobals\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _ts_declarations_global__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// jest-expo imports to this file directly without going through the global types\n// Exporting the types to let jest-expo to know the globalThis types\n\nfunction registerWebGlobals() {\n    if (globalThis.expo) return;\n    globalThis.expo = {\n        EventEmitter: _CoreModule__WEBPACK_IMPORTED_MODULE_1__.EventEmitter,\n        NativeModule: _CoreModule__WEBPACK_IMPORTED_MODULE_1__.NativeModule,\n        SharedObject: _CoreModule__WEBPACK_IMPORTED_MODULE_1__.SharedObject,\n        SharedRef: _CoreModule__WEBPACK_IMPORTED_MODULE_1__.SharedRef,\n        modules: globalThis.ExpoDomWebView?.expoModulesProxy ?? {},\n        uuidv4: _uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"].v4,\n        uuidv5: _uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"].v5,\n        getViewConfig: ()=>{\n            throw new Error(\"Method not implemented.\");\n        },\n        reloadAppAsync: async ()=>{\n            window.location.reload();\n        }\n    };\n}\nregisterWebGlobals();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-modules-core/src/web/index.web.ts\n");

/***/ })

};
;