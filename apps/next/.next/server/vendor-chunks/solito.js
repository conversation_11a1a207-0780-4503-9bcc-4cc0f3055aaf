"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/solito";
exports.ids = ["vendor-chunks/solito"];
exports.modules = {

/***/ "../../node_modules/solito/build/app/navigation/index.js":
/*!***************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUpdateSearchParams: () => (/* reexport safe */ _use_update_search_params__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _use_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-router */ \"../../node_modules/solito/build/app/navigation/use-router.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _use_router__WEBPACK_IMPORTED_MODULE_0__) if([\"default\",\"useUpdateSearchParams\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _use_router__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _use_params__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-params */ \"../../node_modules/solito/build/app/navigation/use-params.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _use_params__WEBPACK_IMPORTED_MODULE_1__) if([\"default\",\"useUpdateSearchParams\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _use_params__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _use_search_params__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-search-params */ \"../../node_modules/solito/build/app/navigation/use-search-params.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _use_search_params__WEBPACK_IMPORTED_MODULE_2__) if([\"default\",\"useUpdateSearchParams\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _use_search_params__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _use_pathname__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-pathname */ \"../../node_modules/solito/build/app/navigation/use-pathname.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _use_pathname__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"useUpdateSearchParams\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _use_pathname__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _use_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./use-link */ \"../../node_modules/solito/build/app/navigation/use-link.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _use_link__WEBPACK_IMPORTED_MODULE_4__) if([\"default\",\"useUpdateSearchParams\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _use_link__WEBPACK_IMPORTED_MODULE_4__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _use_update_search_params__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./use-update-search-params */ \"../../node_modules/solito/build/app/navigation/use-update-search-params.js\");\n\n\n\n\n\n //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9hcHAvbmF2aWdhdGlvbi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTRCO0FBQ0E7QUFDTztBQUNMO0FBQ0o7QUFDbUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi8uLi9zcmMvYXBwL25hdmlnYXRpb24vaW5kZXgudHM/NGRiNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL3VzZS1yb3V0ZXInXG5leHBvcnQgKiBmcm9tICcuL3VzZS1wYXJhbXMnXG5leHBvcnQgKiBmcm9tICcuL3VzZS1zZWFyY2gtcGFyYW1zJ1xuZXhwb3J0ICogZnJvbSAnLi91c2UtcGF0aG5hbWUnXG5leHBvcnQgKiBmcm9tICcuL3VzZS1saW5rJ1xuZXhwb3J0IHsgZGVmYXVsdCBhcyB1c2VVcGRhdGVTZWFyY2hQYXJhbXMgfSBmcm9tICcuL3VzZS11cGRhdGUtc2VhcmNoLXBhcmFtcydcbiJdLCJuYW1lcyI6WyJkZWZhdWx0IiwidXNlVXBkYXRlU2VhcmNoUGFyYW1zIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/index.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/app/navigation/use-link.js":
/*!******************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-link.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLink: () => (/* binding */ useLink)\n/* harmony export */ });\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var _use_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-router */ \"../../node_modules/solito/build/app/navigation/use-router.js\");\n\n\nfunction useLink({ href, replace, experimental }) {\n    const router = (0,_use_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    // https://github.com/react-navigation/react-navigation/blob/main/packages/native/src/useLinkProps.tsx#L64\n    const onPress = (e)=>{\n        let shouldHandle = false;\n        if (react_native__WEBPACK_IMPORTED_MODULE_1__.Platform.OS !== \"web\" || !e) {\n            shouldHandle = e ? !e.defaultPrevented : true;\n        } else if (!e.defaultPrevented && // onPress prevented default\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        !(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) && // ignore clicks with modifier keys\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        (e.button == null || e.button === 0) && // ignore everything but left clicks\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        [\n            undefined,\n            null,\n            \"\",\n            \"self\"\n        ].includes(e.currentTarget?.target) // let browser handle \"target=_blank\" etc.\n        ) {\n            e.preventDefault();\n            shouldHandle = true;\n        }\n        if (shouldHandle) {\n            if (href === \"#\") {\n                // this is a way on web to stay on the same page\n                // useful for conditional hrefs\n                return;\n            }\n            if (replace) {\n                router.replace(href, {\n                    experimental\n                });\n            } else {\n                router.push(href);\n            }\n        }\n    };\n    return {\n        accessibilityRole: \"link\",\n        onPress,\n        href\n    };\n} //# sourceMappingURL=use-link.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/use-link.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/app/navigation/use-next-params.js":
/*!*************************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-next-params.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (()=>{\n    // need to cast this type to appease TS, idk why\n    return (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useParams)();\n}); //# sourceMappingURL=use-next-params.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9hcHAvbmF2aWdhdGlvbi91c2UtbmV4dC1wYXJhbXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTJDO0FBRTNDLGlFQUFlO0lBQ2IsZ0RBQWdEO0lBQ2hELE9BQU9BLDBEQUFTQTtBQUNsQixHQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vLi4vc3JjL2FwcC9uYXZpZ2F0aW9uL3VzZS1uZXh0LXBhcmFtcy50cz8yYTZmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcblxuZXhwb3J0IGRlZmF1bHQgKCkgPT4ge1xuICAvLyBuZWVkIHRvIGNhc3QgdGhpcyB0eXBlIHRvIGFwcGVhc2UgVFMsIGlkayB3aHlcbiAgcmV0dXJuIHVzZVBhcmFtcygpIGFzIFJlY29yZDxzdHJpbmcsIHN0cmluZz4gfCB1bmRlZmluZWRcbn1cbiJdLCJuYW1lcyI6WyJ1c2VQYXJhbXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/use-next-params.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/app/navigation/use-next-pathname.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-next-pathname.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (()=>(0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.usePathname)()); //# sourceMappingURL=use-next-pathname.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9hcHAvbmF2aWdhdGlvbi91c2UtbmV4dC1wYXRobmFtZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsaUVBQWUsSUFBTUEsNERBQVdBLEVBQUEsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uLy4uL3NyYy9hcHAvbmF2aWdhdGlvbi91c2UtbmV4dC1wYXRobmFtZS50cz9kZDViIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuXG5leHBvcnQgZGVmYXVsdCAoKSA9PiB1c2VQYXRobmFtZSgpXG4iXSwibmFtZXMiOlsidXNlUGF0aG5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/use-next-pathname.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/app/navigation/use-next-router.web.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-next-router.web.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useNextAppDirRouter: () => (/* reexport safe */ next_navigation__WEBPACK_IMPORTED_MODULE_0__.useRouter)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_0__);\n //# sourceMappingURL=use-next-router.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9hcHAvbmF2aWdhdGlvbi91c2UtbmV4dC1yb3V0ZXIud2ViLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uLy4uL3NyYy9hcHAvbmF2aWdhdGlvbi91c2UtbmV4dC1yb3V0ZXIud2ViLnRzPzhhYzEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgdXNlUm91dGVyIGFzIHVzZU5leHRBcHBEaXJSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG4iXSwibmFtZXMiOlsidXNlUm91dGVyIiwidXNlTmV4dEFwcERpclJvdXRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/use-next-router.web.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/app/navigation/use-next-search-params.js":
/*!********************************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-next-search-params.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (()=>{\n    // need to cast this type to appease TS, idk why\n    return (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useSearchParams)();\n}); //# sourceMappingURL=use-next-search-params.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9hcHAvbmF2aWdhdGlvbi91c2UtbmV4dC1zZWFyY2gtcGFyYW1zLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpRDtBQUVqRCxpRUFBZTtJQUNiLGdEQUFnRDtJQUNoRCxPQUFPQSxnRUFBZUE7QUFDeEIsR0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uLy4uL3NyYy9hcHAvbmF2aWdhdGlvbi91c2UtbmV4dC1zZWFyY2gtcGFyYW1zLnRzPzY2MzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuXG5leHBvcnQgZGVmYXVsdCAoKSA9PiB7XG4gIC8vIG5lZWQgdG8gY2FzdCB0aGlzIHR5cGUgdG8gYXBwZWFzZSBUUywgaWRrIHdoeVxuICByZXR1cm4gdXNlU2VhcmNoUGFyYW1zKCkgYXMgUmV0dXJuVHlwZTx0eXBlb2YgdXNlU2VhcmNoUGFyYW1zPiB8IHVuZGVmaW5lZFxufVxuIl0sIm5hbWVzIjpbInVzZVNlYXJjaFBhcmFtcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/use-next-search-params.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/app/navigation/use-params.js":
/*!********************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-params.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useParams: () => (/* binding */ useParams)\n/* harmony export */ });\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var _use_next_params__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-next-params */ \"../../node_modules/solito/build/app/navigation/use-next-params.js\");\n/* harmony import */ var _params_use_route__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../params/use-route */ \"../../node_modules/solito/build/params/use-route.web.js\");\n\n\n\nfunction useParams(_settings = {}) {\n    if (react_native__WEBPACK_IMPORTED_MODULE_0__.Platform.OS === \"web\") {\n        return (0,_use_next_params__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    }\n    const route = (0,_params_use_route__WEBPACK_IMPORTED_MODULE_2__.useRoute)();\n    if (!route) {\n        console.error(`[useParams] route is undefined. Is your ${react_native__WEBPACK_IMPORTED_MODULE_0__.Platform.OS} app properly configured for React Navigation?`);\n    }\n    return route?.params;\n} //# sourceMappingURL=use-params.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/use-params.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/app/navigation/use-pathname.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-pathname.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePathname: () => (/* binding */ usePathname)\n/* harmony export */ });\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var _use_next_pathname__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-next-pathname */ \"../../node_modules/solito/build/app/navigation/use-next-pathname.js\");\n/* harmony import */ var _params_use_route__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../params/use-route */ \"../../node_modules/solito/build/params/use-route.web.js\");\n\n\n\n// TODO test this with react navigation and expo router. does it work?\nfunction usePathname() {\n    if (react_native__WEBPACK_IMPORTED_MODULE_0__.Platform.OS === \"web\") {\n        return (0,_use_next_pathname__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    }\n    const path = (0,_params_use_route__WEBPACK_IMPORTED_MODULE_2__.useRoute)()?.path;\n    return path?.includes(\"?\") ? path.split(\"?\")[0] : path;\n} //# sourceMappingURL=use-pathname.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9hcHAvbmF2aWdhdGlvbi91c2UtcGF0aG5hbWUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF1QztBQUNVO0FBQ0E7QUFFakQsc0VBQXNFO0FBQ2hFLFNBQVVHO0lBQ2QsSUFBSUgsa0RBQVFBLENBQUNJLEVBQUUsS0FBSyxPQUFPO1FBQ3pCLE9BQU9ILDhEQUFlQTs7SUFHeEIsTUFBTUksT0FBT0gsMkRBQVFBLElBQUlHO0lBRXpCLE9BQU9BLE1BQU1DLFNBQVMsT0FBT0QsS0FBS0UsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEdBQUdGO0FBQ3BEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vLi4vc3JjL2FwcC9uYXZpZ2F0aW9uL3VzZS1wYXRobmFtZS50cz9hOTM0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBsYXRmb3JtIH0gZnJvbSAncmVhY3QtbmF0aXZlJ1xuaW1wb3J0IHVzZU5leHRQYXRobmFtZSBmcm9tICcuL3VzZS1uZXh0LXBhdGhuYW1lJ1xuaW1wb3J0IHsgdXNlUm91dGUgfSBmcm9tICcuLi8uLi9wYXJhbXMvdXNlLXJvdXRlJ1xuXG4vLyBUT0RPIHRlc3QgdGhpcyB3aXRoIHJlYWN0IG5hdmlnYXRpb24gYW5kIGV4cG8gcm91dGVyLiBkb2VzIGl0IHdvcms/XG5leHBvcnQgZnVuY3Rpb24gdXNlUGF0aG5hbWUoKSB7XG4gIGlmIChQbGF0Zm9ybS5PUyA9PT0gJ3dlYicpIHtcbiAgICByZXR1cm4gdXNlTmV4dFBhdGhuYW1lKClcbiAgfVxuXG4gIGNvbnN0IHBhdGggPSB1c2VSb3V0ZSgpPy5wYXRoXG5cbiAgcmV0dXJuIHBhdGg/LmluY2x1ZGVzKCc/JykgPyBwYXRoLnNwbGl0KCc/JylbMF0gOiBwYXRoXG59XG4iXSwibmFtZXMiOlsiUGxhdGZvcm0iLCJ1c2VOZXh0UGF0aG5hbWUiLCJ1c2VSb3V0ZSIsInVzZVBhdGhuYW1lIiwiT1MiLCJwYXRoIiwiaW5jbHVkZXMiLCJzcGxpdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/use-pathname.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/app/navigation/use-router.js":
/*!********************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-router.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var _router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../router/parse-next-path */ \"../../node_modules/solito/build/router/parse-next-path.js\");\n/* harmony import */ var _router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../router/replace-helpers */ \"../../node_modules/solito/build/router/replace-helpers.web.js\");\n/* harmony import */ var _router_use_link_to__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../router/use-link-to */ \"../../node_modules/solito/build/router/use-link-to.web.js\");\n/* harmony import */ var _router_use_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../router/use-navigation */ \"../../node_modules/solito/build/router/use-navigation.web.js\");\n/* harmony import */ var _use_next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-next-router */ \"../../node_modules/solito/build/app/navigation/use-next-router.web.js\");\n\n\n\n\n\n\n\nfunction useRouter() {\n    const linkTo = (0,_router_use_link_to__WEBPACK_IMPORTED_MODULE_1__.useLinkTo)();\n    const navigation = (0,_router_use_navigation__WEBPACK_IMPORTED_MODULE_2__.useNavigation)();\n    const nextRouter = (0,_use_next_router__WEBPACK_IMPORTED_MODULE_3__.useNextAppDirRouter)();\n    const linking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.LinkingContext);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            push: (url, navigateOptions)=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__.Platform.OS === \"web\") {\n                    nextRouter?.push(url, navigateOptions);\n                } else {\n                    const to = (0,_router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath)(url);\n                    if (to) {\n                        linkTo(to);\n                    }\n                }\n            },\n            replace: (url, navigateOptions)=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__.Platform.OS === \"web\") {\n                    nextRouter?.replace(url, navigateOptions);\n                } else {\n                    const to = (0,_router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath)(url);\n                    if (to) {\n                        if (navigateOptions?.experimental?.nativeBehavior === \"stack-replace\") {\n                            if (linking?.options) {\n                                // custom logic to create a replace() from a URL on native\n                                // https://github.com/react-navigation/react-navigation/discussions/10517\n                                const { options } = linking;\n                                const state = options?.getStateFromPath ? options.getStateFromPath(to, options.config) : (0,_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.getStateFromPath)(to, options?.config);\n                                if (state) {\n                                    const action = (0,_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.getActionFromState)(state, options?.config);\n                                    if (action !== undefined) {\n                                        if (\"payload\" in action && action.payload && \"name\" in action.payload && action.payload.name) {\n                                            const { name, params } = action.payload;\n                                            if (navigateOptions?.experimental?.isNestedNavigator && params && \"screen\" in params && params.screen) {\n                                                navigation?.dispatch(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.StackActions.replace(params.screen, params.params));\n                                            } else {\n                                                navigation?.dispatch(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.StackActions.replace(name, params));\n                                            }\n                                        } else {\n                                            navigation?.dispatch(action);\n                                        }\n                                    } else {\n                                        navigation?.reset(state);\n                                    }\n                                }\n                            } else {\n                                // fallback in case the linking context didn't work\n                                console.warn(`[solito] replace(\"${to}\") faced an issue. You should still see your new screen, but it probably didn't replace the previous one. This may be due to a breaking change in React Navigation. \n  Please open an issue at https://github.com/nandorojo/solito and report how this happened. Thanks!`);\n                                linkTo(to);\n                            }\n                        } else {\n                            linkTo(to);\n                        }\n                    }\n                }\n            },\n            back: ()=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__.Platform.OS === \"web\") {\n                    nextRouter?.back();\n                } else {\n                    navigation?.goBack();\n                }\n            },\n            parseNextPath: _router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath\n        }), [\n        linkTo,\n        navigation\n    ]);\n} //# sourceMappingURL=use-router.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9hcHAvbmF2aWdhdGlvbi91c2Utcm91dGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUEyQztBQUNKO0FBRXFCO0FBTXZCO0FBQ2U7QUFDTztBQUNKO0FBSWpELFNBQVVXO0lBQ2QsTUFBTUMsU0FBU0osOERBQVNBO0lBQ3hCLE1BQU1LLGFBQWFKLHFFQUFhQTtJQUVoQyxNQUFNSyxhQUFhSixxRUFBbUJBO0lBRXRDLE1BQU1LLFVBQVVmLGlEQUFVQSxDQUFDTSxtRUFBY0E7SUFFekMsT0FBT0wsOENBQU9BLENBQ1osSUFBTztZQUNMZSxNQUFNLENBQ0pDLEtBQ0FDO2dCQUVBLElBQUloQixrREFBUUEsQ0FBQ2lCLEVBQUUsS0FBSyxPQUFPO29CQUN6QkwsWUFBWUUsS0FBS0MsS0FBS0M7dUJBQ2pCO29CQUNMLE1BQU1FLEtBQUtqQixzRUFBYUEsQ0FBQ2M7b0JBRXpCLElBQUlHLElBQUk7d0JBQ05SLE9BQU9ROzs7WUFHYjtZQUNBQyxTQUFTLENBQ1BKLEtBQ0FDO2dCQVdBLElBQUloQixrREFBUUEsQ0FBQ2lCLEVBQUUsS0FBSyxPQUFPO29CQUN6QkwsWUFBWU8sUUFBUUosS0FBS0M7dUJBQ3BCO29CQUNMLE1BQU1FLEtBQUtqQixzRUFBYUEsQ0FBQ2M7b0JBRXpCLElBQUlHLElBQUk7d0JBQ04sSUFDRUYsaUJBQWlCSSxjQUFjQyxtQkFBbUIsaUJBQ2xEOzRCQUNBLElBQUlSLFNBQVNTLFNBQVM7Z0NBQ3BCLDBEQUEwRDtnQ0FDMUQseUVBQXlFO2dDQUN6RSxNQUFNLEVBQUVBLE9BQU8sRUFBRSxHQUFHVDtnQ0FFcEIsTUFBTVUsUUFBUUQsU0FBU25CLG1CQUNuQm1CLFFBQVFuQixnQkFBZ0IsQ0FBQ2UsSUFBSUksUUFBUUUsTUFBTSxJQUMzQ3JCLHlFQUFnQkEsQ0FBQ2UsSUFBSUksU0FBU0U7Z0NBRWxDLElBQUlELE9BQU87b0NBQ1QsTUFBTUUsU0FBU3ZCLDJFQUFrQkEsQ0FBQ3FCLE9BQU9ELFNBQVNFO29DQUVsRCxJQUFJQyxXQUFXQyxXQUFXO3dDQUN4QixJQUNFLGFBQWFELFVBQ2JBLE9BQU9FLE9BQU8sSUFDZCxVQUFVRixPQUFPRSxPQUFPLElBQ3hCRixPQUFPRSxPQUFPLENBQUNDLElBQUksRUFDbkI7NENBQ0EsTUFBTSxFQUFFQSxJQUFJLEVBQUVDLE1BQU0sRUFBRSxHQUFHSixPQUFPRSxPQUFPOzRDQUN2QyxJQUNFWCxpQkFBaUJJLGNBQWNVLHFCQUMvQkQsVUFDQSxZQUFZQSxVQUNaQSxPQUFPRSxNQUFNLEVBQ2I7Z0RBQ0FwQixZQUFZcUIsU0FDVjNCLGlFQUFZQSxDQUFDYyxPQUFPLENBQ2xCVSxPQUFPRSxNQUFNLEVBQ2JGLE9BQU9BLE1BQTRCO21EQUdsQztnREFDTGxCLFlBQVlxQixTQUFTM0IsaUVBQVlBLENBQUNjLE9BQU8sQ0FBQ1MsTUFBTUM7OytDQUU3Qzs0Q0FDTGxCLFlBQVlxQixTQUFTUDs7MkNBRWxCO3dDQUNMZCxZQUFZc0IsTUFBTVY7OzttQ0FHakI7Z0NBQ0wsbURBQW1EO2dDQUNuRFcsUUFBUUMsSUFBSSxDQUFDLHFCQUFxQmpCLEdBQUU7b0dBQ2dEO2dDQUNwRlIsT0FBT1E7OytCQUVKOzRCQUNMUixPQUFPUTs7OztZQUlmO1lBQ0FrQixNQUFNO2dCQUNKLElBQUlwQyxrREFBUUEsQ0FBQ2lCLEVBQUUsS0FBSyxPQUFPO29CQUN6QkwsWUFBWXdCO3VCQUNQO29CQUNMekIsWUFBWTBCOztZQUVoQjtZQUNBcEMsYUFBYUEsb0VBQUFBO1lBRWY7UUFBQ1M7UUFBUUM7S0FBVztBQUV4QiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uLy4uL3NyYy9hcHAvbmF2aWdhdGlvbi91c2Utcm91dGVyLnRzPzYwNmMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ29udGV4dCwgdXNlTWVtbyB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgUGxhdGZvcm0gfSBmcm9tICdyZWFjdC1uYXRpdmUnXG5cbmltcG9ydCB7IHBhcnNlTmV4dFBhdGggfSBmcm9tICcuLi8uLi9yb3V0ZXIvcGFyc2UtbmV4dC1wYXRoJ1xuaW1wb3J0IHtcbiAgZ2V0QWN0aW9uRnJvbVN0YXRlLFxuICBnZXRTdGF0ZUZyb21QYXRoLFxuICBMaW5raW5nQ29udGV4dCxcbiAgU3RhY2tBY3Rpb25zLFxufSBmcm9tICcuLi8uLi9yb3V0ZXIvcmVwbGFjZS1oZWxwZXJzJ1xuaW1wb3J0IHsgdXNlTGlua1RvIH0gZnJvbSAnLi4vLi4vcm91dGVyL3VzZS1saW5rLXRvJ1xuaW1wb3J0IHsgdXNlTmF2aWdhdGlvbiB9IGZyb20gJy4uLy4uL3JvdXRlci91c2UtbmF2aWdhdGlvbidcbmltcG9ydCB7IHVzZU5leHRBcHBEaXJSb3V0ZXIgfSBmcm9tICcuL3VzZS1uZXh0LXJvdXRlcidcblxudHlwZSBOZXh0Um91dGVyVHlwZSA9IE5vbk51bGxhYmxlPFJldHVyblR5cGU8dHlwZW9mIHVzZU5leHRBcHBEaXJSb3V0ZXI+PlxuXG5leHBvcnQgZnVuY3Rpb24gdXNlUm91dGVyKCkge1xuICBjb25zdCBsaW5rVG8gPSB1c2VMaW5rVG8oKVxuICBjb25zdCBuYXZpZ2F0aW9uID0gdXNlTmF2aWdhdGlvbigpXG5cbiAgY29uc3QgbmV4dFJvdXRlciA9IHVzZU5leHRBcHBEaXJSb3V0ZXIoKVxuXG4gIGNvbnN0IGxpbmtpbmcgPSB1c2VDb250ZXh0KExpbmtpbmdDb250ZXh0KVxuXG4gIHJldHVybiB1c2VNZW1vKFxuICAgICgpID0+ICh7XG4gICAgICBwdXNoOiAoXG4gICAgICAgIHVybDogUGFyYW1ldGVyczxOZXh0Um91dGVyVHlwZVsncHVzaCddPlswXSxcbiAgICAgICAgbmF2aWdhdGVPcHRpb25zPzogUGFyYW1ldGVyczxOZXh0Um91dGVyVHlwZVsncHVzaCddPlsxXVxuICAgICAgKSA9PiB7XG4gICAgICAgIGlmIChQbGF0Zm9ybS5PUyA9PT0gJ3dlYicpIHtcbiAgICAgICAgICBuZXh0Um91dGVyPy5wdXNoKHVybCwgbmF2aWdhdGVPcHRpb25zKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnN0IHRvID0gcGFyc2VOZXh0UGF0aCh1cmwpXG5cbiAgICAgICAgICBpZiAodG8pIHtcbiAgICAgICAgICAgIGxpbmtUbyh0bylcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICByZXBsYWNlOiAoXG4gICAgICAgIHVybDogUGFyYW1ldGVyczxOZXh0Um91dGVyVHlwZVsncmVwbGFjZSddPlswXSxcbiAgICAgICAgbmF2aWdhdGVPcHRpb25zPzogUGFyYW1ldGVyczxOZXh0Um91dGVyVHlwZVsncmVwbGFjZSddPlsxXSAmIHtcbiAgICAgICAgICBleHBlcmltZW50YWw/OlxuICAgICAgICAgICAgfCB7XG4gICAgICAgICAgICAgICAgbmF0aXZlQmVoYXZpb3I/OiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfCB7XG4gICAgICAgICAgICAgICAgbmF0aXZlQmVoYXZpb3I6ICdzdGFjay1yZXBsYWNlJ1xuICAgICAgICAgICAgICAgIGlzTmVzdGVkTmF2aWdhdG9yOiBib29sZWFuXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgKSA9PiB7XG4gICAgICAgIGlmIChQbGF0Zm9ybS5PUyA9PT0gJ3dlYicpIHtcbiAgICAgICAgICBuZXh0Um91dGVyPy5yZXBsYWNlKHVybCwgbmF2aWdhdGVPcHRpb25zKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnN0IHRvID0gcGFyc2VOZXh0UGF0aCh1cmwpXG5cbiAgICAgICAgICBpZiAodG8pIHtcbiAgICAgICAgICAgIGlmIChcbiAgICAgICAgICAgICAgbmF2aWdhdGVPcHRpb25zPy5leHBlcmltZW50YWw/Lm5hdGl2ZUJlaGF2aW9yID09PSAnc3RhY2stcmVwbGFjZSdcbiAgICAgICAgICAgICkge1xuICAgICAgICAgICAgICBpZiAobGlua2luZz8ub3B0aW9ucykge1xuICAgICAgICAgICAgICAgIC8vIGN1c3RvbSBsb2dpYyB0byBjcmVhdGUgYSByZXBsYWNlKCkgZnJvbSBhIFVSTCBvbiBuYXRpdmVcbiAgICAgICAgICAgICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vcmVhY3QtbmF2aWdhdGlvbi9yZWFjdC1uYXZpZ2F0aW9uL2Rpc2N1c3Npb25zLzEwNTE3XG4gICAgICAgICAgICAgICAgY29uc3QgeyBvcHRpb25zIH0gPSBsaW5raW5nXG5cbiAgICAgICAgICAgICAgICBjb25zdCBzdGF0ZSA9IG9wdGlvbnM/LmdldFN0YXRlRnJvbVBhdGhcbiAgICAgICAgICAgICAgICAgID8gb3B0aW9ucy5nZXRTdGF0ZUZyb21QYXRoKHRvLCBvcHRpb25zLmNvbmZpZylcbiAgICAgICAgICAgICAgICAgIDogZ2V0U3RhdGVGcm9tUGF0aCh0bywgb3B0aW9ucz8uY29uZmlnKVxuXG4gICAgICAgICAgICAgICAgaWYgKHN0YXRlKSB7XG4gICAgICAgICAgICAgICAgICBjb25zdCBhY3Rpb24gPSBnZXRBY3Rpb25Gcm9tU3RhdGUoc3RhdGUsIG9wdGlvbnM/LmNvbmZpZylcblxuICAgICAgICAgICAgICAgICAgaWYgKGFjdGlvbiAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChcbiAgICAgICAgICAgICAgICAgICAgICAncGF5bG9hZCcgaW4gYWN0aW9uICYmXG4gICAgICAgICAgICAgICAgICAgICAgYWN0aW9uLnBheWxvYWQgJiZcbiAgICAgICAgICAgICAgICAgICAgICAnbmFtZScgaW4gYWN0aW9uLnBheWxvYWQgJiZcbiAgICAgICAgICAgICAgICAgICAgICBhY3Rpb24ucGF5bG9hZC5uYW1lXG4gICAgICAgICAgICAgICAgICAgICkge1xuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgbmFtZSwgcGFyYW1zIH0gPSBhY3Rpb24ucGF5bG9hZFxuICAgICAgICAgICAgICAgICAgICAgIGlmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIG5hdmlnYXRlT3B0aW9ucz8uZXhwZXJpbWVudGFsPy5pc05lc3RlZE5hdmlnYXRvciAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgcGFyYW1zICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAnc2NyZWVuJyBpbiBwYXJhbXMgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhcmFtcy5zY3JlZW5cbiAgICAgICAgICAgICAgICAgICAgICApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG5hdmlnYXRpb24/LmRpc3BhdGNoKFxuICAgICAgICAgICAgICAgICAgICAgICAgICBTdGFja0FjdGlvbnMucmVwbGFjZShcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYXJhbXMuc2NyZWVuLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhcmFtcy5wYXJhbXMgYXMgb2JqZWN0IHwgdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgbmF2aWdhdGlvbj8uZGlzcGF0Y2goU3RhY2tBY3Rpb25zLnJlcGxhY2UobmFtZSwgcGFyYW1zKSlcbiAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgbmF2aWdhdGlvbj8uZGlzcGF0Y2goYWN0aW9uKVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBuYXZpZ2F0aW9uPy5yZXNldChzdGF0ZSlcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgLy8gZmFsbGJhY2sgaW4gY2FzZSB0aGUgbGlua2luZyBjb250ZXh0IGRpZG4ndCB3b3JrXG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKGBbc29saXRvXSByZXBsYWNlKFwiJHt0b31cIikgZmFjZWQgYW4gaXNzdWUuIFlvdSBzaG91bGQgc3RpbGwgc2VlIHlvdXIgbmV3IHNjcmVlbiwgYnV0IGl0IHByb2JhYmx5IGRpZG4ndCByZXBsYWNlIHRoZSBwcmV2aW91cyBvbmUuIFRoaXMgbWF5IGJlIGR1ZSB0byBhIGJyZWFraW5nIGNoYW5nZSBpbiBSZWFjdCBOYXZpZ2F0aW9uLiBcbiAgUGxlYXNlIG9wZW4gYW4gaXNzdWUgYXQgaHR0cHM6Ly9naXRodWIuY29tL25hbmRvcm9qby9zb2xpdG8gYW5kIHJlcG9ydCBob3cgdGhpcyBoYXBwZW5lZC4gVGhhbmtzIWApXG4gICAgICAgICAgICAgICAgbGlua1RvKHRvKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICBsaW5rVG8odG8pXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgYmFjazogKCkgPT4ge1xuICAgICAgICBpZiAoUGxhdGZvcm0uT1MgPT09ICd3ZWInKSB7XG4gICAgICAgICAgbmV4dFJvdXRlcj8uYmFjaygpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgbmF2aWdhdGlvbj8uZ29CYWNrKClcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIHBhcnNlTmV4dFBhdGgsXG4gICAgfSksXG4gICAgW2xpbmtUbywgbmF2aWdhdGlvbl1cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZUNvbnRleHQiLCJ1c2VNZW1vIiwiUGxhdGZvcm0iLCJwYXJzZU5leHRQYXRoIiwiZ2V0QWN0aW9uRnJvbVN0YXRlIiwiZ2V0U3RhdGVGcm9tUGF0aCIsIkxpbmtpbmdDb250ZXh0IiwiU3RhY2tBY3Rpb25zIiwidXNlTGlua1RvIiwidXNlTmF2aWdhdGlvbiIsInVzZU5leHRBcHBEaXJSb3V0ZXIiLCJ1c2VSb3V0ZXIiLCJsaW5rVG8iLCJuYXZpZ2F0aW9uIiwibmV4dFJvdXRlciIsImxpbmtpbmciLCJwdXNoIiwidXJsIiwibmF2aWdhdGVPcHRpb25zIiwiT1MiLCJ0byIsInJlcGxhY2UiLCJleHBlcmltZW50YWwiLCJuYXRpdmVCZWhhdmlvciIsIm9wdGlvbnMiLCJzdGF0ZSIsImNvbmZpZyIsImFjdGlvbiIsInVuZGVmaW5lZCIsInBheWxvYWQiLCJuYW1lIiwicGFyYW1zIiwiaXNOZXN0ZWROYXZpZ2F0b3IiLCJzY3JlZW4iLCJkaXNwYXRjaCIsInJlc2V0IiwiY29uc29sZSIsIndhcm4iLCJiYWNrIiwiZ29CYWNrIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/use-router.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/app/navigation/use-search-params.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-search-params.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSearchParams: () => (/* binding */ useSearchParams)\n/* harmony export */ });\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var _params_use_route__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../params/use-route */ \"../../node_modules/solito/build/params/use-route.web.js\");\n/* harmony import */ var _use_next_search_params__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-next-search-params */ \"../../node_modules/solito/build/app/navigation/use-next-search-params.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n\n\nfunction useSearchParams() {\n    if (react_native__WEBPACK_IMPORTED_MODULE_1__.Platform.OS === \"web\") {\n        return (0,_use_next_search_params__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    }\n    const route = (0,_params_use_route__WEBPACK_IMPORTED_MODULE_3__.useRoute)();\n    if (!route) {\n        console.error(`[useParams] route is undefined. Is your ${react_native__WEBPACK_IMPORTED_MODULE_1__.Platform.OS} app properly configured for React Navigation?`);\n    }\n    const params = route?.params;\n    if (true) {\n        const nonStringParamValues = Object.entries(params || {}).map(([key, value])=>{\n            if (typeof value !== \"string\") {\n                return `${key}: ${JSON.stringify(value)}`;\n            }\n            return undefined;\n        }).filter(Boolean);\n        if (nonStringParamValues.length) {\n            throw new Error(`[useSearchParams][solito] Error found in the \"${route?.name ?? \"unknown\"}\" screen (path ${route?.path ?? \"unknown\"}). You used non-string parameters for the following params:\n      \n${nonStringParamValues.join(\"\\n\")}\n\nDue to constraints from Next.js, this is not valid in Solito.  Please refactor your code to use strings for screen parameters.\n`);\n        }\n    }\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>params && new URLSearchParams(params), [\n        params\n    ]);\n} //# sourceMappingURL=use-search-params.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/use-search-params.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/app/navigation/use-update-search-params.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-update-search-params.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _use_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-router */ \"../../node_modules/solito/build/app/navigation/use-router.js\");\n/* harmony import */ var _use_pathname__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-pathname */ \"../../node_modules/solito/build/app/navigation/use-pathname.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    const router = (0,_use_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,_use_pathname__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((params, options)=>{\n        const next = new URLSearchParams(searchParams?.toString());\n        let shouldReplace = false;\n        Object.entries(params).forEach(([key, value])=>{\n            if (next.has(key)) {\n                shouldReplace = true;\n            }\n            if (value == null) {\n                next.delete(key);\n            } else {\n                next.set(key, value);\n            }\n        });\n        const action = router[options?.webBehavior ?? (shouldReplace ? \"replace\" : \"push\")];\n        const stringifiedNext = next.toString();\n        action(`${pathname}${stringifiedNext ? `?${stringifiedNext}` : \"\"}`);\n    }, [\n        router\n    ]);\n} //# sourceMappingURL=use-update-search-params.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/use-update-search-params.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/params/use-route.web.js":
/*!***************************************************************!*\
  !*** ../../node_modules/solito/build/params/use-route.web.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRoute: () => (/* binding */ useRoute)\n/* harmony export */ });\nconst useRoute = ()=>undefined; //# sourceMappingURL=use-route.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9wYXJhbXMvdXNlLXJvdXRlLndlYi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8sTUFBTUEsV0FBVyxJQUFNQyxVQUFTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vc3JjL3BhcmFtcy91c2Utcm91dGUud2ViLnRzPzM4NDkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHVzZVJvdXRlID0gKCkgPT4gdW5kZWZpbmVkXG4iXSwibmFtZXMiOlsidXNlUm91dGUiLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/params/use-route.web.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/router/parse-next-path.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/solito/build/router/parse-next-path.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseNextPath: () => (/* binding */ parseNextPath)\n/* harmony export */ });\nconst parseNextPath = (from)=>{\n    let path = (typeof from == \"string\" ? from : from.pathname) || \"\";\n    // replace each instance of [key] with the corresponding value from query[key]\n    // this ensures we're navigating to the correct URL\n    // it currently ignores [[...param]]\n    // but I can't see why you would use this with RN + Next.js\n    if (typeof from == \"object\" && from.query && typeof from.query == \"object\") {\n        const query = {\n            ...from.query\n        };\n        // replace dynamic routes\n        // and [...param] syntax\n        for(const key in query){\n            if (path.includes(`[${key}]`)) {\n                path = path.replace(`[${key}]`, `${query[key] ?? \"\"}`);\n                delete query[key];\n            } else if (path.includes(`[...${key}]`)) {\n                const values = query[key];\n                if (Array.isArray(values)) {\n                    path = path.replace(`[...${key}]`, values.join(\"/\"));\n                    delete query[key];\n                }\n            }\n        }\n        if (Object.keys(query).length) {\n            // add query param separator\n            path += \"?\";\n            for(const key in query){\n                const value = query[key];\n                if (Array.isArray(value)) {\n                    value.forEach((item)=>{\n                        path += `${key}=${item}&`;\n                    });\n                } else if (value != null) {\n                    path += `${key}=${value}&`;\n                }\n            }\n            if (path.endsWith(\"&\") || path.endsWith(\"?\")) {\n                path = path.slice(0, -1);\n            }\n        }\n    }\n    return path;\n};\n //# sourceMappingURL=parse-next-path.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/router/parse-next-path.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/router/replace-helpers.web.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/solito/build/router/replace-helpers.web.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LinkingContext: () => (/* binding */ LinkingContext),\n/* harmony export */   StackActions: () => (/* binding */ StackActions),\n/* harmony export */   getActionFromState: () => (/* binding */ getActionFromState),\n/* harmony export */   getStateFromPath: () => (/* binding */ getStateFromPath)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst LinkingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    options: undefined\n});\nlet StackActions, getStateFromPath, getActionFromState;\n //# sourceMappingURL=replace-helpers.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvcmVwbGFjZS1oZWxwZXJzLndlYi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBcUM7QUFFckMsTUFBTUMsK0JBQWlCRCxvREFBYUEsQ0FBQztJQUNuQ0UsU0FBU0M7O0FBR1gsSUFBSUMsY0FBY0Msa0JBQWtCQztBQUV5QyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3NyYy9yb3V0ZXIvcmVwbGFjZS1oZWxwZXJzLndlYi50cz80ZjA4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCdcblxuY29uc3QgTGlua2luZ0NvbnRleHQgPSBjcmVhdGVDb250ZXh0KHtcbiAgb3B0aW9uczogdW5kZWZpbmVkLFxufSlcblxubGV0IFN0YWNrQWN0aW9ucywgZ2V0U3RhdGVGcm9tUGF0aCwgZ2V0QWN0aW9uRnJvbVN0YXRlXG5cbmV4cG9ydCB7IExpbmtpbmdDb250ZXh0LCBTdGFja0FjdGlvbnMsIGdldFN0YXRlRnJvbVBhdGgsIGdldEFjdGlvbkZyb21TdGF0ZSB9XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsIkxpbmtpbmdDb250ZXh0Iiwib3B0aW9ucyIsInVuZGVmaW5lZCIsIlN0YWNrQWN0aW9ucyIsImdldFN0YXRlRnJvbVBhdGgiLCJnZXRBY3Rpb25Gcm9tU3RhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/router/replace-helpers.web.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/router/use-link-to.web.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/solito/build/router/use-link-to.web.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLinkTo: () => (/* binding */ useLinkTo)\n/* harmony export */ });\nconst noOp = ()=>{\n    throw new Error(\"[use-link-to] is not supported on the web. Something went wrong if you called this.\");\n};\n/**\n * @deprecated imported from the wrong file. Use `use-link-to` instead.\n */ const useLinkTo = ()=>noOp; //# sourceMappingURL=use-link-to.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvdXNlLWxpbmstdG8ud2ViLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxNQUFNQSxPQUFPO0lBQ1gsTUFBTSxJQUFJQyxNQUNSO0FBRUo7QUFFQTs7SUFHTyxNQUFNQyxZQUFZLElBQU1GLEtBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9zcmMvcm91dGVyL3VzZS1saW5rLXRvLndlYi50cz8wMTkwIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG5vT3AgPSAoKSA9PiB7XG4gIHRocm93IG5ldyBFcnJvcihcbiAgICAnW3VzZS1saW5rLXRvXSBpcyBub3Qgc3VwcG9ydGVkIG9uIHRoZSB3ZWIuIFNvbWV0aGluZyB3ZW50IHdyb25nIGlmIHlvdSBjYWxsZWQgdGhpcy4nXG4gIClcbn1cblxuLyoqXG4gKiBAZGVwcmVjYXRlZCBpbXBvcnRlZCBmcm9tIHRoZSB3cm9uZyBmaWxlLiBVc2UgYHVzZS1saW5rLXRvYCBpbnN0ZWFkLlxuICovXG5leHBvcnQgY29uc3QgdXNlTGlua1RvID0gKCkgPT4gbm9PcFxuIl0sIm5hbWVzIjpbIm5vT3AiLCJFcnJvciIsInVzZUxpbmtUbyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/router/use-link-to.web.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/router/use-navigation.web.js":
/*!********************************************************************!*\
  !*** ../../node_modules/solito/build/router/use-navigation.web.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useNavigation: () => (/* binding */ useNavigation)\n/* harmony export */ });\nconst useNavigation = ()=>undefined; //# sourceMappingURL=use-navigation.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvdXNlLW5hdmlnYXRpb24ud2ViLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxNQUFNQSxnQkFBZ0IsSUFBTUMsVUFBUyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3NyYy9yb3V0ZXIvdXNlLW5hdmlnYXRpb24ud2ViLnRzP2E4NjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHVzZU5hdmlnYXRpb24gPSAoKSA9PiB1bmRlZmluZWRcbiJdLCJuYW1lcyI6WyJ1c2VOYXZpZ2F0aW9uIiwidW5kZWZpbmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/router/use-navigation.web.js\n");

/***/ }),

/***/ "../../node_modules/solito/navigation/index.js":
/*!*****************************************************!*\
  !*** ../../node_modules/solito/navigation/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _build_app_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../build/app/navigation */ \"../../node_modules/solito/build/app/navigation/index.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _build_app_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _build_app_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9uYXZpZ2F0aW9uL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9uYXZpZ2F0aW9uL2luZGV4LmpzPzY3NjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vYnVpbGQvYXBwL25hdmlnYXRpb24nXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/solito/navigation/index.js\n");

/***/ })

};
;