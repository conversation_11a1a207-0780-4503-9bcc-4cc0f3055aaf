"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/expo-constants";
exports.ids = ["vendor-chunks/expo-constants"];
exports.modules = {

/***/ "../../node_modules/expo-constants/build/Constants.js":
/*!************************************************************!*\
  !*** ../../node_modules/expo-constants/build/Constants.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppOwnership: () => (/* reexport safe */ _Constants_types__WEBPACK_IMPORTED_MODULE_0__.AppOwnership),\n/* harmony export */   ExecutionEnvironment: () => (/* reexport safe */ _Constants_types__WEBPACK_IMPORTED_MODULE_0__.ExecutionEnvironment),\n/* harmony export */   UserInterfaceIdiom: () => (/* reexport safe */ _Constants_types__WEBPACK_IMPORTED_MODULE_0__.UserInterfaceIdiom),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var expo_modules_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! expo-modules-core */ \"../../node_modules/expo-modules-core/src/index.ts\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var _Constants_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Constants.types */ \"../../node_modules/expo-constants/build/Constants.types.js\");\n/* harmony import */ var _ExponentConstants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ExponentConstants */ \"../../node_modules/expo-constants/build/ExponentConstants.web.js\");\n\n\n\n\n\nif (!_ExponentConstants__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n    console.warn(\"No native ExponentConstants module found, are you sure the expo-constants's module is linked properly?\");\n}\nconst ExpoUpdates = (0,expo_modules_core__WEBPACK_IMPORTED_MODULE_2__.requireOptionalNativeModule)(\"ExpoUpdates\");\nlet rawUpdatesManifest = null;\n// If expo-updates defines a non-empty manifest, prefer that one\nif (ExpoUpdates) {\n    let updatesManifest;\n    if (ExpoUpdates.manifest) {\n        updatesManifest = ExpoUpdates.manifest;\n    } else if (ExpoUpdates.manifestString) {\n        updatesManifest = JSON.parse(ExpoUpdates.manifestString);\n    }\n    if (updatesManifest && Object.keys(updatesManifest).length > 0) {\n        rawUpdatesManifest = updatesManifest;\n    }\n}\n// If dev-launcher defines a non-empty manifest, prefer that one\nlet rawDevLauncherManifest = null;\nif (react_native__WEBPACK_IMPORTED_MODULE_3__.NativeModules.EXDevLauncher) {\n    let devLauncherManifest;\n    if (react_native__WEBPACK_IMPORTED_MODULE_3__.NativeModules.EXDevLauncher.manifestString) {\n        devLauncherManifest = JSON.parse(react_native__WEBPACK_IMPORTED_MODULE_3__.NativeModules.EXDevLauncher.manifestString);\n    }\n    if (devLauncherManifest && Object.keys(devLauncherManifest).length > 0) {\n        rawDevLauncherManifest = devLauncherManifest;\n    }\n}\n// Fall back to ExponentConstants.manifest if we don't have one from Updates\nlet rawAppConfig = null;\nif (_ExponentConstants__WEBPACK_IMPORTED_MODULE_1__[\"default\"] && _ExponentConstants__WEBPACK_IMPORTED_MODULE_1__[\"default\"].manifest) {\n    const appConfig = _ExponentConstants__WEBPACK_IMPORTED_MODULE_1__[\"default\"].manifest;\n    // On Android we pass the manifest in JSON form so this step is necessary\n    if (typeof appConfig === \"string\") {\n        rawAppConfig = JSON.parse(appConfig);\n    } else {\n        rawAppConfig = appConfig;\n    }\n}\nlet rawManifest = rawUpdatesManifest ?? rawDevLauncherManifest ?? rawAppConfig;\nconst { name, appOwnership, ...nativeConstants } = _ExponentConstants__WEBPACK_IMPORTED_MODULE_1__[\"default\"] || {};\nconst constants = {\n    ...nativeConstants,\n    // Ensure this is null in bare workflow\n    appOwnership: appOwnership ?? null\n};\nObject.defineProperties(constants, {\n    /**\n     * Use `manifest` property by default.\n     * This property is only used for internal purposes.\n     * It behaves similarly to the original one, but suppresses warning upon no manifest available.\n     * `expo-asset` uses it to prevent users from seeing mentioned warning.\n     */ __unsafeNoWarnManifest: {\n        get () {\n            const maybeManifest = getManifest(true);\n            if (!maybeManifest || !isEmbeddedManifest(maybeManifest)) {\n                return null;\n            }\n            return maybeManifest;\n        },\n        enumerable: false\n    },\n    __unsafeNoWarnManifest2: {\n        get () {\n            const maybeManifest = getManifest(true);\n            if (!maybeManifest || !isExpoUpdatesManifest(maybeManifest)) {\n                return null;\n            }\n            return maybeManifest;\n        },\n        enumerable: false\n    },\n    manifest: {\n        get () {\n            const maybeManifest = getManifest();\n            if (!maybeManifest || !isEmbeddedManifest(maybeManifest)) {\n                return null;\n            }\n            return maybeManifest;\n        },\n        enumerable: true\n    },\n    manifest2: {\n        get () {\n            const maybeManifest = getManifest();\n            if (!maybeManifest || !isExpoUpdatesManifest(maybeManifest)) {\n                return null;\n            }\n            return maybeManifest;\n        },\n        enumerable: true\n    },\n    expoConfig: {\n        get () {\n            const maybeManifest = getManifest(true);\n            if (!maybeManifest) {\n                return null;\n            }\n            // if running an embedded update, maybeManifest is a EmbeddedManifest which doesn't have\n            // the expo config. Instead, the embedded expo-constants app.config should be used.\n            if (ExpoUpdates && ExpoUpdates.isEmbeddedLaunch) {\n                return rawAppConfig;\n            }\n            if (isExpoUpdatesManifest(maybeManifest)) {\n                return maybeManifest.extra?.expoClient ?? null;\n            } else if (isEmbeddedManifest(maybeManifest)) {\n                return maybeManifest;\n            }\n            return null;\n        },\n        enumerable: true\n    },\n    expoGoConfig: {\n        get () {\n            const maybeManifest = getManifest(true);\n            if (!maybeManifest) {\n                return null;\n            }\n            if (isExpoUpdatesManifest(maybeManifest)) {\n                return maybeManifest.extra?.expoGo ?? null;\n            } else if (isEmbeddedManifest(maybeManifest)) {\n                return maybeManifest;\n            }\n            return null;\n        },\n        enumerable: true\n    },\n    easConfig: {\n        get () {\n            const maybeManifest = getManifest(true);\n            if (!maybeManifest) {\n                return null;\n            }\n            if (isExpoUpdatesManifest(maybeManifest)) {\n                return maybeManifest.extra?.eas ?? null;\n            } else if (isEmbeddedManifest(maybeManifest)) {\n                return maybeManifest;\n            }\n            return null;\n        },\n        enumerable: true\n    },\n    __rawManifest_TEST: {\n        get () {\n            return rawManifest;\n        },\n        set (value) {\n            rawManifest = value;\n        },\n        enumerable: false\n    }\n});\nfunction isEmbeddedManifest(manifest) {\n    return !isExpoUpdatesManifest(manifest);\n}\nfunction isExpoUpdatesManifest(manifest) {\n    return \"metadata\" in manifest;\n}\nfunction getManifest(suppressWarning = false) {\n    if (!rawManifest) {\n        const invalidManifestType = rawManifest === null ? \"null\" : \"undefined\";\n        if (nativeConstants.executionEnvironment === _Constants_types__WEBPACK_IMPORTED_MODULE_0__.ExecutionEnvironment.Bare && react_native__WEBPACK_IMPORTED_MODULE_3__.Platform.OS !== \"web\") {\n            if (!suppressWarning) {\n                console.warn(`Constants.manifest is ${invalidManifestType} because the embedded app.config could not be read. Ensure that you have installed the expo-constants build scripts if you need to read from Constants.manifest.`);\n            }\n        } else if (nativeConstants.executionEnvironment === _Constants_types__WEBPACK_IMPORTED_MODULE_0__.ExecutionEnvironment.StoreClient || nativeConstants.executionEnvironment === _Constants_types__WEBPACK_IMPORTED_MODULE_0__.ExecutionEnvironment.Standalone) {\n            // If we somehow get here, this is a truly exceptional state to be in.\n            // Constants.manifest should *always* be defined in those contexts.\n            throw new expo_modules_core__WEBPACK_IMPORTED_MODULE_2__.CodedError(\"ERR_CONSTANTS_MANIFEST_UNAVAILABLE\", `Constants.manifest is ${invalidManifestType}, must be an object.`);\n        }\n    }\n    return rawManifest;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (constants); //# sourceMappingURL=Constants.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-constants/build/Constants.js\n");

/***/ }),

/***/ "../../node_modules/expo-constants/build/Constants.types.js":
/*!******************************************************************!*\
  !*** ../../node_modules/expo-constants/build/Constants.types.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppOwnership: () => (/* binding */ AppOwnership),\n/* harmony export */   ExecutionEnvironment: () => (/* binding */ ExecutionEnvironment),\n/* harmony export */   UserInterfaceIdiom: () => (/* binding */ UserInterfaceIdiom)\n/* harmony export */ });\nvar AppOwnership;\n(function(AppOwnership) {\n    /**\n     * The experience is running inside the Expo Go app.\n     * @deprecated Use [`Constants.executionEnvironment`](#executionenvironment) instead.\n     */ AppOwnership[\"Expo\"] = \"expo\";\n})(AppOwnership || (AppOwnership = {}));\n// @docsMissing\nvar ExecutionEnvironment;\n(function(ExecutionEnvironment) {\n    ExecutionEnvironment[\"Bare\"] = \"bare\";\n    ExecutionEnvironment[\"Standalone\"] = \"standalone\";\n    ExecutionEnvironment[\"StoreClient\"] = \"storeClient\";\n})(ExecutionEnvironment || (ExecutionEnvironment = {}));\n// @needsAudit\n/**\n * Current supported values are `handset`, `tablet`, `desktop` and `tv`. CarPlay will show up\n * as `unsupported`.\n */ var UserInterfaceIdiom;\n(function(UserInterfaceIdiom) {\n    UserInterfaceIdiom[\"Handset\"] = \"handset\";\n    UserInterfaceIdiom[\"Tablet\"] = \"tablet\";\n    UserInterfaceIdiom[\"Desktop\"] = \"desktop\";\n    UserInterfaceIdiom[\"TV\"] = \"tv\";\n    UserInterfaceIdiom[\"Unsupported\"] = \"unsupported\";\n})(UserInterfaceIdiom || (UserInterfaceIdiom = {})); //# sourceMappingURL=Constants.types.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tY29uc3RhbnRzL2J1aWxkL0NvbnN0YW50cy50eXBlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFhQSxJQUFZQSxhQU1YO0FBTkQsVUFBWUEsWUFBWTtJQUN0Qjs7O1FBSUFBLFlBQUE7QUFDRixHQU5ZQSxnQkFBQUEsQ0FBQUEsZUFBWTtBQVF4QixlQUFlO0FBQ2YsSUFBWUMscUJBSVg7QUFKRCxVQUFZQSxvQkFBb0I7SUFDOUJBLG9CQUFBO0lBQ0FBLG9CQUFBO0lBQ0FBLG9CQUFBO0FBQ0YsR0FKWUEsd0JBQUFBLENBQUFBLHVCQUFvQjtBQU1oQyxjQUFjO0FBQ2Q7OztJQUlBLElBQVlDLG1CQU1YO0FBTkQsVUFBWUEsa0JBQWtCO0lBQzVCQSxrQkFBQTtJQUNBQSxrQkFBQTtJQUNBQSxrQkFBQTtJQUNBQSxrQkFBQTtJQUNBQSxrQkFBQTtBQUNGLEdBTllBLHNCQUFBQSxDQUFBQSxxQkFBa0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi9zcmMvQ29uc3RhbnRzLnR5cGVzLnRzPzkxMTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBFeHBvQ29uZmlnIH0gZnJvbSAnZXhwby9jb25maWcnO1xuaW1wb3J0IHR5cGUge1xuICBFQVNDb25maWcgYXMgTWFuaWZlc3RzRUFTQ29uZmlnLFxuICBFeHBvR29Db25maWcgYXMgTWFuaWZlc3RzRXhwb0dvQ29uZmlnLFxuICBFeHBvVXBkYXRlc01hbmlmZXN0LFxuICBFbWJlZGRlZE1hbmlmZXN0LFxuICBNYW5pZmVzdEFzc2V0IGFzIE1hbmlmZXN0QXNzZXRGb3JSZUV4cG9ydCxcbiAgTWFuaWZlc3RFeHRyYSBhcyBNYW5pZmVzdEV4dHJhRm9yUmVFeHBvcnQsXG4gIENsaWVudFNjb3BpbmdDb25maWcgYXMgQ2xpZW50U2NvcGluZ0NvbmZpZ0ZvclJlRXhwb3J0LFxuICBFeHBvR29QYWNrYWdlck9wdHMgYXMgRXhwb0dvUGFja2FnZXJPcHRzRm9yUmVFeHBvcnQsXG4gIC8vIEB0cy1pZ25vcmUgLS0gb3B0aW9uYWwgaW50ZXJmYWNlLCB3aWxsIGdyYWNlZnVsbHkgZGVncmFkZSB0byBgYW55YCBub3QgaW5zdGFsbGVkXG59IGZyb20gJ2V4cG8tbWFuaWZlc3RzJztcblxuZXhwb3J0IGVudW0gQXBwT3duZXJzaGlwIHtcbiAgLyoqXG4gICAqIFRoZSBleHBlcmllbmNlIGlzIHJ1bm5pbmcgaW5zaWRlIHRoZSBFeHBvIEdvIGFwcC5cbiAgICogQGRlcHJlY2F0ZWQgVXNlIFtgQ29uc3RhbnRzLmV4ZWN1dGlvbkVudmlyb25tZW50YF0oI2V4ZWN1dGlvbmVudmlyb25tZW50KSBpbnN0ZWFkLlxuICAgKi9cbiAgRXhwbyA9ICdleHBvJyxcbn1cblxuLy8gQGRvY3NNaXNzaW5nXG5leHBvcnQgZW51bSBFeGVjdXRpb25FbnZpcm9ubWVudCB7XG4gIEJhcmUgPSAnYmFyZScsXG4gIFN0YW5kYWxvbmUgPSAnc3RhbmRhbG9uZScsXG4gIFN0b3JlQ2xpZW50ID0gJ3N0b3JlQ2xpZW50Jyxcbn1cblxuLy8gQG5lZWRzQXVkaXRcbi8qKlxuICogQ3VycmVudCBzdXBwb3J0ZWQgdmFsdWVzIGFyZSBgaGFuZHNldGAsIGB0YWJsZXRgLCBgZGVza3RvcGAgYW5kIGB0dmAuIENhclBsYXkgd2lsbCBzaG93IHVwXG4gKiBhcyBgdW5zdXBwb3J0ZWRgLlxuICovXG5leHBvcnQgZW51bSBVc2VySW50ZXJmYWNlSWRpb20ge1xuICBIYW5kc2V0ID0gJ2hhbmRzZXQnLFxuICBUYWJsZXQgPSAndGFibGV0JyxcbiAgRGVza3RvcCA9ICdkZXNrdG9wJyxcbiAgVFYgPSAndHYnLFxuICBVbnN1cHBvcnRlZCA9ICd1bnN1cHBvcnRlZCcsXG59XG5cbi8vIEBuZWVkc0F1ZGl0XG4vKipcbiAqIEBwbGF0Zm9ybSBpb3NcbiAqL1xuZXhwb3J0IHR5cGUgSU9TTWFuaWZlc3QgPSB7XG4gIC8qKlxuICAgKiBUaGUgYnVpbGQgbnVtYmVyIHNwZWNpZmllZCBpbiB0aGUgZW1iZWRkZWQgKipJbmZvLnBsaXN0KiogdmFsdWUgZm9yIGBDRkJ1bmRsZVZlcnNpb25gIGluIHRoaXMgYXBwLlxuICAgKiBJbiBhIHN0YW5kYWxvbmUgYXBwLCB5b3UgY2FuIHNldCB0aGlzIHdpdGggdGhlIGBpb3MuYnVpbGROdW1iZXJgIHZhbHVlIGluICoqYXBwLmpzb24qKi4gVGhpc1xuICAgKiBtYXkgZGlmZmVyIGZyb20gdGhlIHZhbHVlIGluIGBDb25zdGFudHMuZXhwb0NvbmZpZy5pb3MuYnVpbGROdW1iZXJgIGJlY2F1c2UgdGhlIG1hbmlmZXN0XG4gICAqIGNhbiBiZSB1cGRhdGVkLCB3aGVyZWFzIHRoaXMgdmFsdWUgd2lsbCBuZXZlciBjaGFuZ2UgZm9yIGEgZ2l2ZW4gbmF0aXZlIGJpbmFyeS5cbiAgICogVGhlIHZhbHVlIGlzIHNldCB0byBgbnVsbGAgaW4gY2FzZSB5b3UgcnVuIHlvdXIgYXBwIGluIEV4cG8gR28uXG4gICAqL1xuICBidWlsZE51bWJlcjogc3RyaW5nIHwgbnVsbDtcbiAgLyoqXG4gICAqIFRoZSBBcHBsZSBpbnRlcm5hbCBtb2RlbCBpZGVudGlmaWVyIGZvciB0aGlzIGRldmljZS5cbiAgICogQGV4YW1wbGVcbiAgICogYGlQaG9uZTEsMWBcbiAgICogQGRlcHJlY2F0ZWQgVXNlIGBleHBvLWRldmljZWAncyBbYERldmljZS5tb2RlbElkYF0oLi9kZXZpY2UvI2RldmljZW1vZGVsaWQpLlxuICAgKi9cbiAgcGxhdGZvcm06IHN0cmluZztcbiAgLyoqXG4gICAqIFRoZSBodW1hbi1yZWFkYWJsZSBtb2RlbCBuYW1lIG9mIHRoaXMgZGV2aWNlLiBGb3IgZXhhbXBsZSwgYFwiaVBob25lIDcgUGx1c1wiYCBpZiBpdCBjYW4gYmUgZGV0ZXJtaW5lZCxcbiAgICogb3RoZXJ3aXNlIHdpbGwgYmUgYG51bGxgLlxuICAgKiBAZGVwcmVjYXRlZCBNb3ZlZCB0byBgZXhwby1kZXZpY2VgIGFzIFtgRGV2aWNlLm1vZGVsTmFtZWBdKC4vZGV2aWNlLyNkZXZpY2Vtb2RlbG5hbWUpLlxuICAgKi9cbiAgbW9kZWw6IHN0cmluZyB8IG51bGw7XG4gIC8qKlxuICAgKiBUaGUgdXNlciBpbnRlcmZhY2UgaWRpb20gb2YgdGhlIGN1cnJlbnQgZGV2aWNlLCBzdWNoIGFzIHdoZXRoZXIgdGhlIGFwcCBpcyBydW5uaW5nIG9uIGFuIGlQaG9uZSwgaVBhZCwgTWFjIG9yIEFwcGxlIFRWLlxuICAgKiBAZGVwcmVjYXRlZCBVc2UgYGV4cG8tZGV2aWNlYCdzIFtgRGV2aWNlLmdldERldmljZVR5cGVBc3luYygpYF0oLi9kZXZpY2UvI2RldmljZWdldGRldmljZXR5cGVhc3luYykuXG4gICAqL1xuICB1c2VySW50ZXJmYWNlSWRpb206IFVzZXJJbnRlcmZhY2VJZGlvbTtcbiAgLyoqXG4gICAqIFRoZSB2ZXJzaW9uIG9mIGlPUyBydW5uaW5nIG9uIHRoaXMgZGV2aWNlLlxuICAgKiBAZXhhbXBsZVxuICAgKiBgMTAuM2BcbiAgICogQGRlcHJlY2F0ZWQgVXNlIGBleHBvLWRldmljZWAncyBbYERldmljZS5vc1ZlcnNpb25gXSguL2RldmljZS8jZGV2aWNlb3N2ZXJzaW9uKS5cbiAgICovXG4gIHN5c3RlbVZlcnNpb246IHN0cmluZztcbn0gJiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xuXG4vLyBAbmVlZHNBdWRpdFxuLyoqXG4gKiBAcGxhdGZvcm0gYW5kcm9pZFxuICovXG5leHBvcnQgdHlwZSBBbmRyb2lkTWFuaWZlc3QgPSB7XG4gIC8qKlxuICAgKiBUaGUgdmVyc2lvbiBjb2RlIHNldCBieSBgYW5kcm9pZC52ZXJzaW9uQ29kZWAgaW4gYXBwLmpzb24uXG4gICAqIFRoZSB2YWx1ZSBpcyBzZXQgdG8gYG51bGxgIGluIGNhc2UgeW91IHJ1biB5b3VyIGFwcCBpbiBFeHBvIEdvLlxuICAgKiBAZGVwcmVjYXRlZCBVc2UgYGV4cG8tYXBwbGljYXRpb25gJ3MgW2BBcHBsaWNhdGlvbi5uYXRpdmVCdWlsZFZlcnNpb25gXSguL2FwcGxpY2F0aW9uLyNhcHBsaWNhdGlvbm5hdGl2ZWJ1aWxkdmVyc2lvbikuXG4gICAqL1xuICB2ZXJzaW9uQ29kZTogbnVtYmVyO1xufSAmIFJlY29yZDxzdHJpbmcsIGFueT47XG5cbi8qKlxuICogQHBsYXRmb3JtIHdlYlxuICovXG5leHBvcnQgdHlwZSBXZWJNYW5pZmVzdCA9IFJlY29yZDxzdHJpbmcsIGFueT47XG5cbi8vIHR5cGUgcmUtZXhwb3J0cyB0byBwcmV2ZW50IGJyZWFraW5nIGNoYW5nZVxuXG5leHBvcnQgdHlwZSBNYW5pZmVzdEFzc2V0ID0gTWFuaWZlc3RBc3NldEZvclJlRXhwb3J0O1xuZXhwb3J0IHR5cGUgTWFuaWZlc3QgPSBFeHBvVXBkYXRlc01hbmlmZXN0O1xuZXhwb3J0IHR5cGUgTWFuaWZlc3RFeHRyYSA9IE1hbmlmZXN0RXh0cmFGb3JSZUV4cG9ydDtcbmV4cG9ydCB0eXBlIEVBU0NvbmZpZyA9IE1hbmlmZXN0c0VBU0NvbmZpZztcbmV4cG9ydCB0eXBlIENsaWVudFNjb3BpbmdDb25maWcgPSBDbGllbnRTY29waW5nQ29uZmlnRm9yUmVFeHBvcnQ7XG5leHBvcnQgdHlwZSBFeHBvR29Db25maWcgPSBNYW5pZmVzdHNFeHBvR29Db25maWc7XG5leHBvcnQgdHlwZSBFeHBvR29QYWNrYWdlck9wdHMgPSBFeHBvR29QYWNrYWdlck9wdHNGb3JSZUV4cG9ydDtcblxuLy8gQG5lZWRzQXVkaXQgQGRvY3NNaXNzaW5nXG5leHBvcnQgdHlwZSBQbGF0Zm9ybU1hbmlmZXN0ID0ge1xuICBpb3M/OiBJT1NNYW5pZmVzdDtcbiAgYW5kcm9pZD86IEFuZHJvaWRNYW5pZmVzdDtcbiAgd2ViPzogV2ViTWFuaWZlc3Q7XG4gIGRldGFjaD86IHtcbiAgICBzY2hlbWU/OiBzdHJpbmc7XG4gICAgW2tleTogc3RyaW5nXTogYW55O1xuICB9O1xuICBzY2hlbWU/OiBzdHJpbmc7XG4gIGhvc3RVcmk/OiBzdHJpbmc7XG4gIGRldmVsb3Blcj86IHN0cmluZztcbn0gJiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xuXG4vLyBAbmVlZHNBdWRpdCBAZG9jc01pc3NpbmdcbmV4cG9ydCB0eXBlIE5hdGl2ZUNvbnN0YW50cyA9IHtcbiAgLyoqXG4gICAqIEBoaWRkZW5cbiAgICovXG4gIG5hbWU6ICdFeHBvbmVudENvbnN0YW50cyc7XG4gIC8qKlxuICAgKiBSZXR1cm5zIGBleHBvYCB3aGVuIHJ1bm5pbmcgaW4gRXhwbyBHbywgb3RoZXJ3aXNlIGBudWxsYC5cbiAgICogQGRlcHJlY2F0ZWQgVXNlIFtgQ29uc3RhbnRzLmV4ZWN1dGlvbkVudmlyb25tZW50YF0oI2V4ZWN1dGlvbmVudmlyb25tZW50KSBpbnN0ZWFkLlxuICAgKi9cbiAgYXBwT3duZXJzaGlwOiBBcHBPd25lcnNoaXAgfCBudWxsO1xuICAvKipcbiAgICogUmV0dXJucyBgdHJ1ZWAgd2hlbiB0aGUgYXBwIGlzIHJ1bm5pbmcgaW4gZGVidWcgbW9kZSAoYF9fREVWX19gKS4gT3RoZXJ3aXNlLCByZXR1cm5zIGBmYWxzZWAuXG4gICAqL1xuICBkZWJ1Z01vZGU6IGJvb2xlYW47XG4gIC8qKlxuICAgKiBBIGh1bWFuLXJlYWRhYmxlIG5hbWUgZm9yIHRoZSBkZXZpY2UgdHlwZS5cbiAgICovXG4gIGRldmljZU5hbWU/OiBzdHJpbmc7XG4gIC8qKlxuICAgKiBUaGUgW2RldmljZSB5ZWFyIGNsYXNzXShodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svZGV2aWNlLXllYXItY2xhc3MpIG9mIHRoaXMgZGV2aWNlLlxuICAgKiBAZGVwcmVjYXRlZCBNb3ZlZCB0byBgZXhwby1kZXZpY2VgIGFzIFtgRGV2aWNlLmRldmljZVllYXJDbGFzc2BdKC4vZGV2aWNlLyNkZXZpY2V5ZWFyY2xhc3MpLlxuICAgKi9cbiAgZGV2aWNlWWVhckNsYXNzOiBudW1iZXIgfCBudWxsO1xuICAvKipcbiAgICogUmV0dXJucyB0aGUgY3VycmVudCBleGVjdXRpb24gZW52aXJvbm1lbnQuXG4gICAqL1xuICBleGVjdXRpb25FbnZpcm9ubWVudDogRXhlY3V0aW9uRW52aXJvbm1lbnQ7XG4gIGV4cGVyaWVuY2VVcmw6IHN0cmluZztcbiAgLyoqXG4gICAqIE51bGxhYmxlIG9ubHkgb24gdGhlIHdlYi5cbiAgICovXG4gIGV4cG9SdW50aW1lVmVyc2lvbjogc3RyaW5nIHwgbnVsbDtcbiAgLyoqXG4gICAqIFRoZSB2ZXJzaW9uIHN0cmluZyBvZiB0aGUgRXhwbyBHbyBhcHAgY3VycmVudGx5IHJ1bm5pbmcuXG4gICAqIFJldHVybnMgYG51bGxgIGluIGJhcmUgd29ya2Zsb3cgYW5kIHdlYi5cbiAgICovXG4gIGV4cG9WZXJzaW9uOiBzdHJpbmcgfCBudWxsO1xuICBpc0RldGFjaGVkPzogYm9vbGVhbjtcbiAgaW50ZW50VXJpPzogc3RyaW5nO1xuICAvKipcbiAgICogUmV0dXJucyBgdHJ1ZWAgaWYgdGhlIGFwcCBpcyBydW5uaW5nIGluIGhlYWRsZXNzIG1vZGUuIE90aGVyd2lzZSwgcmV0dXJucyBgZmFsc2VgLlxuICAgKi9cbiAgaXNIZWFkbGVzczogYm9vbGVhbjtcbiAgbGlua2luZ1VyaTogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBAaGlkZGVuXG4gICAqIE1hbmlmZXN0IGVtYmVkZGVkIGluIHRoZSBidWlsZC4gUmV0dXJucyBgbnVsbGAgd2hlbiBgbWFuaWZlc3QyYCBpcyBub24tbnVsbC5cbiAgICogQGRlcHJlY2F0ZWQgVXNlIGBDb25zdGFudHMuZXhwb0NvbmZpZ2AgaW5zdGVhZCwgd2hpY2ggYmVoYXZlcyBtb3JlIGNvbnNpc3RlbnRseSBhY3Jvc3MgRUFTIEJ1aWxkXG4gICAqIGFuZCBFQVMgVXBkYXRlLlxuICAgKi9cbiAgbWFuaWZlc3Q6IEVtYmVkZGVkTWFuaWZlc3QgfCBudWxsO1xuICAvKipcbiAgICogTWFuaWZlc3QgZm9yIEV4cG8gYXBwcyB1c2luZyBtb2Rlcm4gRXhwbyBVcGRhdGVzIGZyb20gYSByZW1vdGUgc291cmNlLCBzdWNoIGFzIGFwcHMgdGhhdFxuICAgKiB1c2UgRUFTIFVwZGF0ZS4gYENvbnN0YW50cy5leHBvQ29uZmlnYCBzaG91bGQgYmUgdXNlZCBmb3IgYWNjZXNzaW5nIHRoZSBFeHBvIGNvbmZpZyBvYmplY3QuXG4gICAqL1xuICBtYW5pZmVzdDI6IEV4cG9VcGRhdGVzTWFuaWZlc3QgfCBudWxsO1xuICAvKipcbiAgICogVGhlIHN0YW5kYXJkIEV4cG8gY29uZmlnIG9iamVjdCBkZWZpbmVkIGluICoqYXBwLmpzb24qKiBhbmQgKiphcHAuY29uZmlnLmpzKiogZmlsZXMuIEZvciBib3RoXG4gICAqIGNsYXNzaWMgYW5kIG1vZGVybiBtYW5pZmVzdHMsIHdoZXRoZXIgdGhleSBhcmUgZW1iZWRkZWQgb3IgcmVtb3RlLlxuICAgKi9cbiAgZXhwb0NvbmZpZzpcbiAgICB8IChFeHBvQ29uZmlnICYge1xuICAgICAgICAvKipcbiAgICAgICAgICogT25seSBwcmVzZW50IGR1cmluZyBkZXZlbG9wbWVudCB1c2luZyBAZXhwby9jbGkuXG4gICAgICAgICAqL1xuICAgICAgICBob3N0VXJpPzogc3RyaW5nO1xuICAgICAgfSlcbiAgICB8IG51bGw7XG4gIC8qKlxuICAgKiBUaGUgc3RhbmRhcmQgRXhwbyBHbyBjb25maWcgb2JqZWN0IHBvcHVsYXRlZCB3aGVuIHJ1bm5pbmcgaW4gRXhwbyBHby5cbiAgICovXG4gIGV4cG9Hb0NvbmZpZzogTWFuaWZlc3RzRXhwb0dvQ29uZmlnIHwgbnVsbDtcbiAgLyoqXG4gICAqIFRoZSBzdGFuZGFyZCBFQVMgY29uZmlnIG9iamVjdCBwb3B1bGF0ZWQgd2hlbiB1c2luZyBFQVMuXG4gICAqL1xuICBlYXNDb25maWc6IE1hbmlmZXN0c0VBU0NvbmZpZyB8IG51bGw7XG4gIC8qKlxuICAgKiBBIHN0cmluZyB0aGF0IGlzIHVuaXF1ZSB0byB0aGUgY3VycmVudCBzZXNzaW9uIG9mIHlvdXIgYXBwLiBJdCBpcyBkaWZmZXJlbnQgYWNyb3NzIGFwcHMgYW5kXG4gICAqIGFjcm9zcyBtdWx0aXBsZSBsYXVuY2hlcyBvZiB0aGUgc2FtZSBhcHAuXG4gICAqL1xuICBzZXNzaW9uSWQ6IHN0cmluZztcbiAgLyoqXG4gICAqIFRoZSBkZWZhdWx0IHN0YXR1cyBiYXIgaGVpZ2h0IGZvciB0aGUgZGV2aWNlLiBEb2VzIG5vdCBmYWN0b3IgaW4gY2hhbmdlcyB3aGVuIGxvY2F0aW9uIHRyYWNraW5nXG4gICAqIGlzIGluIHVzZSBvciBhIHBob25lIGNhbGwgaXMgYWN0aXZlLlxuICAgKi9cbiAgc3RhdHVzQmFySGVpZ2h0OiBudW1iZXI7XG4gIC8qKlxuICAgKiBBIGxpc3Qgb2YgdGhlIHN5c3RlbSBmb250IG5hbWVzIGF2YWlsYWJsZSBvbiB0aGUgY3VycmVudCBkZXZpY2UuXG4gICAqL1xuICBzeXN0ZW1Gb250czogc3RyaW5nW107XG4gIHN5c3RlbVZlcnNpb24/OiBudW1iZXI7XG4gIC8qKlxuICAgKiBAaGlkZGVuXG4gICAqL1xuICBzdXBwb3J0ZWRFeHBvU2Rrcz86IHN0cmluZ1tdO1xuICAvKipcbiAgICogUmV0dXJucyB0aGUgc3BlY2lmaWMgcGxhdGZvcm0gbWFuaWZlc3Qgb2JqZWN0LlxuICAgKlxuICAgKiA+ICoqTm90ZSoqOiBUaGlzIGlzIGRpc3RpbmN0IGZyb20gdGhlIGBtYW5pZmVzdGAgYW5kIGBtYW5pZmVzdDJgLlxuICAgKi9cbiAgcGxhdGZvcm0/OiBQbGF0Zm9ybU1hbmlmZXN0O1xuICAvKipcbiAgICogR2V0cyB0aGUgdXNlciBhZ2VudCBzdHJpbmcgd2hpY2ggd291bGQgYmUgaW5jbHVkZWQgaW4gcmVxdWVzdHMgc2VudCBieSBhIHdlYiB2aWV3IHJ1bm5pbmcgb25cbiAgICogdGhpcyBkZXZpY2UuIFRoaXMgaXMgcHJvYmFibHkgbm90IHRoZSBzYW1lIHVzZXIgYWdlbnQgeW91IG1pZ2h0IGJlIHByb3ZpZGluZyBpbiB5b3VyIEpTIGBmZXRjaGBcbiAgICogcmVxdWVzdHMuXG4gICAqL1xuICBnZXRXZWJWaWV3VXNlckFnZW50QXN5bmM6ICgpID0+IFByb21pc2U8c3RyaW5nIHwgbnVsbD47XG59ICYgUmVjb3JkPHN0cmluZywgYW55PjtcblxuLyoqXG4gKiBAaGlkZGVuXG4gKi9cbmV4cG9ydCB0eXBlIENvbnN0YW50cyA9IE5hdGl2ZUNvbnN0YW50cyAmIHtcbiAgLyoqXG4gICAqID4gKipXYXJuaW5nKio6IERvIG5vdCB1c2UgdGhpcyBwcm9wZXJ0eS4gVXNlIGBtYW5pZmVzdGAgYnkgZGVmYXVsdC5cbiAgICpcbiAgICogSW4gY2VydGFpbiBjYXNlcyBhY2Nlc3NpbmcgbWFuaWZlc3QgdmlhIHRoaXMgcHJvcGVydHlcbiAgICogc3VwcHJlc3NlcyBpbXBvcnRhbnQgd2FybmluZyBhYm91dCBtaXNzaW5nIG1hbmlmZXN0LlxuICAgKi9cbiAgX191bnNhZmVOb1dhcm5NYW5pZmVzdD86IEVtYmVkZGVkTWFuaWZlc3Q7XG4gIC8qKlxuICAgKiA+ICoqV2FybmluZyoqOiBEbyBub3QgdXNlIHRoaXMgcHJvcGVydHkuIFVzZSBgbWFuaWZlc3QyYCBieSBkZWZhdWx0LlxuICAgKlxuICAgKiBJbiBjZXJ0YWluIGNhc2VzIGFjY2Vzc2luZyBtYW5pZmVzdCB2aWEgdGhpcyBwcm9wZXJ0eVxuICAgKiBzdXBwcmVzc2VzIGltcG9ydGFudCB3YXJuaW5nIGFib3V0IG1pc3NpbmcgbWFuaWZlc3QuXG4gICAqL1xuICBfX3Vuc2FmZU5vV2Fybk1hbmlmZXN0Mj86IEV4cG9VcGRhdGVzTWFuaWZlc3Q7XG59O1xuIl0sIm5hbWVzIjpbIkFwcE93bmVyc2hpcCIsIkV4ZWN1dGlvbkVudmlyb25tZW50IiwiVXNlckludGVyZmFjZUlkaW9tIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/expo-constants/build/Constants.types.js\n");

/***/ }),

/***/ "../../node_modules/expo-constants/build/ExponentConstants.web.js":
/*!************************************************************************!*\
  !*** ../../node_modules/expo-constants/build/ExponentConstants.web.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Constants_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Constants.types */ \"../../node_modules/expo-constants/build/Constants.types.js\");\n\nconst _sessionId = (Date.now() + \"-\" + Math.floor(Math.random() * 1000000000)).toString();\nfunction getBrowserName() {\n    if (typeof navigator !== \"undefined\" && typeof navigator.userAgent === \"string\") {\n        const agent = navigator.userAgent.toLowerCase();\n        if (agent.includes(\"edge\")) {\n            return \"Edge\";\n        } else if (agent.includes(\"edg\")) {\n            return \"Chromium Edge\";\n        } else if (agent.includes(\"opr\") && \"opr\" in window && !!window[\"opr\"]) {\n            return \"Opera\";\n        } else if (agent.includes(\"chrome\") && \"chrome\" in window && !!window[\"chrome\"]) {\n            return \"Chrome\";\n        } else if (agent.includes(\"trident\")) {\n            return \"IE\";\n        } else if (agent.includes(\"firefox\")) {\n            return \"Firefox\";\n        } else if (agent.includes(\"safari\")) {\n            return \"Safari\";\n        }\n    }\n    return undefined;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    get appOwnership () {\n        return null;\n    },\n    get executionEnvironment () {\n        return _Constants_types__WEBPACK_IMPORTED_MODULE_0__.ExecutionEnvironment.Bare;\n    },\n    get sessionId () {\n        return _sessionId;\n    },\n    get isHeadless () {\n        if (typeof navigator === \"undefined\") return true;\n        return /\\bHeadlessChrome\\//.test(navigator.userAgent);\n    },\n    get expoVersion () {\n        return this.manifest.sdkVersion || null;\n    },\n    get linkingUri () {\n        if (typeof location !== \"undefined\") {\n            // On native this is `exp://`\n            // On web we should use the protocol and hostname (location.origin)\n            return location.origin;\n        } else {\n            return \"\";\n        }\n    },\n    get expoRuntimeVersion () {\n        return this.expoVersion;\n    },\n    get deviceName () {\n        return getBrowserName();\n    },\n    get systemFonts () {\n        // TODO: Bacon: Maybe possible.\n        return [];\n    },\n    get statusBarHeight () {\n        return 0;\n    },\n    get deviceYearClass () {\n        // TODO: Bacon: The android version isn't very accurate either, maybe we could try and guess this value.\n        return null;\n    },\n    get manifest () {\n        // This is defined by @expo/webpack-config or babel-preset-expo.\n        // If your site is bundled with a different config then you may not have access to the app.json automatically.\n        return process.env.APP_MANIFEST || {};\n    },\n    get manifest2 () {\n        return null;\n    },\n    get experienceUrl () {\n        if (typeof location !== \"undefined\") {\n            return location.origin;\n        } else {\n            return \"\";\n        }\n    },\n    get debugMode () {\n        return true;\n    },\n    async getWebViewUserAgentAsync () {\n        if (typeof navigator !== \"undefined\") {\n            return navigator.userAgent;\n        } else {\n            return null;\n        }\n    }\n}); //# sourceMappingURL=ExponentConstants.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/expo-constants/build/ExponentConstants.web.js\n");

/***/ })

};
;