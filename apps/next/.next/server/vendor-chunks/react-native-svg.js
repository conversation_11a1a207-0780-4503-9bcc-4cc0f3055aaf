"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-native-svg";
exports.ids = ["vendor-chunks/react-native-svg"];
exports.modules = {

/***/ "../../node_modules/react-native-svg/lib/commonjs/ReactNativeSVG.web.js":
/*!******************************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/ReactNativeSVG.web.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nvar _exportNames = {\n  camelCase: true,\n  parse: true,\n  SvgAst: true,\n  SvgFromUri: true,\n  SvgFromXml: true,\n  SvgUri: true,\n  SvgXml: true,\n  fetchText: true,\n  inlineStyles: true,\n  loadLocalRawResource: true,\n  LocalSvg: true,\n  SvgCss: true,\n  SvgCssUri: true,\n  SvgWithCss: true,\n  SvgWithCssUri: true,\n  WithLocalSvg: true\n};\nObject.defineProperty(exports, \"LocalSvg\", ({\n  enumerable: true,\n  get: function () {\n    return _deprecated.LocalSvg;\n  }\n}));\nObject.defineProperty(exports, \"SvgAst\", ({\n  enumerable: true,\n  get: function () {\n    return _xml.SvgAst;\n  }\n}));\nObject.defineProperty(exports, \"SvgCss\", ({\n  enumerable: true,\n  get: function () {\n    return _deprecated.SvgCss;\n  }\n}));\nObject.defineProperty(exports, \"SvgCssUri\", ({\n  enumerable: true,\n  get: function () {\n    return _deprecated.SvgCssUri;\n  }\n}));\nObject.defineProperty(exports, \"SvgFromUri\", ({\n  enumerable: true,\n  get: function () {\n    return _xml.SvgFromUri;\n  }\n}));\nObject.defineProperty(exports, \"SvgFromXml\", ({\n  enumerable: true,\n  get: function () {\n    return _xml.SvgFromXml;\n  }\n}));\nObject.defineProperty(exports, \"SvgUri\", ({\n  enumerable: true,\n  get: function () {\n    return _xml.SvgUri;\n  }\n}));\nObject.defineProperty(exports, \"SvgWithCss\", ({\n  enumerable: true,\n  get: function () {\n    return _deprecated.SvgWithCss;\n  }\n}));\nObject.defineProperty(exports, \"SvgWithCssUri\", ({\n  enumerable: true,\n  get: function () {\n    return _deprecated.SvgWithCssUri;\n  }\n}));\nObject.defineProperty(exports, \"SvgXml\", ({\n  enumerable: true,\n  get: function () {\n    return _xml.SvgXml;\n  }\n}));\nObject.defineProperty(exports, \"WithLocalSvg\", ({\n  enumerable: true,\n  get: function () {\n    return _deprecated.WithLocalSvg;\n  }\n}));\nObject.defineProperty(exports, \"camelCase\", ({\n  enumerable: true,\n  get: function () {\n    return _xml.camelCase;\n  }\n}));\nObject.defineProperty(exports, \"default\", ({\n  enumerable: true,\n  get: function () {\n    return _elements.default;\n  }\n}));\nObject.defineProperty(exports, \"fetchText\", ({\n  enumerable: true,\n  get: function () {\n    return _fetchData.fetchText;\n  }\n}));\nObject.defineProperty(exports, \"inlineStyles\", ({\n  enumerable: true,\n  get: function () {\n    return _deprecated.inlineStyles;\n  }\n}));\nObject.defineProperty(exports, \"loadLocalRawResource\", ({\n  enumerable: true,\n  get: function () {\n    return _deprecated.loadLocalRawResource;\n  }\n}));\nObject.defineProperty(exports, \"parse\", ({\n  enumerable: true,\n  get: function () {\n    return _xml.parse;\n  }\n}));\nvar _xml = __webpack_require__(/*! ./xml */ \"../../node_modules/react-native-svg/lib/commonjs/xml.js\");\nvar _fetchData = __webpack_require__(/*! ./utils/fetchData */ \"../../node_modules/react-native-svg/lib/commonjs/utils/fetchData.js\");\nvar _deprecated = __webpack_require__(/*! ./deprecated */ \"../../node_modules/react-native-svg/lib/commonjs/deprecated.js\");\nvar _types = __webpack_require__(/*! ./lib/extract/types */ \"../../node_modules/react-native-svg/lib/commonjs/lib/extract/types.js\");\nObject.keys(_types).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _types[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _types[key];\n    }\n  });\n});\nvar _elements = _interopRequireWildcard(__webpack_require__(/*! ./elements */ \"../../node_modules/react-native-svg/lib/commonjs/elements.web.js\"));\nObject.keys(_elements).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _elements[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _elements[key];\n    }\n  });\n});\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\n//# sourceMappingURL=ReactNativeSVG.web.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LW5hdGl2ZS1zdmcvbGliL2NvbW1vbmpzL1JlYWN0TmF0aXZlU1ZHLndlYi5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBMkM7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRiwwQ0FBeUM7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRiwwQ0FBeUM7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRiw2Q0FBNEM7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRiw4Q0FBNkM7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRiw4Q0FBNkM7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRiwwQ0FBeUM7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRiw4Q0FBNkM7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRixpREFBZ0Q7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRiwwQ0FBeUM7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRixnREFBK0M7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRiw2Q0FBNEM7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRiwyQ0FBMEM7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRiw2Q0FBNEM7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRixnREFBK0M7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRix3REFBdUQ7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRix5Q0FBd0M7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRixXQUFXLG1CQUFPLENBQUMsc0VBQU87QUFDMUIsaUJBQWlCLG1CQUFPLENBQUMsOEZBQW1CO0FBQzVDLGtCQUFrQixtQkFBTyxDQUFDLG9GQUFjO0FBQ3hDLGFBQWEsbUJBQU8sQ0FBQyxrR0FBcUI7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILENBQUM7QUFDRCx3Q0FBd0MsbUJBQU8sQ0FBQyxvRkFBWTtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsQ0FBQztBQUNELHVDQUF1QywrQ0FBK0MsMENBQTBDLGtEQUFrRCxtQkFBbUI7QUFDck0seUNBQXlDLHVDQUF1QywyRUFBMkUsY0FBYyxxQ0FBcUMsb0NBQW9DLFVBQVUsaUJBQWlCLGdFQUFnRSwwQ0FBMEMsOEJBQThCLDBEQUEwRCx3RUFBd0U7QUFDdmhCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LW5hdGl2ZS1zdmcvbGliL2NvbW1vbmpzL1JlYWN0TmF0aXZlU1ZHLndlYi5qcz80MjFmIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pO1xudmFyIF9leHBvcnROYW1lcyA9IHtcbiAgY2FtZWxDYXNlOiB0cnVlLFxuICBwYXJzZTogdHJ1ZSxcbiAgU3ZnQXN0OiB0cnVlLFxuICBTdmdGcm9tVXJpOiB0cnVlLFxuICBTdmdGcm9tWG1sOiB0cnVlLFxuICBTdmdVcmk6IHRydWUsXG4gIFN2Z1htbDogdHJ1ZSxcbiAgZmV0Y2hUZXh0OiB0cnVlLFxuICBpbmxpbmVTdHlsZXM6IHRydWUsXG4gIGxvYWRMb2NhbFJhd1Jlc291cmNlOiB0cnVlLFxuICBMb2NhbFN2ZzogdHJ1ZSxcbiAgU3ZnQ3NzOiB0cnVlLFxuICBTdmdDc3NVcmk6IHRydWUsXG4gIFN2Z1dpdGhDc3M6IHRydWUsXG4gIFN2Z1dpdGhDc3NVcmk6IHRydWUsXG4gIFdpdGhMb2NhbFN2ZzogdHJ1ZVxufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIkxvY2FsU3ZnXCIsIHtcbiAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIF9kZXByZWNhdGVkLkxvY2FsU3ZnO1xuICB9XG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIlN2Z0FzdFwiLCB7XG4gIGVudW1lcmFibGU6IHRydWUsXG4gIGdldDogZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBfeG1sLlN2Z0FzdDtcbiAgfVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJTdmdDc3NcIiwge1xuICBlbnVtZXJhYmxlOiB0cnVlLFxuICBnZXQ6IGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gX2RlcHJlY2F0ZWQuU3ZnQ3NzO1xuICB9XG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIlN2Z0Nzc1VyaVwiLCB7XG4gIGVudW1lcmFibGU6IHRydWUsXG4gIGdldDogZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBfZGVwcmVjYXRlZC5TdmdDc3NVcmk7XG4gIH1cbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiU3ZnRnJvbVVyaVwiLCB7XG4gIGVudW1lcmFibGU6IHRydWUsXG4gIGdldDogZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBfeG1sLlN2Z0Zyb21Vcmk7XG4gIH1cbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiU3ZnRnJvbVhtbFwiLCB7XG4gIGVudW1lcmFibGU6IHRydWUsXG4gIGdldDogZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBfeG1sLlN2Z0Zyb21YbWw7XG4gIH1cbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiU3ZnVXJpXCIsIHtcbiAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIF94bWwuU3ZnVXJpO1xuICB9XG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIlN2Z1dpdGhDc3NcIiwge1xuICBlbnVtZXJhYmxlOiB0cnVlLFxuICBnZXQ6IGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gX2RlcHJlY2F0ZWQuU3ZnV2l0aENzcztcbiAgfVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJTdmdXaXRoQ3NzVXJpXCIsIHtcbiAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIF9kZXByZWNhdGVkLlN2Z1dpdGhDc3NVcmk7XG4gIH1cbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiU3ZnWG1sXCIsIHtcbiAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIF94bWwuU3ZnWG1sO1xuICB9XG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIldpdGhMb2NhbFN2Z1wiLCB7XG4gIGVudW1lcmFibGU6IHRydWUsXG4gIGdldDogZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBfZGVwcmVjYXRlZC5XaXRoTG9jYWxTdmc7XG4gIH1cbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiY2FtZWxDYXNlXCIsIHtcbiAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIF94bWwuY2FtZWxDYXNlO1xuICB9XG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImRlZmF1bHRcIiwge1xuICBlbnVtZXJhYmxlOiB0cnVlLFxuICBnZXQ6IGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gX2VsZW1lbnRzLmRlZmF1bHQ7XG4gIH1cbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiZmV0Y2hUZXh0XCIsIHtcbiAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIF9mZXRjaERhdGEuZmV0Y2hUZXh0O1xuICB9XG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImlubGluZVN0eWxlc1wiLCB7XG4gIGVudW1lcmFibGU6IHRydWUsXG4gIGdldDogZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBfZGVwcmVjYXRlZC5pbmxpbmVTdHlsZXM7XG4gIH1cbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwibG9hZExvY2FsUmF3UmVzb3VyY2VcIiwge1xuICBlbnVtZXJhYmxlOiB0cnVlLFxuICBnZXQ6IGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gX2RlcHJlY2F0ZWQubG9hZExvY2FsUmF3UmVzb3VyY2U7XG4gIH1cbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwicGFyc2VcIiwge1xuICBlbnVtZXJhYmxlOiB0cnVlLFxuICBnZXQ6IGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gX3htbC5wYXJzZTtcbiAgfVxufSk7XG52YXIgX3htbCA9IHJlcXVpcmUoXCIuL3htbFwiKTtcbnZhciBfZmV0Y2hEYXRhID0gcmVxdWlyZShcIi4vdXRpbHMvZmV0Y2hEYXRhXCIpO1xudmFyIF9kZXByZWNhdGVkID0gcmVxdWlyZShcIi4vZGVwcmVjYXRlZFwiKTtcbnZhciBfdHlwZXMgPSByZXF1aXJlKFwiLi9saWIvZXh0cmFjdC90eXBlc1wiKTtcbk9iamVjdC5rZXlzKF90eXBlcykuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7XG4gIGlmIChrZXkgPT09IFwiZGVmYXVsdFwiIHx8IGtleSA9PT0gXCJfX2VzTW9kdWxlXCIpIHJldHVybjtcbiAgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChfZXhwb3J0TmFtZXMsIGtleSkpIHJldHVybjtcbiAgaWYgKGtleSBpbiBleHBvcnRzICYmIGV4cG9ydHNba2V5XSA9PT0gX3R5cGVzW2tleV0pIHJldHVybjtcbiAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIGtleSwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgICByZXR1cm4gX3R5cGVzW2tleV07XG4gICAgfVxuICB9KTtcbn0pO1xudmFyIF9lbGVtZW50cyA9IF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkKHJlcXVpcmUoXCIuL2VsZW1lbnRzXCIpKTtcbk9iamVjdC5rZXlzKF9lbGVtZW50cykuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7XG4gIGlmIChrZXkgPT09IFwiZGVmYXVsdFwiIHx8IGtleSA9PT0gXCJfX2VzTW9kdWxlXCIpIHJldHVybjtcbiAgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChfZXhwb3J0TmFtZXMsIGtleSkpIHJldHVybjtcbiAgaWYgKGtleSBpbiBleHBvcnRzICYmIGV4cG9ydHNba2V5XSA9PT0gX2VsZW1lbnRzW2tleV0pIHJldHVybjtcbiAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIGtleSwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgICByZXR1cm4gX2VsZW1lbnRzW2tleV07XG4gICAgfVxuICB9KTtcbn0pO1xuZnVuY3Rpb24gX2dldFJlcXVpcmVXaWxkY2FyZENhY2hlKGUpIHsgaWYgKFwiZnVuY3Rpb25cIiAhPSB0eXBlb2YgV2Vha01hcCkgcmV0dXJuIG51bGw7IHZhciByID0gbmV3IFdlYWtNYXAoKSwgdCA9IG5ldyBXZWFrTWFwKCk7IHJldHVybiAoX2dldFJlcXVpcmVXaWxkY2FyZENhY2hlID0gZnVuY3Rpb24gKGUpIHsgcmV0dXJuIGUgPyB0IDogcjsgfSkoZSk7IH1cbmZ1bmN0aW9uIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkKGUsIHIpIHsgaWYgKCFyICYmIGUgJiYgZS5fX2VzTW9kdWxlKSByZXR1cm4gZTsgaWYgKG51bGwgPT09IGUgfHwgXCJvYmplY3RcIiAhPSB0eXBlb2YgZSAmJiBcImZ1bmN0aW9uXCIgIT0gdHlwZW9mIGUpIHJldHVybiB7IGRlZmF1bHQ6IGUgfTsgdmFyIHQgPSBfZ2V0UmVxdWlyZVdpbGRjYXJkQ2FjaGUocik7IGlmICh0ICYmIHQuaGFzKGUpKSByZXR1cm4gdC5nZXQoZSk7IHZhciBuID0geyBfX3Byb3RvX186IG51bGwgfSwgYSA9IE9iamVjdC5kZWZpbmVQcm9wZXJ0eSAmJiBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yOyBmb3IgKHZhciB1IGluIGUpIGlmIChcImRlZmF1bHRcIiAhPT0gdSAmJiB7fS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGUsIHUpKSB7IHZhciBpID0gYSA/IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoZSwgdSkgOiBudWxsOyBpICYmIChpLmdldCB8fCBpLnNldCkgPyBPYmplY3QuZGVmaW5lUHJvcGVydHkobiwgdSwgaSkgOiBuW3VdID0gZVt1XTsgfSByZXR1cm4gbi5kZWZhdWx0ID0gZSwgdCAmJiB0LnNldChlLCBuKSwgbjsgfVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9UmVhY3ROYXRpdmVTVkcud2ViLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/ReactNativeSVG.web.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/deprecated.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/deprecated.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.LocalSvg = LocalSvg;\nexports.SvgCss = SvgCss;\nexports.SvgCssUri = SvgCssUri;\nexports.SvgWithCss = SvgWithCss;\nexports.SvgWithCssUri = SvgWithCssUri;\nexports.WithLocalSvg = WithLocalSvg;\nexports.inlineStyles = inlineStyles;\nexports.loadLocalRawResource = loadLocalRawResource;\nexports.showErrorCSS = showErrorCSS;\nfunction showErrorCSS(name, type) {\n  throw Error(`[react-native-svg] You are trying to import a ${type} \\`${name}\\` that has been moved to a sub-package. Change your import from \\`react-native-svg\\` to \\`react-native-svg/css\\`.`);\n}\nfunction SvgCss() {\n  showErrorCSS('SvgCss', 'component');\n}\nfunction SvgCssUri() {\n  showErrorCSS('SvgCssUri', 'component');\n}\nfunction SvgWithCss() {\n  showErrorCSS('SvgWithCss', 'component');\n}\nfunction SvgWithCssUri() {\n  showErrorCSS('SvgWithCssUri', 'component');\n}\nfunction inlineStyles() {\n  showErrorCSS('inlineStyles', 'function');\n}\nfunction LocalSvg() {\n  showErrorCSS('LocalSvg', 'component');\n}\nfunction WithLocalSvg() {\n  showErrorCSS('WithLocalSvg', 'component');\n}\nfunction loadLocalRawResource() {\n  showErrorCSS('loadLocalRawResource', 'function');\n}\n//# sourceMappingURL=deprecated.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/deprecated.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/elements.web.js":
/*!************************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/elements.web.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = exports.Use = exports.TextPath = exports.Text = exports.TSpan = exports.Symbol = exports.Svg = exports.Stop = exports.Rect = exports.RadialGradient = exports.Polyline = exports.Polygon = exports.Pattern = exports.Path = exports.Mask = exports.Marker = exports.LinearGradient = exports.Line = exports.Image = exports.G = exports.ForeignObject = exports.Filter = exports.FeTurbulence = exports.FeTile = exports.FeSpotLight = exports.FeSpecularLighting = exports.FePointLight = exports.FeOffset = exports.FeMorphology = exports.FeMergeNode = exports.FeMerge = exports.FeImage = exports.FeGaussianBlur = exports.FeFuncR = exports.FeFuncG = exports.FeFuncB = exports.FeFuncA = exports.FeFlood = exports.FeDropShadow = exports.FeDistantLight = exports.FeDisplacementMap = exports.FeDiffuseLighting = exports.FeConvolveMatrix = exports.FeComposite = exports.FeComponentTransfer = exports.FeColorMatrix = exports.FeBlend = exports.Ellipse = exports.Defs = exports.ClipPath = exports.Circle = void 0;\nvar _utils = __webpack_require__(/*! ./web/utils */ \"../../node_modules/react-native-svg/lib/commonjs/web/utils/index.js\");\nvar _WebShape = __webpack_require__(/*! ./web/WebShape */ \"../../node_modules/react-native-svg/lib/commonjs/web/WebShape.js\");\nclass Circle extends _WebShape.WebShape {\n  tag = 'circle';\n}\nexports.Circle = Circle;\nclass ClipPath extends _WebShape.WebShape {\n  tag = 'clipPath';\n}\nexports.ClipPath = ClipPath;\nclass Defs extends _WebShape.WebShape {\n  tag = 'defs';\n}\nexports.Defs = Defs;\nclass Ellipse extends _WebShape.WebShape {\n  tag = 'ellipse';\n}\nexports.Ellipse = Ellipse;\nclass FeBlend extends _WebShape.WebShape {\n  tag = 'feBlend';\n}\nexports.FeBlend = FeBlend;\nclass FeColorMatrix extends _WebShape.WebShape {\n  tag = 'feColorMatrix';\n}\nexports.FeColorMatrix = FeColorMatrix;\nclass FeComponentTransfer extends _WebShape.WebShape {\n  tag = 'feComponentTransfer';\n}\nexports.FeComponentTransfer = FeComponentTransfer;\nclass FeComposite extends _WebShape.WebShape {\n  tag = 'feComposite';\n}\nexports.FeComposite = FeComposite;\nclass FeConvolveMatrix extends _WebShape.WebShape {\n  tag = 'feConvolveMatrix';\n}\nexports.FeConvolveMatrix = FeConvolveMatrix;\nclass FeDiffuseLighting extends _WebShape.WebShape {\n  tag = 'feDiffuseLighting';\n}\nexports.FeDiffuseLighting = FeDiffuseLighting;\nclass FeDisplacementMap extends _WebShape.WebShape {\n  tag = 'feDisplacementMap';\n}\nexports.FeDisplacementMap = FeDisplacementMap;\nclass FeDistantLight extends _WebShape.WebShape {\n  tag = 'feDistantLight';\n}\nexports.FeDistantLight = FeDistantLight;\nclass FeDropShadow extends _WebShape.WebShape {\n  tag = 'feDropShadow';\n}\nexports.FeDropShadow = FeDropShadow;\nclass FeFlood extends _WebShape.WebShape {\n  tag = 'feFlood';\n}\nexports.FeFlood = FeFlood;\nclass FeFuncA extends _WebShape.WebShape {\n  tag = 'feFuncA';\n}\nexports.FeFuncA = FeFuncA;\nclass FeFuncB extends _WebShape.WebShape {\n  tag = 'feFuncB';\n}\nexports.FeFuncB = FeFuncB;\nclass FeFuncG extends _WebShape.WebShape {\n  tag = 'feFuncG';\n}\nexports.FeFuncG = FeFuncG;\nclass FeFuncR extends _WebShape.WebShape {\n  tag = 'feFuncR';\n}\nexports.FeFuncR = FeFuncR;\nclass FeGaussianBlur extends _WebShape.WebShape {\n  tag = 'feGaussianBlur';\n}\nexports.FeGaussianBlur = FeGaussianBlur;\nclass FeImage extends _WebShape.WebShape {\n  tag = 'feImage';\n}\nexports.FeImage = FeImage;\nclass FeMerge extends _WebShape.WebShape {\n  tag = 'feMerge';\n}\nexports.FeMerge = FeMerge;\nclass FeMergeNode extends _WebShape.WebShape {\n  tag = 'feMergeNode';\n}\nexports.FeMergeNode = FeMergeNode;\nclass FeMorphology extends _WebShape.WebShape {\n  tag = 'feMorphology';\n}\nexports.FeMorphology = FeMorphology;\nclass FeOffset extends _WebShape.WebShape {\n  tag = 'feOffset';\n}\nexports.FeOffset = FeOffset;\nclass FePointLight extends _WebShape.WebShape {\n  tag = 'fePointLight';\n}\nexports.FePointLight = FePointLight;\nclass FeSpecularLighting extends _WebShape.WebShape {\n  tag = 'feSpecularLighting';\n}\nexports.FeSpecularLighting = FeSpecularLighting;\nclass FeSpotLight extends _WebShape.WebShape {\n  tag = 'feSpotLight';\n}\nexports.FeSpotLight = FeSpotLight;\nclass FeTile extends _WebShape.WebShape {\n  tag = 'feTile';\n}\nexports.FeTile = FeTile;\nclass FeTurbulence extends _WebShape.WebShape {\n  tag = 'feTurbulence';\n}\nexports.FeTurbulence = FeTurbulence;\nclass Filter extends _WebShape.WebShape {\n  tag = 'filter';\n}\nexports.Filter = Filter;\nclass ForeignObject extends _WebShape.WebShape {\n  tag = 'foreignObject';\n}\nexports.ForeignObject = ForeignObject;\nclass G extends _WebShape.WebShape {\n  tag = 'g';\n  prepareProps(props) {\n    const {\n      x,\n      y,\n      ...rest\n    } = props;\n    if ((x || y) && !rest.translate) {\n      rest.translate = `${x || 0}, ${y || 0}`;\n    }\n    return rest;\n  }\n}\nexports.G = G;\nclass Image extends _WebShape.WebShape {\n  tag = 'image';\n}\nexports.Image = Image;\nclass Line extends _WebShape.WebShape {\n  tag = 'line';\n}\nexports.Line = Line;\nclass LinearGradient extends _WebShape.WebShape {\n  tag = 'linearGradient';\n}\nexports.LinearGradient = LinearGradient;\nclass Marker extends _WebShape.WebShape {\n  tag = 'marker';\n}\nexports.Marker = Marker;\nclass Mask extends _WebShape.WebShape {\n  tag = 'mask';\n}\nexports.Mask = Mask;\nclass Path extends _WebShape.WebShape {\n  tag = 'path';\n}\nexports.Path = Path;\nclass Pattern extends _WebShape.WebShape {\n  tag = 'pattern';\n}\nexports.Pattern = Pattern;\nclass Polygon extends _WebShape.WebShape {\n  tag = 'polygon';\n}\nexports.Polygon = Polygon;\nclass Polyline extends _WebShape.WebShape {\n  tag = 'polyline';\n}\nexports.Polyline = Polyline;\nclass RadialGradient extends _WebShape.WebShape {\n  tag = 'radialGradient';\n}\nexports.RadialGradient = RadialGradient;\nclass Rect extends _WebShape.WebShape {\n  tag = 'rect';\n}\nexports.Rect = Rect;\nclass Stop extends _WebShape.WebShape {\n  tag = 'stop';\n}\nexports.Stop = Stop;\nclass Svg extends _WebShape.WebShape {\n  tag = 'svg';\n  toDataURL(callback, options = {}) {\n    const ref = this.elementRef.current;\n    if (ref === null) {\n      return;\n    }\n    const rect = (0, _utils.getBoundingClientRect)(ref);\n    const width = Number(options.width) || rect.width;\n    const height = Number(options.height) || rect.height;\n    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    svg.setAttribute('viewBox', `0 0 ${rect.width} ${rect.height}`);\n    svg.setAttribute('width', String(width));\n    svg.setAttribute('height', String(height));\n    svg.appendChild(ref.cloneNode(true));\n    const img = new window.Image();\n    img.onload = () => {\n      const canvas = document.createElement('canvas');\n      canvas.width = width;\n      canvas.height = height;\n      const context = canvas.getContext('2d');\n      context === null || context === void 0 || context.drawImage(img, 0, 0);\n      callback(canvas.toDataURL().replace('data:image/png;base64,', ''));\n    };\n    img.src = `data:image/svg+xml;utf8,${(0, _utils.encodeSvg)(new window.XMLSerializer().serializeToString(svg))}`;\n  }\n}\nexports.Svg = Svg;\nclass Symbol extends _WebShape.WebShape {\n  tag = 'symbol';\n}\nexports.Symbol = Symbol;\nclass TSpan extends _WebShape.WebShape {\n  tag = 'tspan';\n}\nexports.TSpan = TSpan;\nclass Text extends _WebShape.WebShape {\n  tag = 'text';\n}\nexports.Text = Text;\nclass TextPath extends _WebShape.WebShape {\n  tag = 'textPath';\n}\nexports.TextPath = TextPath;\nclass Use extends _WebShape.WebShape {\n  tag = 'use';\n}\nexports.Use = Use;\nvar _default = exports[\"default\"] = Svg;\n//# sourceMappingURL=elements.web.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/elements.web.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/index.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n'use client';\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nvar _exportNames = {};\nObject.defineProperty(exports, \"default\", ({\n  enumerable: true,\n  get: function () {\n    return _ReactNativeSVG.default;\n  }\n}));\nvar _ReactNativeSVG = _interopRequireWildcard(__webpack_require__(/*! ./ReactNativeSVG */ \"../../node_modules/react-native-svg/lib/commonjs/ReactNativeSVG.web.js\"));\nObject.keys(_ReactNativeSVG).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _ReactNativeSVG[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _ReactNativeSVG[key];\n    }\n  });\n});\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/index.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/lib/Matrix2D.js":
/*!************************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/lib/Matrix2D.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.append = append;\nexports.appendTransform = appendTransform;\nexports.identity = void 0;\nexports.reset = reset;\nexports.toArray = toArray;\n/**\n * based on\n * https://github.com/CreateJS/EaselJS/blob/631cdffb85eff9413dab43b4676f059b4232d291/src/easeljs/geom/Matrix2D.js\n */\nconst DEG_TO_RAD = Math.PI / 180;\nconst identity = exports.identity = [1, 0, 0, 1, 0, 0];\nlet a = 1;\nlet b = 0;\nlet c = 0;\nlet d = 1;\nlet tx = 0;\nlet ty = 0;\nlet hasInitialState = true;\n\n/**\n * Represents an affine transformation matrix, and provides tools for concatenating transforms.\n *\n * This matrix can be visualized as:\n *\n * \t[ a  c  tx\n * \t  b  d  ty\n * \t  0  0  1  ]\n *\n * Note the locations of b and c.\n **/\n\n/**\n * Reset current matrix to an identity matrix.\n * @method reset\n **/\nfunction reset() {\n  if (hasInitialState) {\n    return;\n  }\n  a = d = 1;\n  b = c = tx = ty = 0;\n  hasInitialState = true;\n}\n\n/**\n * Returns an array with current matrix values.\n * @method toArray\n * @return {Array} an array with current matrix values.\n **/\nfunction toArray() {\n  if (hasInitialState) {\n    return identity;\n  }\n  return [a, b, c, d, tx, ty];\n}\n\n/**\n * Appends the specified matrix properties to this matrix. All parameters are required.\n * This is the equivalent of multiplying `(this matrix) * (specified matrix)`.\n * @method append\n * @param {Number} a2\n * @param {Number} b2\n * @param {Number} c2\n * @param {Number} d2\n * @param {Number} tx2\n * @param {Number} ty2\n **/\nfunction append(a2, b2, c2, d2, tx2, ty2) {\n  const change = a2 !== 1 || b2 !== 0 || c2 !== 0 || d2 !== 1;\n  const translate = tx2 !== 0 || ty2 !== 0;\n  if (!change && !translate) {\n    return;\n  }\n  if (hasInitialState) {\n    hasInitialState = false;\n    a = a2;\n    b = b2;\n    c = c2;\n    d = d2;\n    tx = tx2;\n    ty = ty2;\n    return;\n  }\n  const a1 = a;\n  const b1 = b;\n  const c1 = c;\n  const d1 = d;\n  if (change) {\n    a = a1 * a2 + c1 * b2;\n    b = b1 * a2 + d1 * b2;\n    c = a1 * c2 + c1 * d2;\n    d = b1 * c2 + d1 * d2;\n  }\n  if (translate) {\n    tx = a1 * tx2 + c1 * ty2 + tx;\n    ty = b1 * tx2 + d1 * ty2 + ty;\n  }\n}\n\n/**\n * Generates matrix properties from the specified display object transform properties, and appends them to this matrix.\n * For example, you can use this to generate a matrix representing the transformations of a display object:\n *\n * \treset();\n * \tappendTransform(o.x, o.y, o.scaleX, o.scaleY, o.rotation);\n * \tvar matrix = toArray()\n *\n * @method appendTransform\n * @param {Number} x\n * @param {Number} y\n * @param {Number} scaleX\n * @param {Number} scaleY\n * @param {Number} rotation\n * @param {Number} skewX\n * @param {Number} skewY\n * @param {Number} regX Optional.\n * @param {Number} regY Optional.\n **/\nfunction appendTransform(x, y, scaleX, scaleY, rotation, skewX, skewY, regX, regY) {\n  if (x === 0 && y === 0 && scaleX === 1 && scaleY === 1 && rotation === 0 && skewX === 0 && skewY === 0 && regX === 0 && regY === 0) {\n    return;\n  }\n  let cos, sin;\n  if (rotation % 360) {\n    const r = rotation * DEG_TO_RAD;\n    cos = Math.cos(r);\n    sin = Math.sin(r);\n  } else {\n    cos = 1;\n    sin = 0;\n  }\n  const a2 = cos * scaleX;\n  const b2 = sin * scaleX;\n  const c2 = -sin * scaleY;\n  const d2 = cos * scaleY;\n  if (skewX || skewY) {\n    const b1 = Math.tan(skewY * DEG_TO_RAD);\n    const c1 = Math.tan(skewX * DEG_TO_RAD);\n    append(a2 + c1 * b2, b1 * a2 + b2, c2 + c1 * d2, b1 * c2 + d2, x, y);\n  } else {\n    append(a2, b2, c2, d2, x, y);\n  }\n  if (regX || regY) {\n    // append the registration offset:\n    tx -= regX * a + regY * c;\n    ty -= regX * b + regY * d;\n    hasInitialState = false;\n  }\n}\n//# sourceMappingURL=Matrix2D.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/lib/Matrix2D.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/lib/SvgTouchableMixin.js":
/*!*********************************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/lib/SvgTouchableMixin.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nvar _reactNative = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\nconst PRESS_RETENTION_OFFSET = {\n  top: 20,\n  left: 20,\n  right: 20,\n  bottom: 30\n};\n// @ts-expect-error: Mixin is not typed\nconst {\n  Mixin\n} = _reactNative.Touchable;\nconst {\n  touchableHandleStartShouldSetResponder,\n  touchableHandleResponderTerminationRequest,\n  touchableHandleResponderGrant,\n  touchableHandleResponderMove,\n  touchableHandleResponderRelease,\n  touchableHandleResponderTerminate,\n  touchableGetInitialState\n} = Mixin;\nconst SvgTouchableMixin = {\n  ...Mixin,\n  touchableHandleStartShouldSetResponder(e) {\n    const {\n      onStartShouldSetResponder\n    } = this.props;\n    if (onStartShouldSetResponder) {\n      return onStartShouldSetResponder(e);\n    } else {\n      return touchableHandleStartShouldSetResponder.call(this, e);\n    }\n  },\n  touchableHandleResponderTerminationRequest(e) {\n    const {\n      onResponderTerminationRequest\n    } = this.props;\n    if (onResponderTerminationRequest) {\n      return onResponderTerminationRequest(e);\n    } else {\n      return touchableHandleResponderTerminationRequest.call(this, e);\n    }\n  },\n  touchableHandleResponderGrant(e) {\n    const {\n      onResponderGrant\n    } = this.props;\n    if (onResponderGrant) {\n      return onResponderGrant(e);\n    } else {\n      return touchableHandleResponderGrant.call(this, e);\n    }\n  },\n  touchableHandleResponderMove(e) {\n    const {\n      onResponderMove\n    } = this.props;\n    if (onResponderMove) {\n      return onResponderMove(e);\n    } else {\n      return touchableHandleResponderMove.call(this, e);\n    }\n  },\n  touchableHandleResponderRelease(e) {\n    const {\n      onResponderRelease\n    } = this.props;\n    if (onResponderRelease) {\n      return onResponderRelease(e);\n    } else {\n      return touchableHandleResponderRelease.call(this, e);\n    }\n  },\n  touchableHandleResponderTerminate(e) {\n    const {\n      onResponderTerminate\n    } = this.props;\n    if (onResponderTerminate) {\n      return onResponderTerminate(e);\n    } else {\n      return touchableHandleResponderTerminate.call(this, e);\n    }\n  },\n  touchableHandlePress(e) {\n    const {\n      onPress\n    } = this.props;\n    onPress && onPress(e);\n  },\n  touchableHandleActivePressIn(e) {\n    const {\n      onPressIn\n    } = this.props;\n    onPressIn && onPressIn(e);\n  },\n  touchableHandleActivePressOut(e) {\n    const {\n      onPressOut\n    } = this.props;\n    onPressOut && onPressOut(e);\n  },\n  touchableHandleLongPress(e) {\n    const {\n      onLongPress\n    } = this.props;\n    onLongPress && onLongPress(e);\n  },\n  touchableGetPressRectOffset() {\n    const {\n      pressRetentionOffset\n    } = this.props;\n    return pressRetentionOffset || PRESS_RETENTION_OFFSET;\n  },\n  touchableGetHitSlop() {\n    const {\n      hitSlop\n    } = this.props;\n    return hitSlop;\n  },\n  touchableGetHighlightDelayMS() {\n    const {\n      delayPressIn\n    } = this.props;\n    return delayPressIn || 0;\n  },\n  touchableGetLongPressDelayMS() {\n    const {\n      delayLongPress\n    } = this.props;\n    return delayLongPress === 0 ? 0 : delayLongPress || 500;\n  },\n  touchableGetPressOutDelayMS() {\n    const {\n      delayPressOut\n    } = this.props;\n    return delayPressOut || 0;\n  }\n};\nconst touchKeys = Object.keys(SvgTouchableMixin);\nconst touchVals = touchKeys.map(key => SvgTouchableMixin[key]);\nconst numTouchKeys = touchKeys.length;\nvar _default = target => {\n  for (let i = 0; i < numTouchKeys; i++) {\n    const key = touchKeys[i];\n    const val = touchVals[i];\n    if (typeof val === 'function') {\n      target[key] = val.bind(target);\n    } else {\n      target[key] = val;\n    }\n  }\n  target.state = touchableGetInitialState();\n};\nexports[\"default\"] = _default;\n//# sourceMappingURL=SvgTouchableMixin.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/lib/SvgTouchableMixin.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/lib/extract/extractTransform.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/lib/extract/extractTransform.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = extractTransform;\nexports.extractTransformSvgView = extractTransformSvgView;\nexports.props2transform = props2transform;\nexports.transformToMatrix = transformToMatrix;\nexports.transformsArrayToProps = transformsArrayToProps;\nvar _Matrix2D = __webpack_require__(/*! ../Matrix2D */ \"../../node_modules/react-native-svg/lib/commonjs/lib/Matrix2D.js\");\nvar _transform = __webpack_require__(/*! ./transform */ \"../../node_modules/react-native-svg/lib/commonjs/lib/extract/transform.js\");\nvar _transformToRn = __webpack_require__(/*! ./transformToRn */ \"../../node_modules/react-native-svg/lib/commonjs/lib/extract/transformToRn.js\");\nfunction appendTransformProps(props) {\n  const {\n    x,\n    y,\n    originX,\n    originY,\n    scaleX,\n    scaleY,\n    rotation,\n    skewX,\n    skewY\n  } = props;\n  (0, _Matrix2D.appendTransform)(x + originX, y + originY, scaleX, scaleY, rotation, skewX, skewY, originX, originY);\n}\nfunction universal2axis(universal, axisX, axisY, defaultValue) {\n  let x;\n  let y;\n  if (typeof universal === 'number') {\n    x = y = universal;\n  } else if (typeof universal === 'string') {\n    const coords = universal.split(/\\s*,\\s*/);\n    if (coords.length === 2) {\n      x = +coords[0];\n      y = +coords[1];\n    } else if (coords.length === 1) {\n      x = y = +coords[0];\n    }\n  } else if (Array.isArray(universal)) {\n    if (universal.length === 2) {\n      x = +universal[0];\n      y = +universal[1];\n    } else if (universal.length === 1) {\n      x = y = +universal[0];\n    }\n  }\n  axisX = +axisX;\n  if (!isNaN(axisX)) {\n    x = axisX;\n  }\n  axisY = +axisY;\n  if (!isNaN(axisY)) {\n    y = axisY;\n  }\n  return [x || defaultValue || 0, y || defaultValue || 0];\n}\nfunction transformsArrayToProps(transformObjectsArray) {\n  const props = {};\n  transformObjectsArray === null || transformObjectsArray === void 0 || transformObjectsArray.forEach(transformObject => {\n    const keys = Object.keys(transformObject);\n    if (keys.length !== 1) {\n      console.error('You must specify exactly one property per transform object.');\n    }\n    const key = keys[0];\n    const value = transformObject[key];\n    // @ts-expect-error FIXME\n    props[key] = value;\n  });\n  return props;\n}\nfunction props2transform(props) {\n  if (!props) {\n    return null;\n  }\n  const {\n    rotation,\n    translate,\n    translateX,\n    translateY,\n    origin,\n    originX,\n    originY,\n    scale,\n    scaleX,\n    scaleY,\n    skew,\n    skewX,\n    skewY,\n    x,\n    y\n  } = props;\n  if (rotation == null && translate == null && translateX == null && translateY == null && origin == null && originX == null && originY == null && scale == null && scaleX == null && scaleY == null && skew == null && skewX == null && skewY == null && x == null && y == null) {\n    return null;\n  }\n  if (Array.isArray(x) || Array.isArray(y)) {\n    console.warn('Passing SvgLengthList to x or y attribute where SvgLength expected');\n  }\n  const tr = universal2axis(translate, translateX || (Array.isArray(x) ? x[0] : x), translateY || (Array.isArray(y) ? y[0] : y));\n  const or = universal2axis(origin, originX, originY);\n  const sc = universal2axis(scale, scaleX, scaleY, 1);\n  const sk = universal2axis(skew, skewX, skewY);\n  return {\n    rotation: rotation == null ? 0 : +rotation || 0,\n    originX: or[0],\n    originY: or[1],\n    scaleX: sc[0],\n    scaleY: sc[1],\n    skewX: sk[0],\n    skewY: sk[1],\n    x: tr[0],\n    y: tr[1]\n  };\n}\nfunction transformToMatrix(props, transform) {\n  if (!props && !transform) {\n    return null;\n  }\n  (0, _Matrix2D.reset)();\n  props && appendTransformProps(props);\n  if (transform) {\n    if (Array.isArray(transform)) {\n      if (typeof transform[0] === 'number') {\n        const columnMatrix = transform;\n        (0, _Matrix2D.append)(columnMatrix[0], columnMatrix[1], columnMatrix[2], columnMatrix[3], columnMatrix[4], columnMatrix[5]);\n      } else {\n        const transformProps = props2transform(\n        // @ts-expect-error FIXME\n        transformsArrayToProps(transform));\n        transformProps && appendTransformProps(transformProps);\n      }\n    } else if (typeof transform === 'string') {\n      try {\n        const t = (0, _transform.parse)(transform);\n        (0, _Matrix2D.append)(t[0], t[3], t[1], t[4], t[2], t[5]);\n      } catch (e) {\n        console.error(e);\n      }\n    } else {\n      // @ts-expect-error FIXME\n      const transformProps = props2transform(transform);\n      transformProps && appendTransformProps(transformProps);\n    }\n  }\n  return (0, _Matrix2D.toArray)();\n}\nfunction extractTransform(props) {\n  if (Array.isArray(props) && typeof props[0] === 'number') {\n    return props;\n  }\n  if (typeof props === 'string') {\n    try {\n      const t = (0, _transform.parse)(props);\n      return [t[0], t[3], t[1], t[4], t[2], t[5]];\n    } catch (e) {\n      console.error(e);\n      return _Matrix2D.identity;\n    }\n  }\n  // this type is not correct since props can be of type TransformsStyle['transform'] too\n  // but it satisfies TS and should not produce any type errors\n  const transformProps = props;\n  return transformToMatrix(props2transform(transformProps), transformProps === null || transformProps === void 0 ? void 0 : transformProps.transform);\n}\nfunction extractTransformSvgView(props) {\n  if (typeof props.transform === 'string') {\n    return (0, _transformToRn.parse)(props.transform);\n  }\n  return props.transform;\n}\n//# sourceMappingURL=extractTransform.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LW5hdGl2ZS1zdmcvbGliL2NvbW1vbmpzL2xpYi9leHRyYWN0L2V4dHJhY3RUcmFuc2Zvcm0uanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysa0JBQWU7QUFDZiwrQkFBK0I7QUFDL0IsdUJBQXVCO0FBQ3ZCLHlCQUF5QjtBQUN6Qiw4QkFBOEI7QUFDOUIsZ0JBQWdCLG1CQUFPLENBQUMscUZBQWE7QUFDckMsaUJBQWlCLG1CQUFPLENBQUMsOEZBQWE7QUFDdEMscUJBQXFCLG1CQUFPLENBQUMsc0dBQWlCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL25vZGVfbW9kdWxlcy9yZWFjdC1uYXRpdmUtc3ZnL2xpYi9jb21tb25qcy9saWIvZXh0cmFjdC9leHRyYWN0VHJhbnNmb3JtLmpzPzBiMmQiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLmRlZmF1bHQgPSBleHRyYWN0VHJhbnNmb3JtO1xuZXhwb3J0cy5leHRyYWN0VHJhbnNmb3JtU3ZnVmlldyA9IGV4dHJhY3RUcmFuc2Zvcm1TdmdWaWV3O1xuZXhwb3J0cy5wcm9wczJ0cmFuc2Zvcm0gPSBwcm9wczJ0cmFuc2Zvcm07XG5leHBvcnRzLnRyYW5zZm9ybVRvTWF0cml4ID0gdHJhbnNmb3JtVG9NYXRyaXg7XG5leHBvcnRzLnRyYW5zZm9ybXNBcnJheVRvUHJvcHMgPSB0cmFuc2Zvcm1zQXJyYXlUb1Byb3BzO1xudmFyIF9NYXRyaXgyRCA9IHJlcXVpcmUoXCIuLi9NYXRyaXgyRFwiKTtcbnZhciBfdHJhbnNmb3JtID0gcmVxdWlyZShcIi4vdHJhbnNmb3JtXCIpO1xudmFyIF90cmFuc2Zvcm1Ub1JuID0gcmVxdWlyZShcIi4vdHJhbnNmb3JtVG9SblwiKTtcbmZ1bmN0aW9uIGFwcGVuZFRyYW5zZm9ybVByb3BzKHByb3BzKSB7XG4gIGNvbnN0IHtcbiAgICB4LFxuICAgIHksXG4gICAgb3JpZ2luWCxcbiAgICBvcmlnaW5ZLFxuICAgIHNjYWxlWCxcbiAgICBzY2FsZVksXG4gICAgcm90YXRpb24sXG4gICAgc2tld1gsXG4gICAgc2tld1lcbiAgfSA9IHByb3BzO1xuICAoMCwgX01hdHJpeDJELmFwcGVuZFRyYW5zZm9ybSkoeCArIG9yaWdpblgsIHkgKyBvcmlnaW5ZLCBzY2FsZVgsIHNjYWxlWSwgcm90YXRpb24sIHNrZXdYLCBza2V3WSwgb3JpZ2luWCwgb3JpZ2luWSk7XG59XG5mdW5jdGlvbiB1bml2ZXJzYWwyYXhpcyh1bml2ZXJzYWwsIGF4aXNYLCBheGlzWSwgZGVmYXVsdFZhbHVlKSB7XG4gIGxldCB4O1xuICBsZXQgeTtcbiAgaWYgKHR5cGVvZiB1bml2ZXJzYWwgPT09ICdudW1iZXInKSB7XG4gICAgeCA9IHkgPSB1bml2ZXJzYWw7XG4gIH0gZWxzZSBpZiAodHlwZW9mIHVuaXZlcnNhbCA9PT0gJ3N0cmluZycpIHtcbiAgICBjb25zdCBjb29yZHMgPSB1bml2ZXJzYWwuc3BsaXQoL1xccyosXFxzKi8pO1xuICAgIGlmIChjb29yZHMubGVuZ3RoID09PSAyKSB7XG4gICAgICB4ID0gK2Nvb3Jkc1swXTtcbiAgICAgIHkgPSArY29vcmRzWzFdO1xuICAgIH0gZWxzZSBpZiAoY29vcmRzLmxlbmd0aCA9PT0gMSkge1xuICAgICAgeCA9IHkgPSArY29vcmRzWzBdO1xuICAgIH1cbiAgfSBlbHNlIGlmIChBcnJheS5pc0FycmF5KHVuaXZlcnNhbCkpIHtcbiAgICBpZiAodW5pdmVyc2FsLmxlbmd0aCA9PT0gMikge1xuICAgICAgeCA9ICt1bml2ZXJzYWxbMF07XG4gICAgICB5ID0gK3VuaXZlcnNhbFsxXTtcbiAgICB9IGVsc2UgaWYgKHVuaXZlcnNhbC5sZW5ndGggPT09IDEpIHtcbiAgICAgIHggPSB5ID0gK3VuaXZlcnNhbFswXTtcbiAgICB9XG4gIH1cbiAgYXhpc1ggPSArYXhpc1g7XG4gIGlmICghaXNOYU4oYXhpc1gpKSB7XG4gICAgeCA9IGF4aXNYO1xuICB9XG4gIGF4aXNZID0gK2F4aXNZO1xuICBpZiAoIWlzTmFOKGF4aXNZKSkge1xuICAgIHkgPSBheGlzWTtcbiAgfVxuICByZXR1cm4gW3ggfHwgZGVmYXVsdFZhbHVlIHx8IDAsIHkgfHwgZGVmYXVsdFZhbHVlIHx8IDBdO1xufVxuZnVuY3Rpb24gdHJhbnNmb3Jtc0FycmF5VG9Qcm9wcyh0cmFuc2Zvcm1PYmplY3RzQXJyYXkpIHtcbiAgY29uc3QgcHJvcHMgPSB7fTtcbiAgdHJhbnNmb3JtT2JqZWN0c0FycmF5ID09PSBudWxsIHx8IHRyYW5zZm9ybU9iamVjdHNBcnJheSA9PT0gdm9pZCAwIHx8IHRyYW5zZm9ybU9iamVjdHNBcnJheS5mb3JFYWNoKHRyYW5zZm9ybU9iamVjdCA9PiB7XG4gICAgY29uc3Qga2V5cyA9IE9iamVjdC5rZXlzKHRyYW5zZm9ybU9iamVjdCk7XG4gICAgaWYgKGtleXMubGVuZ3RoICE9PSAxKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdZb3UgbXVzdCBzcGVjaWZ5IGV4YWN0bHkgb25lIHByb3BlcnR5IHBlciB0cmFuc2Zvcm0gb2JqZWN0LicpO1xuICAgIH1cbiAgICBjb25zdCBrZXkgPSBrZXlzWzBdO1xuICAgIGNvbnN0IHZhbHVlID0gdHJhbnNmb3JtT2JqZWN0W2tleV07XG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvciBGSVhNRVxuICAgIHByb3BzW2tleV0gPSB2YWx1ZTtcbiAgfSk7XG4gIHJldHVybiBwcm9wcztcbn1cbmZ1bmN0aW9uIHByb3BzMnRyYW5zZm9ybShwcm9wcykge1xuICBpZiAoIXByb3BzKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbiAgY29uc3Qge1xuICAgIHJvdGF0aW9uLFxuICAgIHRyYW5zbGF0ZSxcbiAgICB0cmFuc2xhdGVYLFxuICAgIHRyYW5zbGF0ZVksXG4gICAgb3JpZ2luLFxuICAgIG9yaWdpblgsXG4gICAgb3JpZ2luWSxcbiAgICBzY2FsZSxcbiAgICBzY2FsZVgsXG4gICAgc2NhbGVZLFxuICAgIHNrZXcsXG4gICAgc2tld1gsXG4gICAgc2tld1ksXG4gICAgeCxcbiAgICB5XG4gIH0gPSBwcm9wcztcbiAgaWYgKHJvdGF0aW9uID09IG51bGwgJiYgdHJhbnNsYXRlID09IG51bGwgJiYgdHJhbnNsYXRlWCA9PSBudWxsICYmIHRyYW5zbGF0ZVkgPT0gbnVsbCAmJiBvcmlnaW4gPT0gbnVsbCAmJiBvcmlnaW5YID09IG51bGwgJiYgb3JpZ2luWSA9PSBudWxsICYmIHNjYWxlID09IG51bGwgJiYgc2NhbGVYID09IG51bGwgJiYgc2NhbGVZID09IG51bGwgJiYgc2tldyA9PSBudWxsICYmIHNrZXdYID09IG51bGwgJiYgc2tld1kgPT0gbnVsbCAmJiB4ID09IG51bGwgJiYgeSA9PSBudWxsKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbiAgaWYgKEFycmF5LmlzQXJyYXkoeCkgfHwgQXJyYXkuaXNBcnJheSh5KSkge1xuICAgIGNvbnNvbGUud2FybignUGFzc2luZyBTdmdMZW5ndGhMaXN0IHRvIHggb3IgeSBhdHRyaWJ1dGUgd2hlcmUgU3ZnTGVuZ3RoIGV4cGVjdGVkJyk7XG4gIH1cbiAgY29uc3QgdHIgPSB1bml2ZXJzYWwyYXhpcyh0cmFuc2xhdGUsIHRyYW5zbGF0ZVggfHwgKEFycmF5LmlzQXJyYXkoeCkgPyB4WzBdIDogeCksIHRyYW5zbGF0ZVkgfHwgKEFycmF5LmlzQXJyYXkoeSkgPyB5WzBdIDogeSkpO1xuICBjb25zdCBvciA9IHVuaXZlcnNhbDJheGlzKG9yaWdpbiwgb3JpZ2luWCwgb3JpZ2luWSk7XG4gIGNvbnN0IHNjID0gdW5pdmVyc2FsMmF4aXMoc2NhbGUsIHNjYWxlWCwgc2NhbGVZLCAxKTtcbiAgY29uc3Qgc2sgPSB1bml2ZXJzYWwyYXhpcyhza2V3LCBza2V3WCwgc2tld1kpO1xuICByZXR1cm4ge1xuICAgIHJvdGF0aW9uOiByb3RhdGlvbiA9PSBudWxsID8gMCA6ICtyb3RhdGlvbiB8fCAwLFxuICAgIG9yaWdpblg6IG9yWzBdLFxuICAgIG9yaWdpblk6IG9yWzFdLFxuICAgIHNjYWxlWDogc2NbMF0sXG4gICAgc2NhbGVZOiBzY1sxXSxcbiAgICBza2V3WDogc2tbMF0sXG4gICAgc2tld1k6IHNrWzFdLFxuICAgIHg6IHRyWzBdLFxuICAgIHk6IHRyWzFdXG4gIH07XG59XG5mdW5jdGlvbiB0cmFuc2Zvcm1Ub01hdHJpeChwcm9wcywgdHJhbnNmb3JtKSB7XG4gIGlmICghcHJvcHMgJiYgIXRyYW5zZm9ybSkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG4gICgwLCBfTWF0cml4MkQucmVzZXQpKCk7XG4gIHByb3BzICYmIGFwcGVuZFRyYW5zZm9ybVByb3BzKHByb3BzKTtcbiAgaWYgKHRyYW5zZm9ybSkge1xuICAgIGlmIChBcnJheS5pc0FycmF5KHRyYW5zZm9ybSkpIHtcbiAgICAgIGlmICh0eXBlb2YgdHJhbnNmb3JtWzBdID09PSAnbnVtYmVyJykge1xuICAgICAgICBjb25zdCBjb2x1bW5NYXRyaXggPSB0cmFuc2Zvcm07XG4gICAgICAgICgwLCBfTWF0cml4MkQuYXBwZW5kKShjb2x1bW5NYXRyaXhbMF0sIGNvbHVtbk1hdHJpeFsxXSwgY29sdW1uTWF0cml4WzJdLCBjb2x1bW5NYXRyaXhbM10sIGNvbHVtbk1hdHJpeFs0XSwgY29sdW1uTWF0cml4WzVdKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IHRyYW5zZm9ybVByb3BzID0gcHJvcHMydHJhbnNmb3JtKFxuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yIEZJWE1FXG4gICAgICAgIHRyYW5zZm9ybXNBcnJheVRvUHJvcHModHJhbnNmb3JtKSk7XG4gICAgICAgIHRyYW5zZm9ybVByb3BzICYmIGFwcGVuZFRyYW5zZm9ybVByb3BzKHRyYW5zZm9ybVByb3BzKTtcbiAgICAgIH1cbiAgICB9IGVsc2UgaWYgKHR5cGVvZiB0cmFuc2Zvcm0gPT09ICdzdHJpbmcnKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCB0ID0gKDAsIF90cmFuc2Zvcm0ucGFyc2UpKHRyYW5zZm9ybSk7XG4gICAgICAgICgwLCBfTWF0cml4MkQuYXBwZW5kKSh0WzBdLCB0WzNdLCB0WzFdLCB0WzRdLCB0WzJdLCB0WzVdKTtcbiAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihlKTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciBGSVhNRVxuICAgICAgY29uc3QgdHJhbnNmb3JtUHJvcHMgPSBwcm9wczJ0cmFuc2Zvcm0odHJhbnNmb3JtKTtcbiAgICAgIHRyYW5zZm9ybVByb3BzICYmIGFwcGVuZFRyYW5zZm9ybVByb3BzKHRyYW5zZm9ybVByb3BzKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuICgwLCBfTWF0cml4MkQudG9BcnJheSkoKTtcbn1cbmZ1bmN0aW9uIGV4dHJhY3RUcmFuc2Zvcm0ocHJvcHMpIHtcbiAgaWYgKEFycmF5LmlzQXJyYXkocHJvcHMpICYmIHR5cGVvZiBwcm9wc1swXSA9PT0gJ251bWJlcicpIHtcbiAgICByZXR1cm4gcHJvcHM7XG4gIH1cbiAgaWYgKHR5cGVvZiBwcm9wcyA9PT0gJ3N0cmluZycpIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdCA9ICgwLCBfdHJhbnNmb3JtLnBhcnNlKShwcm9wcyk7XG4gICAgICByZXR1cm4gW3RbMF0sIHRbM10sIHRbMV0sIHRbNF0sIHRbMl0sIHRbNV1dO1xuICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoZSk7XG4gICAgICByZXR1cm4gX01hdHJpeDJELmlkZW50aXR5O1xuICAgIH1cbiAgfVxuICAvLyB0aGlzIHR5cGUgaXMgbm90IGNvcnJlY3Qgc2luY2UgcHJvcHMgY2FuIGJlIG9mIHR5cGUgVHJhbnNmb3Jtc1N0eWxlWyd0cmFuc2Zvcm0nXSB0b29cbiAgLy8gYnV0IGl0IHNhdGlzZmllcyBUUyBhbmQgc2hvdWxkIG5vdCBwcm9kdWNlIGFueSB0eXBlIGVycm9yc1xuICBjb25zdCB0cmFuc2Zvcm1Qcm9wcyA9IHByb3BzO1xuICByZXR1cm4gdHJhbnNmb3JtVG9NYXRyaXgocHJvcHMydHJhbnNmb3JtKHRyYW5zZm9ybVByb3BzKSwgdHJhbnNmb3JtUHJvcHMgPT09IG51bGwgfHwgdHJhbnNmb3JtUHJvcHMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHRyYW5zZm9ybVByb3BzLnRyYW5zZm9ybSk7XG59XG5mdW5jdGlvbiBleHRyYWN0VHJhbnNmb3JtU3ZnVmlldyhwcm9wcykge1xuICBpZiAodHlwZW9mIHByb3BzLnRyYW5zZm9ybSA9PT0gJ3N0cmluZycpIHtcbiAgICByZXR1cm4gKDAsIF90cmFuc2Zvcm1Ub1JuLnBhcnNlKShwcm9wcy50cmFuc2Zvcm0pO1xuICB9XG4gIHJldHVybiBwcm9wcy50cmFuc2Zvcm07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1leHRyYWN0VHJhbnNmb3JtLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/lib/extract/extractTransform.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/lib/extract/transform.js":
/*!*********************************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/lib/extract/transform.js ***!
  \*********************************************************************************/
/***/ ((module) => {

eval("/*\n * Generated by PEG.js 0.10.0.\n *\n * http://pegjs.org/\n */\n\n\n\nfunction peg$subclass(child, parent) {\n  function ctor() {\n    this.constructor = child;\n  }\n  ctor.prototype = parent.prototype;\n  child.prototype = new ctor();\n}\nfunction peg$SyntaxError(message, expected, found, location) {\n  this.message = message;\n  this.expected = expected;\n  this.found = found;\n  this.location = location;\n  this.name = \"SyntaxError\";\n  if (typeof Error.captureStackTrace === \"function\") {\n    Error.captureStackTrace(this, peg$SyntaxError);\n  }\n}\npeg$subclass(peg$SyntaxError, Error);\npeg$SyntaxError.buildMessage = function (expected, found) {\n  var DESCRIBE_EXPECTATION_FNS = {\n    literal: function (expectation) {\n      return \"\\\"\" + literalEscape(expectation.text) + \"\\\"\";\n    },\n    \"class\": function (expectation) {\n      var escapedParts = \"\",\n        i;\n      for (i = 0; i < expectation.parts.length; i++) {\n        escapedParts += expectation.parts[i] instanceof Array ? classEscape(expectation.parts[i][0]) + \"-\" + classEscape(expectation.parts[i][1]) : classEscape(expectation.parts[i]);\n      }\n      return \"[\" + (expectation.inverted ? \"^\" : \"\") + escapedParts + \"]\";\n    },\n    any: function (expectation) {\n      return \"any character\";\n    },\n    end: function (expectation) {\n      return \"end of input\";\n    },\n    other: function (expectation) {\n      return expectation.description;\n    }\n  };\n  function hex(ch) {\n    return ch.charCodeAt(0).toString(16).toUpperCase();\n  }\n  function literalEscape(s) {\n    return s.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"').replace(/\\0/g, '\\\\0').replace(/\\t/g, '\\\\t').replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r').replace(/[\\x00-\\x0F]/g, function (ch) {\n      return '\\\\x0' + hex(ch);\n    }).replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function (ch) {\n      return '\\\\x' + hex(ch);\n    });\n  }\n  function classEscape(s) {\n    return s.replace(/\\\\/g, '\\\\\\\\').replace(/\\]/g, '\\\\]').replace(/\\^/g, '\\\\^').replace(/-/g, '\\\\-').replace(/\\0/g, '\\\\0').replace(/\\t/g, '\\\\t').replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r').replace(/[\\x00-\\x0F]/g, function (ch) {\n      return '\\\\x0' + hex(ch);\n    }).replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function (ch) {\n      return '\\\\x' + hex(ch);\n    });\n  }\n  function describeExpectation(expectation) {\n    return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n  }\n  function describeExpected(expected) {\n    var descriptions = new Array(expected.length),\n      i,\n      j;\n    for (i = 0; i < expected.length; i++) {\n      descriptions[i] = describeExpectation(expected[i]);\n    }\n    descriptions.sort();\n    if (descriptions.length > 0) {\n      for (i = 1, j = 1; i < descriptions.length; i++) {\n        if (descriptions[i - 1] !== descriptions[i]) {\n          descriptions[j] = descriptions[i];\n          j++;\n        }\n      }\n      descriptions.length = j;\n    }\n    switch (descriptions.length) {\n      case 1:\n        return descriptions[0];\n      case 2:\n        return descriptions[0] + \" or \" + descriptions[1];\n      default:\n        return descriptions.slice(0, -1).join(\", \") + \", or \" + descriptions[descriptions.length - 1];\n    }\n  }\n  function describeFound(found) {\n    return found ? \"\\\"\" + literalEscape(found) + \"\\\"\" : \"end of input\";\n  }\n  return \"Expected \" + describeExpected(expected) + \" but \" + describeFound(found) + \" found.\";\n};\nfunction peg$parse(input, options) {\n  options = options !== void 0 ? options : {};\n  var peg$FAILED = {},\n    peg$startRuleFunctions = {\n      transformList: peg$parsetransformList\n    },\n    peg$startRuleFunction = peg$parsetransformList,\n    peg$c0 = function (ts) {\n      return ts;\n    },\n    peg$c1 = function (t, ts) {\n      return multiply_matrices(t, ts);\n    },\n    peg$c2 = \"matrix\",\n    peg$c3 = peg$literalExpectation(\"matrix\", false),\n    peg$c4 = \"(\",\n    peg$c5 = peg$literalExpectation(\"(\", false),\n    peg$c6 = \")\",\n    peg$c7 = peg$literalExpectation(\")\", false),\n    peg$c8 = function (a, b, c, d, e, f) {\n      return [a, c, e, b, d, f];\n    },\n    peg$c9 = \"translate\",\n    peg$c10 = peg$literalExpectation(\"translate\", false),\n    peg$c11 = function (tx, ty) {\n      return [1, 0, tx, 0, 1, ty || 0];\n    },\n    peg$c12 = \"scale\",\n    peg$c13 = peg$literalExpectation(\"scale\", false),\n    peg$c14 = function (sx, sy) {\n      return [sx, 0, 0, 0, sy === null ? sx : sy, 0];\n    },\n    peg$c15 = \"rotate\",\n    peg$c16 = peg$literalExpectation(\"rotate\", false),\n    peg$c17 = function (angle, c) {\n      var cos = Math.cos(deg2rad * angle);\n      var sin = Math.sin(deg2rad * angle);\n      if (c !== null) {\n        var x = c[0];\n        var y = c[1];\n        return [cos, -sin, cos * -x + -sin * -y + x, sin, cos, sin * -x + cos * -y + y];\n      }\n      return [cos, -sin, 0, sin, cos, 0];\n    },\n    peg$c18 = \"skewX\",\n    peg$c19 = peg$literalExpectation(\"skewX\", false),\n    peg$c20 = function (angle) {\n      return [1, Math.tan(deg2rad * angle), 0, 0, 1, 0];\n    },\n    peg$c21 = \"skewY\",\n    peg$c22 = peg$literalExpectation(\"skewY\", false),\n    peg$c23 = function (angle) {\n      return [1, 0, 0, Math.tan(deg2rad * angle), 1, 0];\n    },\n    peg$c24 = function (f) {\n      return parseFloat(f.join(\"\"));\n    },\n    peg$c25 = function (i) {\n      return parseInt(i.join(\"\"));\n    },\n    peg$c26 = function (n) {\n      return n;\n    },\n    peg$c27 = function (n1, n2) {\n      return [n1, n2];\n    },\n    peg$c28 = \",\",\n    peg$c29 = peg$literalExpectation(\",\", false),\n    peg$c30 = function (ds) {\n      return ds.join(\"\");\n    },\n    peg$c31 = function (f) {\n      return f.join(\"\");\n    },\n    peg$c32 = function (d) {\n      return d.join(\"\");\n    },\n    peg$c33 = peg$otherExpectation(\"fractionalConstant\"),\n    peg$c34 = \".\",\n    peg$c35 = peg$literalExpectation(\".\", false),\n    peg$c36 = function (d1, d2) {\n      return [d1 ? d1.join(\"\") : null, \".\", d2.join(\"\")].join(\"\");\n    },\n    peg$c37 = /^[eE]/,\n    peg$c38 = peg$classExpectation([\"e\", \"E\"], false, false),\n    peg$c39 = function (e) {\n      return [e[0], e[1], e[2].join(\"\")].join(\"\");\n    },\n    peg$c40 = /^[+\\-]/,\n    peg$c41 = peg$classExpectation([\"+\", \"-\"], false, false),\n    peg$c42 = /^[0-9]/,\n    peg$c43 = peg$classExpectation([[\"0\", \"9\"]], false, false),\n    peg$c44 = /^[ \\t\\r\\n]/,\n    peg$c45 = peg$classExpectation([\" \", \"\\t\", \"\\r\", \"\\n\"], false, false),\n    peg$currPos = 0,\n    peg$savedPos = 0,\n    peg$posDetailsCache = [{\n      line: 1,\n      column: 1\n    }],\n    peg$maxFailPos = 0,\n    peg$maxFailExpected = [],\n    peg$silentFails = 0,\n    peg$result;\n  if (\"startRule\" in options) {\n    if (!(options.startRule in peg$startRuleFunctions)) {\n      throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n    }\n    peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n  }\n  function text() {\n    return input.substring(peg$savedPos, peg$currPos);\n  }\n  function location() {\n    return peg$computeLocation(peg$savedPos, peg$currPos);\n  }\n  function expected(description, location) {\n    location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos);\n    throw peg$buildStructuredError([peg$otherExpectation(description)], input.substring(peg$savedPos, peg$currPos), location);\n  }\n  function error(message, location) {\n    location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos);\n    throw peg$buildSimpleError(message, location);\n  }\n  function peg$literalExpectation(text, ignoreCase) {\n    return {\n      type: \"literal\",\n      text: text,\n      ignoreCase: ignoreCase\n    };\n  }\n  function peg$classExpectation(parts, inverted, ignoreCase) {\n    return {\n      type: \"class\",\n      parts: parts,\n      inverted: inverted,\n      ignoreCase: ignoreCase\n    };\n  }\n  function peg$anyExpectation() {\n    return {\n      type: \"any\"\n    };\n  }\n  function peg$endExpectation() {\n    return {\n      type: \"end\"\n    };\n  }\n  function peg$otherExpectation(description) {\n    return {\n      type: \"other\",\n      description: description\n    };\n  }\n  function peg$computePosDetails(pos) {\n    var details = peg$posDetailsCache[pos],\n      p;\n    if (details) {\n      return details;\n    } else {\n      p = pos - 1;\n      while (!peg$posDetailsCache[p]) {\n        p--;\n      }\n      details = peg$posDetailsCache[p];\n      details = {\n        line: details.line,\n        column: details.column\n      };\n      while (p < pos) {\n        if (input.charCodeAt(p) === 10) {\n          details.line++;\n          details.column = 1;\n        } else {\n          details.column++;\n        }\n        p++;\n      }\n      peg$posDetailsCache[pos] = details;\n      return details;\n    }\n  }\n  function peg$computeLocation(startPos, endPos) {\n    var startPosDetails = peg$computePosDetails(startPos),\n      endPosDetails = peg$computePosDetails(endPos);\n    return {\n      start: {\n        offset: startPos,\n        line: startPosDetails.line,\n        column: startPosDetails.column\n      },\n      end: {\n        offset: endPos,\n        line: endPosDetails.line,\n        column: endPosDetails.column\n      }\n    };\n  }\n  function peg$fail(expected) {\n    if (peg$currPos < peg$maxFailPos) {\n      return;\n    }\n    if (peg$currPos > peg$maxFailPos) {\n      peg$maxFailPos = peg$currPos;\n      peg$maxFailExpected = [];\n    }\n    peg$maxFailExpected.push(expected);\n  }\n  function peg$buildSimpleError(message, location) {\n    return new peg$SyntaxError(message, null, null, location);\n  }\n  function peg$buildStructuredError(expected, found, location) {\n    return new peg$SyntaxError(peg$SyntaxError.buildMessage(expected, found), expected, found, location);\n  }\n  function peg$parsetransformList() {\n    var s0, s1, s2, s3, s4;\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = peg$parsewsp();\n    while (s2 !== peg$FAILED) {\n      s1.push(s2);\n      s2 = peg$parsewsp();\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsetransforms();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = [];\n        s4 = peg$parsewsp();\n        while (s4 !== peg$FAILED) {\n          s3.push(s4);\n          s4 = peg$parsewsp();\n        }\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c0(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsetransforms() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    s1 = peg$parsetransform();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsecommaWsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsecommaWsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsetransforms();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c1(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    if (s0 === peg$FAILED) {\n      s0 = peg$parsetransform();\n    }\n    return s0;\n  }\n  function peg$parsetransform() {\n    var s0;\n    s0 = peg$parsematrix();\n    if (s0 === peg$FAILED) {\n      s0 = peg$parsetranslate();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parsescale();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parserotate();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parseskewX();\n            if (s0 === peg$FAILED) {\n              s0 = peg$parseskewY();\n            }\n          }\n        }\n      }\n    }\n    return s0;\n  }\n  function peg$parsematrix() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10, s11, s12, s13, s14, s15, s16, s17;\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 6) === peg$c2) {\n      s1 = peg$c2;\n      peg$currPos += 6;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c3);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c5);\n          }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsecommaWsp();\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parsenumber();\n                if (s7 !== peg$FAILED) {\n                  s8 = peg$parsecommaWsp();\n                  if (s8 !== peg$FAILED) {\n                    s9 = peg$parsenumber();\n                    if (s9 !== peg$FAILED) {\n                      s10 = peg$parsecommaWsp();\n                      if (s10 !== peg$FAILED) {\n                        s11 = peg$parsenumber();\n                        if (s11 !== peg$FAILED) {\n                          s12 = peg$parsecommaWsp();\n                          if (s12 !== peg$FAILED) {\n                            s13 = peg$parsenumber();\n                            if (s13 !== peg$FAILED) {\n                              s14 = peg$parsecommaWsp();\n                              if (s14 !== peg$FAILED) {\n                                s15 = peg$parsenumber();\n                                if (s15 !== peg$FAILED) {\n                                  s16 = [];\n                                  s17 = peg$parsewsp();\n                                  while (s17 !== peg$FAILED) {\n                                    s16.push(s17);\n                                    s17 = peg$parsewsp();\n                                  }\n                                  if (s16 !== peg$FAILED) {\n                                    if (input.charCodeAt(peg$currPos) === 41) {\n                                      s17 = peg$c6;\n                                      peg$currPos++;\n                                    } else {\n                                      s17 = peg$FAILED;\n                                      if (peg$silentFails === 0) {\n                                        peg$fail(peg$c7);\n                                      }\n                                    }\n                                    if (s17 !== peg$FAILED) {\n                                      peg$savedPos = s0;\n                                      s1 = peg$c8(s5, s7, s9, s11, s13, s15);\n                                      s0 = s1;\n                                    } else {\n                                      peg$currPos = s0;\n                                      s0 = peg$FAILED;\n                                    }\n                                  } else {\n                                    peg$currPos = s0;\n                                    s0 = peg$FAILED;\n                                  }\n                                } else {\n                                  peg$currPos = s0;\n                                  s0 = peg$FAILED;\n                                }\n                              } else {\n                                peg$currPos = s0;\n                                s0 = peg$FAILED;\n                              }\n                            } else {\n                              peg$currPos = s0;\n                              s0 = peg$FAILED;\n                            }\n                          } else {\n                            peg$currPos = s0;\n                            s0 = peg$FAILED;\n                          }\n                        } else {\n                          peg$currPos = s0;\n                          s0 = peg$FAILED;\n                        }\n                      } else {\n                        peg$currPos = s0;\n                        s0 = peg$FAILED;\n                      }\n                    } else {\n                      peg$currPos = s0;\n                      s0 = peg$FAILED;\n                    }\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsetranslate() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8;\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 9) === peg$c9) {\n      s1 = peg$c9;\n      peg$currPos += 9;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c10);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c5);\n          }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsecommaWspNumber();\n              if (s6 === peg$FAILED) {\n                s6 = null;\n              }\n              if (s6 !== peg$FAILED) {\n                s7 = [];\n                s8 = peg$parsewsp();\n                while (s8 !== peg$FAILED) {\n                  s7.push(s8);\n                  s8 = peg$parsewsp();\n                }\n                if (s7 !== peg$FAILED) {\n                  if (input.charCodeAt(peg$currPos) === 41) {\n                    s8 = peg$c6;\n                    peg$currPos++;\n                  } else {\n                    s8 = peg$FAILED;\n                    if (peg$silentFails === 0) {\n                      peg$fail(peg$c7);\n                    }\n                  }\n                  if (s8 !== peg$FAILED) {\n                    peg$savedPos = s0;\n                    s1 = peg$c11(s5, s6);\n                    s0 = s1;\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsescale() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8;\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 5) === peg$c12) {\n      s1 = peg$c12;\n      peg$currPos += 5;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c13);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c5);\n          }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsecommaWspNumber();\n              if (s6 === peg$FAILED) {\n                s6 = null;\n              }\n              if (s6 !== peg$FAILED) {\n                s7 = [];\n                s8 = peg$parsewsp();\n                while (s8 !== peg$FAILED) {\n                  s7.push(s8);\n                  s8 = peg$parsewsp();\n                }\n                if (s7 !== peg$FAILED) {\n                  if (input.charCodeAt(peg$currPos) === 41) {\n                    s8 = peg$c6;\n                    peg$currPos++;\n                  } else {\n                    s8 = peg$FAILED;\n                    if (peg$silentFails === 0) {\n                      peg$fail(peg$c7);\n                    }\n                  }\n                  if (s8 !== peg$FAILED) {\n                    peg$savedPos = s0;\n                    s1 = peg$c14(s5, s6);\n                    s0 = s1;\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parserotate() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8;\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 6) === peg$c15) {\n      s1 = peg$c15;\n      peg$currPos += 6;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c16);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c5);\n          }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsecommaWspTwoNumbers();\n              if (s6 === peg$FAILED) {\n                s6 = null;\n              }\n              if (s6 !== peg$FAILED) {\n                s7 = [];\n                s8 = peg$parsewsp();\n                while (s8 !== peg$FAILED) {\n                  s7.push(s8);\n                  s8 = peg$parsewsp();\n                }\n                if (s7 !== peg$FAILED) {\n                  if (input.charCodeAt(peg$currPos) === 41) {\n                    s8 = peg$c6;\n                    peg$currPos++;\n                  } else {\n                    s8 = peg$FAILED;\n                    if (peg$silentFails === 0) {\n                      peg$fail(peg$c7);\n                    }\n                  }\n                  if (s8 !== peg$FAILED) {\n                    peg$savedPos = s0;\n                    s1 = peg$c17(s5, s6);\n                    s0 = s1;\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parseskewX() {\n    var s0, s1, s2, s3, s4, s5, s6, s7;\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 5) === peg$c18) {\n      s1 = peg$c18;\n      peg$currPos += 5;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c19);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c5);\n          }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = [];\n              s7 = peg$parsewsp();\n              while (s7 !== peg$FAILED) {\n                s6.push(s7);\n                s7 = peg$parsewsp();\n              }\n              if (s6 !== peg$FAILED) {\n                if (input.charCodeAt(peg$currPos) === 41) {\n                  s7 = peg$c6;\n                  peg$currPos++;\n                } else {\n                  s7 = peg$FAILED;\n                  if (peg$silentFails === 0) {\n                    peg$fail(peg$c7);\n                  }\n                }\n                if (s7 !== peg$FAILED) {\n                  peg$savedPos = s0;\n                  s1 = peg$c20(s5);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parseskewY() {\n    var s0, s1, s2, s3, s4, s5, s6, s7;\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 5) === peg$c21) {\n      s1 = peg$c21;\n      peg$currPos += 5;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c22);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c5);\n          }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = [];\n              s7 = peg$parsewsp();\n              while (s7 !== peg$FAILED) {\n                s6.push(s7);\n                s7 = peg$parsewsp();\n              }\n              if (s6 !== peg$FAILED) {\n                if (input.charCodeAt(peg$currPos) === 41) {\n                  s7 = peg$c6;\n                  peg$currPos++;\n                } else {\n                  s7 = peg$FAILED;\n                  if (peg$silentFails === 0) {\n                    peg$fail(peg$c7);\n                  }\n                }\n                if (s7 !== peg$FAILED) {\n                  peg$savedPos = s0;\n                  s1 = peg$c23(s5);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsenumber() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    s2 = peg$parsesign();\n    if (s2 === peg$FAILED) {\n      s2 = null;\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parsefloatingPointConstant();\n      if (s3 !== peg$FAILED) {\n        s2 = [s2, s3];\n        s1 = s2;\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c24(s1);\n    }\n    s0 = s1;\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = peg$parsesign();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parseintegerConstant();\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c25(s1);\n      }\n      s0 = s1;\n    }\n    return s0;\n  }\n  function peg$parsecommaWspNumber() {\n    var s0, s1, s2;\n    s0 = peg$currPos;\n    s1 = peg$parsecommaWsp();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsenumber();\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c26(s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsecommaWspTwoNumbers() {\n    var s0, s1, s2, s3, s4;\n    s0 = peg$currPos;\n    s1 = peg$parsecommaWsp();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsenumber();\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecommaWsp();\n        if (s3 !== peg$FAILED) {\n          s4 = peg$parsenumber();\n          if (s4 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c27(s2, s4);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsecommaWsp() {\n    var s0, s1, s2, s3, s4;\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = peg$parsewsp();\n    if (s2 !== peg$FAILED) {\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = peg$parsewsp();\n      }\n    } else {\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = [];\n        s4 = peg$parsewsp();\n        while (s4 !== peg$FAILED) {\n          s3.push(s4);\n          s4 = peg$parsewsp();\n        }\n        if (s3 !== peg$FAILED) {\n          s1 = [s1, s2, s3];\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$parsecomma();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parsewsp();\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$parsewsp();\n        }\n        if (s2 !== peg$FAILED) {\n          s1 = [s1, s2];\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    }\n    return s0;\n  }\n  function peg$parsecomma() {\n    var s0;\n    if (input.charCodeAt(peg$currPos) === 44) {\n      s0 = peg$c28;\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c29);\n      }\n    }\n    return s0;\n  }\n  function peg$parseintegerConstant() {\n    var s0, s1;\n    s0 = peg$currPos;\n    s1 = peg$parsedigitSequence();\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c30(s1);\n    }\n    s0 = s1;\n    return s0;\n  }\n  function peg$parsefloatingPointConstant() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    s2 = peg$parsefractionalConstant();\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parseexponent();\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      if (s3 !== peg$FAILED) {\n        s2 = [s2, s3];\n        s1 = s2;\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c31(s1);\n    }\n    s0 = s1;\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = peg$parsedigitSequence();\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parseexponent();\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c32(s1);\n      }\n      s0 = s1;\n    }\n    return s0;\n  }\n  function peg$parsefractionalConstant() {\n    var s0, s1, s2, s3;\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$parsedigitSequence();\n    if (s1 === peg$FAILED) {\n      s1 = null;\n    }\n    if (s1 !== peg$FAILED) {\n      if (input.charCodeAt(peg$currPos) === 46) {\n        s2 = peg$c34;\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) {\n          peg$fail(peg$c35);\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsedigitSequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c36(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$parsedigitSequence();\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s2 = peg$c34;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c35);\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c32(s1);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c33);\n      }\n    }\n    return s0;\n  }\n  function peg$parseexponent() {\n    var s0, s1, s2, s3, s4;\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    if (peg$c37.test(input.charAt(peg$currPos))) {\n      s2 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c38);\n      }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parsesign();\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      if (s3 !== peg$FAILED) {\n        s4 = peg$parsedigitSequence();\n        if (s4 !== peg$FAILED) {\n          s2 = [s2, s3, s4];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c39(s1);\n    }\n    s0 = s1;\n    return s0;\n  }\n  function peg$parsesign() {\n    var s0;\n    if (peg$c40.test(input.charAt(peg$currPos))) {\n      s0 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c41);\n      }\n    }\n    return s0;\n  }\n  function peg$parsedigitSequence() {\n    var s0, s1;\n    s0 = [];\n    s1 = peg$parsedigit();\n    if (s1 !== peg$FAILED) {\n      while (s1 !== peg$FAILED) {\n        s0.push(s1);\n        s1 = peg$parsedigit();\n      }\n    } else {\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsedigit() {\n    var s0;\n    if (peg$c42.test(input.charAt(peg$currPos))) {\n      s0 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c43);\n      }\n    }\n    return s0;\n  }\n  function peg$parsewsp() {\n    var s0;\n    if (peg$c44.test(input.charAt(peg$currPos))) {\n      s0 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c45);\n      }\n    }\n    return s0;\n  }\n  var deg2rad = Math.PI / 180;\n\n  /*\n   ╔═        ═╗   ╔═        ═╗   ╔═     ═╗\n   ║ al cl el ║   ║ ar cr er ║   ║ a c e ║\n   ║ bl dl fl ║ * ║ br dr fr ║ = ║ b d f ║\n   ║ 0  0  1  ║   ║ 0  0  1  ║   ║ 0 0 1 ║\n   ╚═        ═╝   ╚═        ═╝   ╚═     ═╝\n  */\n  function multiply_matrices(l, r) {\n    var al = l[0];\n    var cl = l[1];\n    var el = l[2];\n    var bl = l[3];\n    var dl = l[4];\n    var fl = l[5];\n    var ar = r[0];\n    var cr = r[1];\n    var er = r[2];\n    var br = r[3];\n    var dr = r[4];\n    var fr = r[5];\n    var a = al * ar + cl * br;\n    var c = al * cr + cl * dr;\n    var e = al * er + cl * fr + el;\n    var b = bl * ar + dl * br;\n    var d = bl * cr + dl * dr;\n    var f = bl * er + dl * fr + fl;\n    return [a, c, e, b, d, f];\n  }\n  peg$result = peg$startRuleFunction();\n  if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n    return peg$result;\n  } else {\n    if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n      peg$fail(peg$endExpectation());\n    }\n    throw peg$buildStructuredError(peg$maxFailExpected, peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null, peg$maxFailPos < input.length ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1) : peg$computeLocation(peg$maxFailPos, peg$maxFailPos));\n  }\n}\nmodule.exports = {\n  SyntaxError: peg$SyntaxError,\n  parse: peg$parse\n};\n//# sourceMappingURL=transform.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/lib/extract/transform.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/lib/extract/transformToRn.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/lib/extract/transformToRn.js ***!
  \*************************************************************************************/
/***/ ((module) => {

eval("// @generated by Peggy 4.0.3.\n//\n// https://peggyjs.org/\n\n\n\nfunction peg$subclass(child, parent) {\n  function C() {\n    this.constructor = child;\n  }\n  C.prototype = parent.prototype;\n  child.prototype = new C();\n}\nfunction peg$SyntaxError(message, expected, found, location) {\n  var self = Error.call(this, message);\n  // istanbul ignore next Check is a necessary evil to support older environments\n  if (Object.setPrototypeOf) {\n    Object.setPrototypeOf(self, peg$SyntaxError.prototype);\n  }\n  self.expected = expected;\n  self.found = found;\n  self.location = location;\n  self.name = 'SyntaxError';\n  return self;\n}\npeg$subclass(peg$SyntaxError, Error);\nfunction peg$padEnd(str, targetLength, padString) {\n  padString = padString || ' ';\n  if (str.length > targetLength) {\n    return str;\n  }\n  targetLength -= str.length;\n  padString += padString.repeat(targetLength);\n  return str + padString.slice(0, targetLength);\n}\npeg$SyntaxError.prototype.format = function (sources) {\n  var str = 'Error: ' + this.message;\n  if (this.location) {\n    var src = null;\n    var k;\n    for (k = 0; k < sources.length; k++) {\n      if (sources[k].source === this.location.source) {\n        src = sources[k].text.split(/\\r\\n|\\n|\\r/g);\n        break;\n      }\n    }\n    var s = this.location.start;\n    var offset_s = this.location.source && typeof this.location.source.offset === 'function' ? this.location.source.offset(s) : s;\n    var loc = this.location.source + ':' + offset_s.line + ':' + offset_s.column;\n    if (src) {\n      var e = this.location.end;\n      var filler = peg$padEnd('', offset_s.line.toString().length, ' ');\n      var line = src[s.line - 1];\n      var last = s.line === e.line ? e.column : line.length + 1;\n      var hatLen = last - s.column || 1;\n      str += '\\n --> ' + loc + '\\n' + filler + ' |\\n' + offset_s.line + ' | ' + line + '\\n' + filler + ' | ' + peg$padEnd('', s.column - 1, ' ') + peg$padEnd('', hatLen, '^');\n    } else {\n      str += '\\n at ' + loc;\n    }\n  }\n  return str;\n};\npeg$SyntaxError.buildMessage = function (expected, found) {\n  var DESCRIBE_EXPECTATION_FNS = {\n    literal: function (expectation) {\n      return '\"' + literalEscape(expectation.text) + '\"';\n    },\n    class: function (expectation) {\n      var escapedParts = expectation.parts.map(function (part) {\n        return Array.isArray(part) ? classEscape(part[0]) + '-' + classEscape(part[1]) : classEscape(part);\n      });\n      return '[' + (expectation.inverted ? '^' : '') + escapedParts.join('') + ']';\n    },\n    any: function () {\n      return 'any character';\n    },\n    end: function () {\n      return 'end of input';\n    },\n    other: function (expectation) {\n      return expectation.description;\n    }\n  };\n  function hex(ch) {\n    return ch.charCodeAt(0).toString(16).toUpperCase();\n  }\n  function literalEscape(s) {\n    return s.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"').replace(/\\0/g, '\\\\0').replace(/\\t/g, '\\\\t').replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r').replace(/[\\x00-\\x0F]/g, function (ch) {\n      return '\\\\x0' + hex(ch);\n    }).replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function (ch) {\n      return '\\\\x' + hex(ch);\n    });\n  }\n  function classEscape(s) {\n    return s.replace(/\\\\/g, '\\\\\\\\').replace(/\\]/g, '\\\\]').replace(/\\^/g, '\\\\^').replace(/-/g, '\\\\-').replace(/\\0/g, '\\\\0').replace(/\\t/g, '\\\\t').replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r').replace(/[\\x00-\\x0F]/g, function (ch) {\n      return '\\\\x0' + hex(ch);\n    }).replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function (ch) {\n      return '\\\\x' + hex(ch);\n    });\n  }\n  function describeExpectation(expectation) {\n    return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n  }\n  function describeExpected(expected) {\n    var descriptions = expected.map(describeExpectation);\n    var i, j;\n    descriptions.sort();\n    if (descriptions.length > 0) {\n      for (i = 1, j = 1; i < descriptions.length; i++) {\n        if (descriptions[i - 1] !== descriptions[i]) {\n          descriptions[j] = descriptions[i];\n          j++;\n        }\n      }\n      descriptions.length = j;\n    }\n    switch (descriptions.length) {\n      case 1:\n        return descriptions[0];\n      case 2:\n        return descriptions[0] + ' or ' + descriptions[1];\n      default:\n        return descriptions.slice(0, -1).join(', ') + ', or ' + descriptions[descriptions.length - 1];\n    }\n  }\n  function describeFound(found) {\n    return found ? '\"' + literalEscape(found) + '\"' : 'end of input';\n  }\n  return 'Expected ' + describeExpected(expected) + ' but ' + describeFound(found) + ' found.';\n};\nfunction peg$parse(input, options) {\n  options = options !== undefined ? options : {};\n  var peg$FAILED = {};\n  var peg$source = options.grammarSource;\n  var peg$startRuleFunctions = {\n    start: peg$parsestart\n  };\n  var peg$startRuleFunction = peg$parsestart;\n  var peg$c0 = 'matrix(';\n  var peg$c1 = ')';\n  var peg$c2 = 'translate(';\n  var peg$c3 = 'scale(';\n  var peg$c4 = 'rotate(';\n  var peg$c5 = 'skewX(';\n  var peg$c6 = 'skewY(';\n  var peg$c7 = '.';\n  var peg$c8 = 'e';\n  var peg$r0 = /^[ \\t\\n\\r,]/;\n  var peg$r1 = /^[ \\t\\n\\r]/;\n  var peg$r2 = /^[+\\-]/;\n  var peg$r3 = /^[0-9]/;\n  var peg$e0 = peg$otherExpectation('transform functions');\n  var peg$e1 = peg$otherExpectation('transformFunctions');\n  var peg$e2 = peg$otherExpectation('transform function');\n  var peg$e3 = peg$otherExpectation('matrix');\n  var peg$e4 = peg$literalExpectation('matrix(', false);\n  var peg$e5 = peg$literalExpectation(')', false);\n  var peg$e6 = peg$otherExpectation('translate');\n  var peg$e7 = peg$literalExpectation('translate(', false);\n  var peg$e8 = peg$otherExpectation('scale');\n  var peg$e9 = peg$literalExpectation('scale(', false);\n  var peg$e10 = peg$otherExpectation('rotate');\n  var peg$e11 = peg$literalExpectation('rotate(', false);\n  var peg$e12 = peg$otherExpectation('x, y');\n  var peg$e13 = peg$otherExpectation('skewX');\n  var peg$e14 = peg$literalExpectation('skewX(', false);\n  var peg$e15 = peg$otherExpectation('skewY');\n  var peg$e16 = peg$literalExpectation('skewY(', false);\n  var peg$e17 = peg$otherExpectation('space or comma');\n  var peg$e18 = peg$classExpectation([' ', '\\t', '\\n', '\\r', ','], false, false);\n  var peg$e19 = peg$otherExpectation('whitespace');\n  var peg$e20 = peg$classExpectation([' ', '\\t', '\\n', '\\r'], false, false);\n  var peg$e21 = peg$classExpectation(['+', '-'], false, false);\n  var peg$e22 = peg$classExpectation([['0', '9']], false, false);\n  var peg$e23 = peg$literalExpectation('.', false);\n  var peg$e24 = peg$literalExpectation('e', false);\n  var peg$f0 = function (head, tail) {\n    const results = Array.isArray(head) ? head : [head];\n    tail.forEach(element => {\n      if (Array.isArray(element[1])) {\n        results.push(...element[1]);\n      } else {\n        results.push(element[1]);\n      }\n    });\n    return results;\n  };\n  var peg$f1 = function (a, b, c, d, e, f, g, h, i) {\n    return {\n      matrix: [a, b, c, d, e, f, g, h, i]\n    };\n  };\n  var peg$f2 = function (x, y) {\n    if (y == undefined) {\n      return {\n        translate: x\n      };\n    }\n    return {\n      translate: [x, y]\n    };\n  };\n  var peg$f3 = function (x, y) {\n    if (y == undefined) {\n      return {\n        scale: x\n      };\n    }\n    return [{\n      scaleX: x\n    }, {\n      scaleY: y\n    }];\n  };\n  var peg$f4 = function (x, yz) {\n    if (yz !== null) {\n      return {\n        rotate: `${x}deg`\n      };\n    }\n    return [{\n      rotate: `${x}deg`\n    }];\n  };\n  var peg$f5 = function (y, z) {\n    return [y, z];\n  };\n  var peg$f6 = function (x) {\n    return [{\n      skewX: `${x}deg`\n    }];\n  };\n  var peg$f7 = function (y) {\n    return [{\n      skewY: `${y}deg`\n    }];\n  };\n  var peg$f8 = function () {\n    return parseFloat(text());\n  };\n  var peg$currPos = options.peg$currPos | 0;\n  var peg$savedPos = peg$currPos;\n  var peg$posDetailsCache = [{\n    line: 1,\n    column: 1\n  }];\n  var peg$maxFailPos = peg$currPos;\n  var peg$maxFailExpected = options.peg$maxFailExpected || [];\n  var peg$silentFails = options.peg$silentFails | 0;\n  var peg$result;\n  if (options.startRule) {\n    if (!(options.startRule in peg$startRuleFunctions)) {\n      throw new Error('Can\\'t start parsing from rule \"' + options.startRule + '\".');\n    }\n    peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n  }\n  function text() {\n    return input.substring(peg$savedPos, peg$currPos);\n  }\n  function offset() {\n    return peg$savedPos;\n  }\n  function range() {\n    return {\n      source: peg$source,\n      start: peg$savedPos,\n      end: peg$currPos\n    };\n  }\n  function location() {\n    return peg$computeLocation(peg$savedPos, peg$currPos);\n  }\n  function expected(description, location) {\n    location = location !== undefined ? location : peg$computeLocation(peg$savedPos, peg$currPos);\n    throw peg$buildStructuredError([peg$otherExpectation(description)], input.substring(peg$savedPos, peg$currPos), location);\n  }\n  function error(message, location) {\n    location = location !== undefined ? location : peg$computeLocation(peg$savedPos, peg$currPos);\n    throw peg$buildSimpleError(message, location);\n  }\n  function peg$literalExpectation(text, ignoreCase) {\n    return {\n      type: 'literal',\n      text: text,\n      ignoreCase: ignoreCase\n    };\n  }\n  function peg$classExpectation(parts, inverted, ignoreCase) {\n    return {\n      type: 'class',\n      parts: parts,\n      inverted: inverted,\n      ignoreCase: ignoreCase\n    };\n  }\n  function peg$anyExpectation() {\n    return {\n      type: 'any'\n    };\n  }\n  function peg$endExpectation() {\n    return {\n      type: 'end'\n    };\n  }\n  function peg$otherExpectation(description) {\n    return {\n      type: 'other',\n      description: description\n    };\n  }\n  function peg$computePosDetails(pos) {\n    var details = peg$posDetailsCache[pos];\n    var p;\n    if (details) {\n      return details;\n    } else {\n      if (pos >= peg$posDetailsCache.length) {\n        p = peg$posDetailsCache.length - 1;\n      } else {\n        p = pos;\n        while (!peg$posDetailsCache[--p]) {}\n      }\n      details = peg$posDetailsCache[p];\n      details = {\n        line: details.line,\n        column: details.column\n      };\n      while (p < pos) {\n        if (input.charCodeAt(p) === 10) {\n          details.line++;\n          details.column = 1;\n        } else {\n          details.column++;\n        }\n        p++;\n      }\n      peg$posDetailsCache[pos] = details;\n      return details;\n    }\n  }\n  function peg$computeLocation(startPos, endPos, offset) {\n    var startPosDetails = peg$computePosDetails(startPos);\n    var endPosDetails = peg$computePosDetails(endPos);\n    var res = {\n      source: peg$source,\n      start: {\n        offset: startPos,\n        line: startPosDetails.line,\n        column: startPosDetails.column\n      },\n      end: {\n        offset: endPos,\n        line: endPosDetails.line,\n        column: endPosDetails.column\n      }\n    };\n    if (offset && peg$source && typeof peg$source.offset === 'function') {\n      res.start = peg$source.offset(res.start);\n      res.end = peg$source.offset(res.end);\n    }\n    return res;\n  }\n  function peg$fail(expected) {\n    if (peg$currPos < peg$maxFailPos) {\n      return;\n    }\n    if (peg$currPos > peg$maxFailPos) {\n      peg$maxFailPos = peg$currPos;\n      peg$maxFailExpected = [];\n    }\n    peg$maxFailExpected.push(expected);\n  }\n  function peg$buildSimpleError(message, location) {\n    return new peg$SyntaxError(message, null, null, location);\n  }\n  function peg$buildStructuredError(expected, found, location) {\n    return new peg$SyntaxError(peg$SyntaxError.buildMessage(expected, found), expected, found, location);\n  }\n  function peg$parsestart() {\n    var s0, s1;\n    peg$silentFails++;\n    s0 = peg$parsetransformFunctions();\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e0);\n      }\n    }\n    return s0;\n  }\n  function peg$parsetransformFunctions() {\n    var s0, s1, s2, s3, s4, s5;\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$parsefunction();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parse_();\n      s5 = peg$parsefunction();\n      if (s5 !== peg$FAILED) {\n        s4 = [s4, s5];\n        s3 = s4;\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parse_();\n        s5 = peg$parsefunction();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      peg$savedPos = s0;\n      s0 = peg$f0(s1, s2);\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e1);\n      }\n    }\n    return s0;\n  }\n  function peg$parsefunction() {\n    var s0, s1;\n    peg$silentFails++;\n    s0 = peg$parsematrix();\n    if (s0 === peg$FAILED) {\n      s0 = peg$parsetranslate();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parsescale();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parserotate();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parseskewX();\n            if (s0 === peg$FAILED) {\n              s0 = peg$parseskewY();\n            }\n          }\n        }\n      }\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e2);\n      }\n    }\n    return s0;\n  }\n  function peg$parsematrix() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10, s11, s12, s13, s14, s15, s16, s17, s18, s19, s20, s21, s22, s23;\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$parse_();\n    if (input.substr(peg$currPos, 7) === peg$c0) {\n      s2 = peg$c0;\n      peg$currPos += 7;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e4);\n      }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parse_();\n      s4 = peg$parseNUM();\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsespaceOrComma();\n        s6 = peg$parseNUM();\n        if (s6 !== peg$FAILED) {\n          s7 = peg$parsespaceOrComma();\n          s8 = peg$parseNUM();\n          if (s8 !== peg$FAILED) {\n            s9 = peg$parsespaceOrComma();\n            s10 = peg$parseNUM();\n            if (s10 !== peg$FAILED) {\n              s11 = peg$parsespaceOrComma();\n              s12 = peg$parseNUM();\n              if (s12 !== peg$FAILED) {\n                s13 = peg$parsespaceOrComma();\n                s14 = peg$parseNUM();\n                if (s14 !== peg$FAILED) {\n                  s15 = peg$parsespaceOrComma();\n                  s16 = peg$parseNUM();\n                  if (s16 !== peg$FAILED) {\n                    s17 = peg$parsespaceOrComma();\n                    s18 = peg$parseNUM();\n                    if (s18 !== peg$FAILED) {\n                      s19 = peg$parsespaceOrComma();\n                      s20 = peg$parseNUM();\n                      if (s20 !== peg$FAILED) {\n                        s21 = peg$parse_();\n                        if (input.charCodeAt(peg$currPos) === 41) {\n                          s22 = peg$c1;\n                          peg$currPos++;\n                        } else {\n                          s22 = peg$FAILED;\n                          if (peg$silentFails === 0) {\n                            peg$fail(peg$e5);\n                          }\n                        }\n                        if (s22 !== peg$FAILED) {\n                          s23 = peg$parse_();\n                          peg$savedPos = s0;\n                          s0 = peg$f1(s4, s6, s8, s10, s12, s14, s16, s18, s20);\n                        } else {\n                          peg$currPos = s0;\n                          s0 = peg$FAILED;\n                        }\n                      } else {\n                        peg$currPos = s0;\n                        s0 = peg$FAILED;\n                      }\n                    } else {\n                      peg$currPos = s0;\n                      s0 = peg$FAILED;\n                    }\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e3);\n      }\n    }\n    return s0;\n  }\n  function peg$parsetranslate() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9;\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$parse_();\n    if (input.substr(peg$currPos, 10) === peg$c2) {\n      s2 = peg$c2;\n      peg$currPos += 10;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e7);\n      }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parse_();\n      s4 = peg$parseNUM();\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsespaceOrComma();\n        s6 = peg$parseNUM();\n        if (s6 === peg$FAILED) {\n          s6 = null;\n        }\n        s7 = peg$parse_();\n        if (input.charCodeAt(peg$currPos) === 41) {\n          s8 = peg$c1;\n          peg$currPos++;\n        } else {\n          s8 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$e5);\n          }\n        }\n        if (s8 !== peg$FAILED) {\n          s9 = peg$parse_();\n          peg$savedPos = s0;\n          s0 = peg$f2(s4, s6);\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e6);\n      }\n    }\n    return s0;\n  }\n  function peg$parsescale() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9;\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$parse_();\n    if (input.substr(peg$currPos, 6) === peg$c3) {\n      s2 = peg$c3;\n      peg$currPos += 6;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e9);\n      }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parse_();\n      s4 = peg$parseNUM();\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsespaceOrComma();\n        s6 = peg$parseNUM();\n        if (s6 === peg$FAILED) {\n          s6 = null;\n        }\n        s7 = peg$parse_();\n        if (input.charCodeAt(peg$currPos) === 41) {\n          s8 = peg$c1;\n          peg$currPos++;\n        } else {\n          s8 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$e5);\n          }\n        }\n        if (s8 !== peg$FAILED) {\n          s9 = peg$parse_();\n          peg$savedPos = s0;\n          s0 = peg$f3(s4, s6);\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e8);\n      }\n    }\n    return s0;\n  }\n  function peg$parserotate() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8;\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$parse_();\n    if (input.substr(peg$currPos, 7) === peg$c4) {\n      s2 = peg$c4;\n      peg$currPos += 7;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e11);\n      }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parse_();\n      s4 = peg$parseNUM();\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsetwoNumbers();\n        if (s5 === peg$FAILED) {\n          s5 = null;\n        }\n        s6 = peg$parse_();\n        if (input.charCodeAt(peg$currPos) === 41) {\n          s7 = peg$c1;\n          peg$currPos++;\n        } else {\n          s7 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$e5);\n          }\n        }\n        if (s7 !== peg$FAILED) {\n          s8 = peg$parse_();\n          peg$savedPos = s0;\n          s0 = peg$f4(s4, s5);\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e10);\n      }\n    }\n    return s0;\n  }\n  function peg$parsetwoNumbers() {\n    var s0, s1, s2, s3, s4;\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$parsespaceOrComma();\n    s2 = peg$parseNUM();\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parsespaceOrComma();\n      s4 = peg$parseNUM();\n      if (s4 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s0 = peg$f5(s2, s4);\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e12);\n      }\n    }\n    return s0;\n  }\n  function peg$parseskewX() {\n    var s0, s1, s2, s3, s4, s5, s6, s7;\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$parse_();\n    if (input.substr(peg$currPos, 6) === peg$c5) {\n      s2 = peg$c5;\n      peg$currPos += 6;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e14);\n      }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parse_();\n      s4 = peg$parseNUM();\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parse_();\n        if (input.charCodeAt(peg$currPos) === 41) {\n          s6 = peg$c1;\n          peg$currPos++;\n        } else {\n          s6 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$e5);\n          }\n        }\n        if (s6 !== peg$FAILED) {\n          s7 = peg$parse_();\n          peg$savedPos = s0;\n          s0 = peg$f6(s4);\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e13);\n      }\n    }\n    return s0;\n  }\n  function peg$parseskewY() {\n    var s0, s1, s2, s3, s4, s5, s6, s7;\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$parse_();\n    if (input.substr(peg$currPos, 6) === peg$c6) {\n      s2 = peg$c6;\n      peg$currPos += 6;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e16);\n      }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parse_();\n      s4 = peg$parseNUM();\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parse_();\n        if (input.charCodeAt(peg$currPos) === 41) {\n          s6 = peg$c1;\n          peg$currPos++;\n        } else {\n          s6 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$e5);\n          }\n        }\n        if (s6 !== peg$FAILED) {\n          s7 = peg$parse_();\n          peg$savedPos = s0;\n          s0 = peg$f7(s4);\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e15);\n      }\n    }\n    return s0;\n  }\n  function peg$parsespaceOrComma() {\n    var s0, s1;\n    peg$silentFails++;\n    s0 = [];\n    s1 = input.charAt(peg$currPos);\n    if (peg$r0.test(s1)) {\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e18);\n      }\n    }\n    while (s1 !== peg$FAILED) {\n      s0.push(s1);\n      s1 = input.charAt(peg$currPos);\n      if (peg$r0.test(s1)) {\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) {\n          peg$fail(peg$e18);\n        }\n      }\n    }\n    peg$silentFails--;\n    s1 = peg$FAILED;\n    if (peg$silentFails === 0) {\n      peg$fail(peg$e17);\n    }\n    return s0;\n  }\n  function peg$parse_() {\n    var s0, s1;\n    peg$silentFails++;\n    s0 = [];\n    s1 = input.charAt(peg$currPos);\n    if (peg$r1.test(s1)) {\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e20);\n      }\n    }\n    while (s1 !== peg$FAILED) {\n      s0.push(s1);\n      s1 = input.charAt(peg$currPos);\n      if (peg$r1.test(s1)) {\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) {\n          peg$fail(peg$e20);\n        }\n      }\n    }\n    peg$silentFails--;\n    s1 = peg$FAILED;\n    if (peg$silentFails === 0) {\n      peg$fail(peg$e19);\n    }\n    return s0;\n  }\n  function peg$parseNUM() {\n    var s0, s1, s2, s3, s4, s5, s6, s7;\n    s0 = peg$currPos;\n    s1 = input.charAt(peg$currPos);\n    if (peg$r2.test(s1)) {\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e21);\n      }\n    }\n    if (s1 === peg$FAILED) {\n      s1 = null;\n    }\n    s2 = peg$currPos;\n    s3 = [];\n    s4 = input.charAt(peg$currPos);\n    if (peg$r3.test(s4)) {\n      peg$currPos++;\n    } else {\n      s4 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e22);\n      }\n    }\n    while (s4 !== peg$FAILED) {\n      s3.push(s4);\n      s4 = input.charAt(peg$currPos);\n      if (peg$r3.test(s4)) {\n        peg$currPos++;\n      } else {\n        s4 = peg$FAILED;\n        if (peg$silentFails === 0) {\n          peg$fail(peg$e22);\n        }\n      }\n    }\n    if (input.charCodeAt(peg$currPos) === 46) {\n      s4 = peg$c7;\n      peg$currPos++;\n    } else {\n      s4 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$e23);\n      }\n    }\n    if (s4 !== peg$FAILED) {\n      s5 = [];\n      s6 = input.charAt(peg$currPos);\n      if (peg$r3.test(s6)) {\n        peg$currPos++;\n      } else {\n        s6 = peg$FAILED;\n        if (peg$silentFails === 0) {\n          peg$fail(peg$e22);\n        }\n      }\n      if (s6 !== peg$FAILED) {\n        while (s6 !== peg$FAILED) {\n          s5.push(s6);\n          s6 = input.charAt(peg$currPos);\n          if (peg$r3.test(s6)) {\n            peg$currPos++;\n          } else {\n            s6 = peg$FAILED;\n            if (peg$silentFails === 0) {\n              peg$fail(peg$e22);\n            }\n          }\n        }\n      } else {\n        s5 = peg$FAILED;\n      }\n      if (s5 !== peg$FAILED) {\n        s3 = [s3, s4, s5];\n        s2 = s3;\n      } else {\n        peg$currPos = s2;\n        s2 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s2;\n      s2 = peg$FAILED;\n    }\n    if (s2 === peg$FAILED) {\n      s2 = [];\n      s3 = input.charAt(peg$currPos);\n      if (peg$r3.test(s3)) {\n        peg$currPos++;\n      } else {\n        s3 = peg$FAILED;\n        if (peg$silentFails === 0) {\n          peg$fail(peg$e22);\n        }\n      }\n      if (s3 !== peg$FAILED) {\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = input.charAt(peg$currPos);\n          if (peg$r3.test(s3)) {\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) {\n              peg$fail(peg$e22);\n            }\n          }\n        }\n      } else {\n        s2 = peg$FAILED;\n      }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 101) {\n        s4 = peg$c8;\n        peg$currPos++;\n      } else {\n        s4 = peg$FAILED;\n        if (peg$silentFails === 0) {\n          peg$fail(peg$e24);\n        }\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = input.charAt(peg$currPos);\n        if (peg$r2.test(s5)) {\n          peg$currPos++;\n        } else {\n          s5 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$e21);\n          }\n        }\n        if (s5 === peg$FAILED) {\n          s5 = null;\n        }\n        s6 = [];\n        s7 = input.charAt(peg$currPos);\n        if (peg$r3.test(s7)) {\n          peg$currPos++;\n        } else {\n          s7 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$e22);\n          }\n        }\n        if (s7 !== peg$FAILED) {\n          while (s7 !== peg$FAILED) {\n            s6.push(s7);\n            s7 = input.charAt(peg$currPos);\n            if (peg$r3.test(s7)) {\n              peg$currPos++;\n            } else {\n              s7 = peg$FAILED;\n              if (peg$silentFails === 0) {\n                peg$fail(peg$e22);\n              }\n            }\n          }\n        } else {\n          s6 = peg$FAILED;\n        }\n        if (s6 !== peg$FAILED) {\n          s4 = [s4, s5, s6];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      peg$savedPos = s0;\n      s0 = peg$f8();\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  peg$result = peg$startRuleFunction();\n  if (options.peg$library) {\n    return /** @type {any} */{\n      peg$result,\n      peg$currPos,\n      peg$FAILED,\n      peg$maxFailExpected,\n      peg$maxFailPos\n    };\n  }\n  if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n    return peg$result;\n  } else {\n    if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n      peg$fail(peg$endExpectation());\n    }\n    throw peg$buildStructuredError(peg$maxFailExpected, peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null, peg$maxFailPos < input.length ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1) : peg$computeLocation(peg$maxFailPos, peg$maxFailPos));\n  }\n}\nmodule.exports = {\n  StartRules: ['start'],\n  SyntaxError: peg$SyntaxError,\n  parse: peg$parse\n};\n//# sourceMappingURL=transformToRn.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/lib/extract/transformToRn.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/lib/extract/types.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/lib/extract/types.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LW5hdGl2ZS1zdmcvbGliL2NvbW1vbmpzL2xpYi9leHRyYWN0L3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LW5hdGl2ZS1zdmcvbGliL2NvbW1vbmpzL2xpYi9leHRyYWN0L3R5cGVzLmpzPzJiZjYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/lib/extract/types.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/lib/resolve.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/lib/resolve.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.resolve = resolve;\nvar _reactNative = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n// Kept in separate file, to avoid name collision with Symbol element\nfunction resolve(styleProp, cleanedProps) {\n  if (styleProp) {\n    return _reactNative.StyleSheet ? [styleProp, cleanedProps] :\n    // Compatibility for arrays of styles in plain react web\n    styleProp[Symbol.iterator] ? Object.assign({}, ...styleProp, cleanedProps) : Object.assign({}, styleProp, cleanedProps);\n  } else {\n    return cleanedProps;\n  }\n}\n//# sourceMappingURL=resolve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LW5hdGl2ZS1zdmcvbGliL2NvbW1vbmpzL2xpYi9yZXNvbHZlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGVBQWU7QUFDZixtQkFBbUIsbUJBQU8sQ0FBQywyRUFBYztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlELGdEQUFnRDtBQUNqRyxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9ub2RlX21vZHVsZXMvcmVhY3QtbmF0aXZlLXN2Zy9saWIvY29tbW9uanMvbGliL3Jlc29sdmUuanM/OWU2YSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMucmVzb2x2ZSA9IHJlc29sdmU7XG52YXIgX3JlYWN0TmF0aXZlID0gcmVxdWlyZShcInJlYWN0LW5hdGl2ZVwiKTtcbi8vIEtlcHQgaW4gc2VwYXJhdGUgZmlsZSwgdG8gYXZvaWQgbmFtZSBjb2xsaXNpb24gd2l0aCBTeW1ib2wgZWxlbWVudFxuZnVuY3Rpb24gcmVzb2x2ZShzdHlsZVByb3AsIGNsZWFuZWRQcm9wcykge1xuICBpZiAoc3R5bGVQcm9wKSB7XG4gICAgcmV0dXJuIF9yZWFjdE5hdGl2ZS5TdHlsZVNoZWV0ID8gW3N0eWxlUHJvcCwgY2xlYW5lZFByb3BzXSA6XG4gICAgLy8gQ29tcGF0aWJpbGl0eSBmb3IgYXJyYXlzIG9mIHN0eWxlcyBpbiBwbGFpbiByZWFjdCB3ZWJcbiAgICBzdHlsZVByb3BbU3ltYm9sLml0ZXJhdG9yXSA/IE9iamVjdC5hc3NpZ24oe30sIC4uLnN0eWxlUHJvcCwgY2xlYW5lZFByb3BzKSA6IE9iamVjdC5hc3NpZ24oe30sIHN0eWxlUHJvcCwgY2xlYW5lZFByb3BzKTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gY2xlYW5lZFByb3BzO1xuICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZXNvbHZlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/lib/resolve.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/lib/resolveAssetUri.js":
/*!*******************************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/lib/resolveAssetUri.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.resolveAssetUri = resolveAssetUri;\nvar _reactNative = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\nvar _registry = __webpack_require__(/*! @react-native/assets-registry/registry */ \"@react-native/assets-registry/registry\");\n// @ts-expect-error react-native/assets-registry doesn't export types.\n\nconst svgDataUriPattern = /^(data:image\\/svg\\+xml;utf8,)(.*)/;\n\n// Based on that function: https://github.com/necolas/react-native-web/blob/54c14d64dabd175e8055e1dc92e9196c821f9b7d/packages/react-native-web/src/exports/Image/index.js#L118-L156\nfunction resolveAssetUri(source) {\n  let src = {};\n  if (typeof source === 'number') {\n    // get the URI from the packager\n    const asset = (0, _registry.getAssetByID)(source);\n    if (asset == null) {\n      throw new Error(`Image: asset with ID \"${source}\" could not be found. Please check the image source or packager.`);\n    }\n    src = {\n      width: asset.width,\n      height: asset.height,\n      scale: asset.scales[0]\n    };\n    if (asset.scales.length > 1) {\n      const preferredScale = _reactNative.PixelRatio.get();\n      // Get the scale which is closest to the preferred scale\n      src.scale = asset.scales.reduce((prev, curr) => Math.abs(curr - preferredScale) < Math.abs(prev - preferredScale) ? curr : prev);\n    }\n    const scaleSuffix = src.scale !== 1 ? `@${src.scale}x` : '';\n    src.uri = asset ? `${asset.httpServerLocation}/${asset.name}${scaleSuffix}.${asset.type}` : '';\n  } else if (typeof source === 'string') {\n    src.uri = source;\n  } else if (source && !Array.isArray(source) && typeof source.uri === 'string') {\n    src.uri = source.uri;\n  }\n  if (src.uri) {\n    var _src;\n    const match = (_src = src) === null || _src === void 0 || (_src = _src.uri) === null || _src === void 0 ? void 0 : _src.match(svgDataUriPattern);\n    // inline SVG markup may contain characters (e.g., #, \") that need to be escaped\n    if (match) {\n      const [, prefix, svg] = match;\n      const encodedSvg = encodeURIComponent(svg);\n      src.uri = `${prefix}${encodedSvg}`;\n      return src;\n    }\n  }\n  return src;\n}\n//# sourceMappingURL=resolveAssetUri.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/lib/resolveAssetUri.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/utils/fetchData.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/utils/fetchData.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.fetchText = fetchText;\nvar _reactNative = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\nvar _buffer = __webpack_require__(/*! buffer */ \"buffer\");\nasync function fetchText(uri) {\n  if (!uri) {\n    return null;\n  }\n  if (uri.startsWith('data:image/svg+xml;utf8') && _reactNative.Platform.OS === 'android') {\n    return dataUriToXml(uri);\n  } else if (uri.startsWith('data:image/svg+xml;base64')) {\n    return decodeBase64Image(uri);\n  } else {\n    return fetchUriData(uri);\n  }\n}\nconst decodeBase64Image = uri => {\n  const decoded = decodeURIComponent(uri);\n  const splitContent = decoded.split(';')[1].split(',');\n  const dataType = splitContent[0];\n  const content = splitContent.slice(1).join(',');\n  return _buffer.Buffer.from(content, dataType).toString('utf-8');\n};\nfunction dataUriToXml(uri) {\n  try {\n    // decode and remove data:image/svg+xml;utf8, prefix\n    return decodeURIComponent(uri).split(',').slice(1).join(',');\n  } catch (error) {\n    throw new Error(`Decoding ${uri} failed with error: ${error}`);\n  }\n}\nasync function fetchUriData(uri) {\n  const response = await fetch(uri);\n  if (response.ok || response.status === 0 && uri.startsWith('file://')) {\n    return await response.text();\n  }\n  throw new Error(`Fetching ${uri} failed with status ${response.status}`);\n}\n//# sourceMappingURL=fetchData.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/utils/fetchData.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/web/WebShape.js":
/*!************************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/web/WebShape.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.WebShape = void 0;\nvar _react = _interopRequireDefault(__webpack_require__(/*! react */ \"react\"));\nvar _reactNative = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\nvar _prepare = __webpack_require__(/*! ./utils/prepare */ \"../../node_modules/react-native-svg/lib/commonjs/web/utils/prepare.js\");\nvar _convertInt32Color = __webpack_require__(/*! ./utils/convertInt32Color */ \"../../node_modules/react-native-svg/lib/commonjs/web/utils/convertInt32Color.js\");\nvar _utils = __webpack_require__(/*! ./utils */ \"../../node_modules/react-native-svg/lib/commonjs/web/utils/index.js\");\nvar _SvgTouchableMixin = _interopRequireDefault(__webpack_require__(/*! ../lib/SvgTouchableMixin */ \"../../node_modules/react-native-svg/lib/commonjs/lib/SvgTouchableMixin.js\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nclass WebShape extends _react.default.Component {\n  prepareProps(props) {\n    return props;\n  }\n  elementRef = /*#__PURE__*/_react.default.createRef();\n  lastMergedProps = {};\n\n  /**\n   * disclaimer: I am not sure why the props are wrapped in a `style` attribute here, but that's how reanimated calls it\n   */\n  setNativeProps(props) {\n    const merged = Object.assign({}, this.props, this.lastMergedProps, props.style);\n    this.lastMergedProps = merged;\n    const clean = (0, _prepare.prepare)(this, this.prepareProps(merged));\n    const current = this.elementRef.current;\n    if (current) {\n      for (const cleanAttribute of Object.keys(clean)) {\n        const cleanValue = clean[cleanAttribute];\n        switch (cleanAttribute) {\n          case 'ref':\n          case 'children':\n            break;\n          case 'style':\n            // style can be an object here or an array, so we convert it to an array and assign each element\n            for (const partialStyle of [].concat(clean.style ?? [])) {\n              Object.assign(current.style, partialStyle);\n            }\n            break;\n          case 'fill':\n            if (cleanValue && typeof cleanValue === 'object') {\n              const value = cleanValue;\n              current.setAttribute('fill', (0, _convertInt32Color.convertInt32ColorToRGBA)(value.payload));\n            }\n            break;\n          case 'stroke':\n            if (cleanValue && typeof cleanValue === 'object') {\n              const value = cleanValue;\n              current.setAttribute('stroke', (0, _convertInt32Color.convertInt32ColorToRGBA)(value.payload));\n            }\n            break;\n          default:\n            // apply all other incoming prop updates as attributes on the node\n            // same logic as in https://github.com/software-mansion/react-native-reanimated/blob/d04720c82f5941532991b235787285d36d717247/src/reanimated2/js-reanimated/index.ts#L38-L39\n            // @ts-expect-error TODO: fix this\n            current.setAttribute((0, _utils.camelCaseToDashed)(cleanAttribute), cleanValue);\n            break;\n        }\n      }\n    }\n  }\n  constructor(props) {\n    super(props);\n\n    // Do not attach touchable mixin handlers if SVG element doesn't have a touchable prop\n    if ((0, _utils.hasTouchableProperty)(props)) {\n      (0, _SvgTouchableMixin.default)(this);\n    }\n    this._remeasureMetricsOnActivation = _utils.remeasure.bind(this);\n  }\n  render() {\n    if (!this.tag) {\n      throw new Error('When extending `WebShape` you need to overwrite either `tag` or `render`!');\n    }\n    this.lastMergedProps = {};\n    return (0, _reactNative.unstable_createElement)(this.tag, (0, _prepare.prepare)(this, this.prepareProps(this.props)));\n  }\n}\nexports.WebShape = WebShape;\n//# sourceMappingURL=WebShape.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/web/WebShape.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/web/utils/convertInt32Color.js":
/*!***************************************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/web/utils/convertInt32Color.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.convertInt32ColorToRGBA = convertInt32ColorToRGBA;\nfunction convertInt32ColorToRGBA(color) {\n  const r = color >> 16 & 255;\n  const g = color >> 8 & 255;\n  const b = color & 255;\n  const a = (color >> 24 & 255) / 255;\n  const alpha = a.toFixed(2);\n  return `rgba(${r},${g},${b},${alpha})`;\n}\n//# sourceMappingURL=convertInt32Color.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LW5hdGl2ZS1zdmcvbGliL2NvbW1vbmpzL3dlYi91dGlscy9jb252ZXJ0SW50MzJDb2xvci5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRiwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLE1BQU07QUFDdEM7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL25vZGVfbW9kdWxlcy9yZWFjdC1uYXRpdmUtc3ZnL2xpYi9jb21tb25qcy93ZWIvdXRpbHMvY29udmVydEludDMyQ29sb3IuanM/YTliNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuY29udmVydEludDMyQ29sb3JUb1JHQkEgPSBjb252ZXJ0SW50MzJDb2xvclRvUkdCQTtcbmZ1bmN0aW9uIGNvbnZlcnRJbnQzMkNvbG9yVG9SR0JBKGNvbG9yKSB7XG4gIGNvbnN0IHIgPSBjb2xvciA+PiAxNiAmIDI1NTtcbiAgY29uc3QgZyA9IGNvbG9yID4+IDggJiAyNTU7XG4gIGNvbnN0IGIgPSBjb2xvciAmIDI1NTtcbiAgY29uc3QgYSA9IChjb2xvciA+PiAyNCAmIDI1NSkgLyAyNTU7XG4gIGNvbnN0IGFscGhhID0gYS50b0ZpeGVkKDIpO1xuICByZXR1cm4gYHJnYmEoJHtyfSwke2d9LCR7Yn0sJHthbHBoYX0pYDtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnZlcnRJbnQzMkNvbG9yLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/web/utils/convertInt32Color.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/web/utils/index.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/web/utils/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.camelCaseToDashed = void 0;\nexports.encodeSvg = encodeSvg;\nexports.hasTouchableProperty = exports.getBoundingClientRect = void 0;\nexports.parseTransformProp = parseTransformProp;\nexports.remeasure = remeasure;\nvar _extractTransform = __webpack_require__(/*! ../../lib/extract/extractTransform */ \"../../node_modules/react-native-svg/lib/commonjs/lib/extract/extractTransform.js\");\nconst hasTouchableProperty = props => props.onPress || props.onPressIn || props.onPressOut || props.onLongPress;\nexports.hasTouchableProperty = hasTouchableProperty;\nconst camelCaseToDashed = camelCase => {\n  return camelCase.replace(/[A-Z]/g, m => '-' + m.toLowerCase());\n};\nexports.camelCaseToDashed = camelCaseToDashed;\nfunction stringifyTransformProps(transformProps) {\n  const transformArray = [];\n  if (transformProps.translate != null) {\n    transformArray.push(`translate(${transformProps.translate})`);\n  }\n  if (transformProps.translateX != null || transformProps.translateY != null) {\n    transformArray.push(`translate(${transformProps.translateX || 0}, ${transformProps.translateY || 0})`);\n  }\n  if (transformProps.scale != null) {\n    transformArray.push(`scale(${transformProps.scale})`);\n  }\n  if (transformProps.scaleX != null || transformProps.scaleY != null) {\n    transformArray.push(`scale(${transformProps.scaleX || 1}, ${transformProps.scaleY || 1})`);\n  }\n  // rotation maps to rotate, not to collide with the text rotate attribute (which acts per glyph rather than block)\n  if (transformProps.rotation != null) {\n    transformArray.push(`rotate(${transformProps.rotation})`);\n  }\n  if (transformProps.skewX != null) {\n    transformArray.push(`skewX(${transformProps.skewX})`);\n  }\n  if (transformProps.skewY != null) {\n    transformArray.push(`skewY(${transformProps.skewY})`);\n  }\n  return transformArray;\n}\nfunction parseTransformProp(transform, props) {\n  const transformArray = [];\n  props && transformArray.push(...stringifyTransformProps(props));\n  if (Array.isArray(transform)) {\n    if (typeof transform[0] === 'number') {\n      transformArray.push(`matrix(${transform.join(' ')})`);\n    } else {\n      const stringifiedProps = (0, _extractTransform.transformsArrayToProps)(\n      // @ts-expect-error FIXME\n      transform);\n      transformArray.push(...stringifyTransformProps(stringifiedProps));\n    }\n  } else if (typeof transform === 'string') {\n    transformArray.push(transform);\n  }\n  return transformArray.length ? transformArray.join(' ') : undefined;\n}\nconst getBoundingClientRect = node => {\n  if (node) {\n    const isElement = node.nodeType === 1; /* Node.ELEMENT_NODE */\n    if (isElement && typeof node.getBoundingClientRect === 'function') {\n      return node.getBoundingClientRect();\n    }\n  }\n  throw new Error('Can not get boundingClientRect of ' + node || 0);\n};\nexports.getBoundingClientRect = getBoundingClientRect;\nconst measureLayout = (node, callback) => {\n  const relativeNode = node === null || node === void 0 ? void 0 : node.parentNode;\n  if (relativeNode) {\n    setTimeout(() => {\n      // @ts-expect-error TODO: handle it better\n      const relativeRect = getBoundingClientRect(relativeNode);\n      const {\n        height,\n        left,\n        top,\n        width\n      } = getBoundingClientRect(node);\n      const x = left - relativeRect.left;\n      const y = top - relativeRect.top;\n      callback(x, y, width, height, left, top);\n    }, 0);\n  }\n};\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction remeasure() {\n  const tag = this.state.touchable.responderID;\n  if (tag === null) {\n    return;\n  }\n  measureLayout(tag, this._handleQueryLayout);\n}\n\n/* Taken from here: https://gist.github.com/jennyknuth/222825e315d45a738ed9d6e04c7a88d0 */\nfunction encodeSvg(svgString) {\n  return svgString.replace('<svg', ~svgString.indexOf('xmlns') ? '<svg' : '<svg xmlns=\"http://www.w3.org/2000/svg\"').replace(/\"/g, \"'\").replace(/%/g, '%25').replace(/#/g, '%23').replace(/{/g, '%7B').replace(/}/g, '%7D').replace(/</g, '%3C').replace(/>/g, '%3E').replace(/\\s+/g, ' ');\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/web/utils/index.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/web/utils/prepare.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/web/utils/prepare.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.prepare = void 0;\nvar _ = __webpack_require__(/*! . */ \"../../node_modules/react-native-svg/lib/commonjs/web/utils/index.js\");\nvar _resolve = __webpack_require__(/*! ../../lib/resolve */ \"../../node_modules/react-native-svg/lib/commonjs/lib/resolve.js\");\nvar _resolveAssetUri2 = __webpack_require__(/*! ../../lib/resolveAssetUri */ \"../../node_modules/react-native-svg/lib/commonjs/lib/resolveAssetUri.js\");\n/**\n * `react-native-svg` supports additional props that aren't defined in the spec.\n * This function replaces them in a spec conforming manner.\n *\n * @param {WebShape} self Instance given to us.\n * @param {Object?} props Optional overridden props given to us.\n * @returns {Object} Cleaned props object.\n * @private\n */\nconst prepare = (self, props = self.props) => {\n  const {\n    transform,\n    origin,\n    originX,\n    originY,\n    fontFamily,\n    fontSize,\n    fontWeight,\n    fontStyle,\n    style,\n    forwardedRef,\n    gradientTransform,\n    patternTransform,\n    onPress,\n    ...rest\n  } = props;\n  const clean = {\n    ...((0, _.hasTouchableProperty)(props) ? {\n      onStartShouldSetResponder: self.touchableHandleStartShouldSetResponder,\n      onResponderTerminationRequest: self.touchableHandleResponderTerminationRequest,\n      onResponderGrant: self.touchableHandleResponderGrant,\n      onResponderMove: self.touchableHandleResponderMove,\n      onResponderRelease: self.touchableHandleResponderRelease,\n      onResponderTerminate: self.touchableHandleResponderTerminate\n    } : null),\n    ...rest\n  };\n  if (origin != null) {\n    clean['transform-origin'] = origin.toString().replace(',', ' ');\n  } else if (originX != null || originY != null) {\n    clean['transform-origin'] = `${originX || 0} ${originY || 0}`;\n  }\n\n  // we do it like this because setting transform as undefined causes error in web\n  const parsedTransform = (0, _.parseTransformProp)(transform, props);\n  if (parsedTransform) {\n    clean.transform = parsedTransform;\n  }\n  const parsedGradientTransform = (0, _.parseTransformProp)(gradientTransform);\n  if (parsedGradientTransform) {\n    clean.gradientTransform = parsedGradientTransform;\n  }\n  const parsedPatternTransform = (0, _.parseTransformProp)(patternTransform);\n  if (parsedPatternTransform) {\n    clean.patternTransform = parsedPatternTransform;\n  }\n  clean.ref = el => {\n    self.elementRef.current = el;\n    if (typeof forwardedRef === 'function') {\n      forwardedRef(el);\n    } else if (forwardedRef) {\n      forwardedRef.current = el;\n    }\n  };\n  const styles = {};\n  if (fontFamily != null) {\n    styles.fontFamily = fontFamily;\n  }\n  if (fontSize != null) {\n    styles.fontSize = fontSize;\n  }\n  if (fontWeight != null) {\n    styles.fontWeight = fontWeight;\n  }\n  if (fontStyle != null) {\n    styles.fontStyle = fontStyle;\n  }\n  clean.style = (0, _resolve.resolve)(style, styles);\n  if (onPress !== null) {\n    clean.onClick = props.onPress;\n  }\n  if (props.href !== null && props.href !== undefined) {\n    var _resolveAssetUri;\n    clean.href = (_resolveAssetUri = (0, _resolveAssetUri2.resolveAssetUri)(props.href)) === null || _resolveAssetUri === void 0 ? void 0 : _resolveAssetUri.uri;\n  }\n  return clean;\n};\nexports.prepare = prepare;\n//# sourceMappingURL=prepare.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LW5hdGl2ZS1zdmcvbGliL2NvbW1vbmpzL3dlYi91dGlscy9wcmVwYXJlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGVBQWU7QUFDZixRQUFRLG1CQUFPLENBQUMsOEVBQUc7QUFDbkIsZUFBZSxtQkFBTyxDQUFDLDBGQUFtQjtBQUMxQyx3QkFBd0IsbUJBQU8sQ0FBQywwR0FBMkI7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFVBQVU7QUFDckIsV0FBVyxTQUFTO0FBQ3BCLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSixtQ0FBbUMsY0FBYyxFQUFFLGFBQWE7QUFDaEU7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZTtBQUNmIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LW5hdGl2ZS1zdmcvbGliL2NvbW1vbmpzL3dlYi91dGlscy9wcmVwYXJlLmpzPzQ0ZTciXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLnByZXBhcmUgPSB2b2lkIDA7XG52YXIgXyA9IHJlcXVpcmUoXCIuXCIpO1xudmFyIF9yZXNvbHZlID0gcmVxdWlyZShcIi4uLy4uL2xpYi9yZXNvbHZlXCIpO1xudmFyIF9yZXNvbHZlQXNzZXRVcmkyID0gcmVxdWlyZShcIi4uLy4uL2xpYi9yZXNvbHZlQXNzZXRVcmlcIik7XG4vKipcbiAqIGByZWFjdC1uYXRpdmUtc3ZnYCBzdXBwb3J0cyBhZGRpdGlvbmFsIHByb3BzIHRoYXQgYXJlbid0IGRlZmluZWQgaW4gdGhlIHNwZWMuXG4gKiBUaGlzIGZ1bmN0aW9uIHJlcGxhY2VzIHRoZW0gaW4gYSBzcGVjIGNvbmZvcm1pbmcgbWFubmVyLlxuICpcbiAqIEBwYXJhbSB7V2ViU2hhcGV9IHNlbGYgSW5zdGFuY2UgZ2l2ZW4gdG8gdXMuXG4gKiBAcGFyYW0ge09iamVjdD99IHByb3BzIE9wdGlvbmFsIG92ZXJyaWRkZW4gcHJvcHMgZ2l2ZW4gdG8gdXMuXG4gKiBAcmV0dXJucyB7T2JqZWN0fSBDbGVhbmVkIHByb3BzIG9iamVjdC5cbiAqIEBwcml2YXRlXG4gKi9cbmNvbnN0IHByZXBhcmUgPSAoc2VsZiwgcHJvcHMgPSBzZWxmLnByb3BzKSA9PiB7XG4gIGNvbnN0IHtcbiAgICB0cmFuc2Zvcm0sXG4gICAgb3JpZ2luLFxuICAgIG9yaWdpblgsXG4gICAgb3JpZ2luWSxcbiAgICBmb250RmFtaWx5LFxuICAgIGZvbnRTaXplLFxuICAgIGZvbnRXZWlnaHQsXG4gICAgZm9udFN0eWxlLFxuICAgIHN0eWxlLFxuICAgIGZvcndhcmRlZFJlZixcbiAgICBncmFkaWVudFRyYW5zZm9ybSxcbiAgICBwYXR0ZXJuVHJhbnNmb3JtLFxuICAgIG9uUHJlc3MsXG4gICAgLi4ucmVzdFxuICB9ID0gcHJvcHM7XG4gIGNvbnN0IGNsZWFuID0ge1xuICAgIC4uLigoMCwgXy5oYXNUb3VjaGFibGVQcm9wZXJ0eSkocHJvcHMpID8ge1xuICAgICAgb25TdGFydFNob3VsZFNldFJlc3BvbmRlcjogc2VsZi50b3VjaGFibGVIYW5kbGVTdGFydFNob3VsZFNldFJlc3BvbmRlcixcbiAgICAgIG9uUmVzcG9uZGVyVGVybWluYXRpb25SZXF1ZXN0OiBzZWxmLnRvdWNoYWJsZUhhbmRsZVJlc3BvbmRlclRlcm1pbmF0aW9uUmVxdWVzdCxcbiAgICAgIG9uUmVzcG9uZGVyR3JhbnQ6IHNlbGYudG91Y2hhYmxlSGFuZGxlUmVzcG9uZGVyR3JhbnQsXG4gICAgICBvblJlc3BvbmRlck1vdmU6IHNlbGYudG91Y2hhYmxlSGFuZGxlUmVzcG9uZGVyTW92ZSxcbiAgICAgIG9uUmVzcG9uZGVyUmVsZWFzZTogc2VsZi50b3VjaGFibGVIYW5kbGVSZXNwb25kZXJSZWxlYXNlLFxuICAgICAgb25SZXNwb25kZXJUZXJtaW5hdGU6IHNlbGYudG91Y2hhYmxlSGFuZGxlUmVzcG9uZGVyVGVybWluYXRlXG4gICAgfSA6IG51bGwpLFxuICAgIC4uLnJlc3RcbiAgfTtcbiAgaWYgKG9yaWdpbiAhPSBudWxsKSB7XG4gICAgY2xlYW5bJ3RyYW5zZm9ybS1vcmlnaW4nXSA9IG9yaWdpbi50b1N0cmluZygpLnJlcGxhY2UoJywnLCAnICcpO1xuICB9IGVsc2UgaWYgKG9yaWdpblggIT0gbnVsbCB8fCBvcmlnaW5ZICE9IG51bGwpIHtcbiAgICBjbGVhblsndHJhbnNmb3JtLW9yaWdpbiddID0gYCR7b3JpZ2luWCB8fCAwfSAke29yaWdpblkgfHwgMH1gO1xuICB9XG5cbiAgLy8gd2UgZG8gaXQgbGlrZSB0aGlzIGJlY2F1c2Ugc2V0dGluZyB0cmFuc2Zvcm0gYXMgdW5kZWZpbmVkIGNhdXNlcyBlcnJvciBpbiB3ZWJcbiAgY29uc3QgcGFyc2VkVHJhbnNmb3JtID0gKDAsIF8ucGFyc2VUcmFuc2Zvcm1Qcm9wKSh0cmFuc2Zvcm0sIHByb3BzKTtcbiAgaWYgKHBhcnNlZFRyYW5zZm9ybSkge1xuICAgIGNsZWFuLnRyYW5zZm9ybSA9IHBhcnNlZFRyYW5zZm9ybTtcbiAgfVxuICBjb25zdCBwYXJzZWRHcmFkaWVudFRyYW5zZm9ybSA9ICgwLCBfLnBhcnNlVHJhbnNmb3JtUHJvcCkoZ3JhZGllbnRUcmFuc2Zvcm0pO1xuICBpZiAocGFyc2VkR3JhZGllbnRUcmFuc2Zvcm0pIHtcbiAgICBjbGVhbi5ncmFkaWVudFRyYW5zZm9ybSA9IHBhcnNlZEdyYWRpZW50VHJhbnNmb3JtO1xuICB9XG4gIGNvbnN0IHBhcnNlZFBhdHRlcm5UcmFuc2Zvcm0gPSAoMCwgXy5wYXJzZVRyYW5zZm9ybVByb3ApKHBhdHRlcm5UcmFuc2Zvcm0pO1xuICBpZiAocGFyc2VkUGF0dGVyblRyYW5zZm9ybSkge1xuICAgIGNsZWFuLnBhdHRlcm5UcmFuc2Zvcm0gPSBwYXJzZWRQYXR0ZXJuVHJhbnNmb3JtO1xuICB9XG4gIGNsZWFuLnJlZiA9IGVsID0+IHtcbiAgICBzZWxmLmVsZW1lbnRSZWYuY3VycmVudCA9IGVsO1xuICAgIGlmICh0eXBlb2YgZm9yd2FyZGVkUmVmID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICBmb3J3YXJkZWRSZWYoZWwpO1xuICAgIH0gZWxzZSBpZiAoZm9yd2FyZGVkUmVmKSB7XG4gICAgICBmb3J3YXJkZWRSZWYuY3VycmVudCA9IGVsO1xuICAgIH1cbiAgfTtcbiAgY29uc3Qgc3R5bGVzID0ge307XG4gIGlmIChmb250RmFtaWx5ICE9IG51bGwpIHtcbiAgICBzdHlsZXMuZm9udEZhbWlseSA9IGZvbnRGYW1pbHk7XG4gIH1cbiAgaWYgKGZvbnRTaXplICE9IG51bGwpIHtcbiAgICBzdHlsZXMuZm9udFNpemUgPSBmb250U2l6ZTtcbiAgfVxuICBpZiAoZm9udFdlaWdodCAhPSBudWxsKSB7XG4gICAgc3R5bGVzLmZvbnRXZWlnaHQgPSBmb250V2VpZ2h0O1xuICB9XG4gIGlmIChmb250U3R5bGUgIT0gbnVsbCkge1xuICAgIHN0eWxlcy5mb250U3R5bGUgPSBmb250U3R5bGU7XG4gIH1cbiAgY2xlYW4uc3R5bGUgPSAoMCwgX3Jlc29sdmUucmVzb2x2ZSkoc3R5bGUsIHN0eWxlcyk7XG4gIGlmIChvblByZXNzICE9PSBudWxsKSB7XG4gICAgY2xlYW4ub25DbGljayA9IHByb3BzLm9uUHJlc3M7XG4gIH1cbiAgaWYgKHByb3BzLmhyZWYgIT09IG51bGwgJiYgcHJvcHMuaHJlZiAhPT0gdW5kZWZpbmVkKSB7XG4gICAgdmFyIF9yZXNvbHZlQXNzZXRVcmk7XG4gICAgY2xlYW4uaHJlZiA9IChfcmVzb2x2ZUFzc2V0VXJpID0gKDAsIF9yZXNvbHZlQXNzZXRVcmkyLnJlc29sdmVBc3NldFVyaSkocHJvcHMuaHJlZikpID09PSBudWxsIHx8IF9yZXNvbHZlQXNzZXRVcmkgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9yZXNvbHZlQXNzZXRVcmkudXJpO1xuICB9XG4gIHJldHVybiBjbGVhbjtcbn07XG5leHBvcnRzLnByZXBhcmUgPSBwcmVwYXJlO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cHJlcGFyZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/web/utils/prepare.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/xml.js":
/*!***************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/xml.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.SvgAst = SvgAst;\nexports.SvgFromXml = exports.SvgFromUri = void 0;\nexports.SvgUri = SvgUri;\nexports.SvgXml = SvgXml;\nexports.astToReact = astToReact;\nexports.camelCase = void 0;\nexports.getStyle = getStyle;\nexports.parse = parse;\nObject.defineProperty(exports, \"tags\", ({\n  enumerable: true,\n  get: function () {\n    return _xmlTags.tags;\n  }\n}));\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\nvar React = _react;\nvar _fetchData = __webpack_require__(/*! ./utils/fetchData */ \"../../node_modules/react-native-svg/lib/commonjs/utils/fetchData.js\");\nvar _xmlTags = __webpack_require__(/*! ./xmlTags */ \"../../node_modules/react-native-svg/lib/commonjs/xmlTags.js\");\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction missingTag() {\n  return null;\n}\nfunction SvgAst({\n  ast,\n  override\n}) {\n  if (!ast) {\n    return null;\n  }\n  const {\n    props,\n    children\n  } = ast;\n  const Svg = _xmlTags.tags.svg;\n  return /*#__PURE__*/React.createElement(Svg, _extends({}, props, override), children);\n}\nconst err = console.error.bind(console);\nfunction SvgXml(props) {\n  const {\n    onError = err,\n    xml,\n    override,\n    fallback\n  } = props;\n  try {\n    const ast = (0, _react.useMemo)(() => xml !== null ? parse(xml) : null, [xml]);\n    return /*#__PURE__*/React.createElement(SvgAst, {\n      ast: ast,\n      override: override || props\n    });\n  } catch (error) {\n    onError(error);\n    return fallback ?? null;\n  }\n}\nfunction SvgUri(props) {\n  const {\n    onError = err,\n    uri,\n    onLoad,\n    fallback\n  } = props;\n  const [xml, setXml] = (0, _react.useState)(null);\n  const [isError, setIsError] = (0, _react.useState)(false);\n  (0, _react.useEffect)(() => {\n    uri ? (0, _fetchData.fetchText)(uri).then(data => {\n      setXml(data);\n      isError && setIsError(false);\n      onLoad === null || onLoad === void 0 || onLoad();\n    }).catch(e => {\n      onError(e);\n      setIsError(true);\n    }) : setXml(null);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [onError, uri, onLoad]);\n  if (isError) {\n    return fallback ?? null;\n  }\n  return /*#__PURE__*/React.createElement(SvgXml, {\n    xml: xml,\n    override: props,\n    fallback: fallback\n  });\n}\n\n// Extending Component is required for Animated support.\n\nclass SvgFromXml extends _react.Component {\n  state = {\n    ast: null\n  };\n  componentDidMount() {\n    this.parse(this.props.xml);\n  }\n  componentDidUpdate(prevProps) {\n    const {\n      xml\n    } = this.props;\n    if (xml !== prevProps.xml) {\n      this.parse(xml);\n    }\n  }\n  parse(xml) {\n    const {\n      onError = err\n    } = this.props;\n    try {\n      this.setState({\n        ast: xml ? parse(xml) : null\n      });\n    } catch (e) {\n      const error = e;\n      onError({\n        ...error,\n        message: `[RNSVG] Couldn't parse SVG, reason: ${error.message}`\n      });\n    }\n  }\n  render() {\n    const {\n      props,\n      state: {\n        ast\n      }\n    } = this;\n    return /*#__PURE__*/React.createElement(SvgAst, {\n      ast: ast,\n      override: props.override || props\n    });\n  }\n}\nexports.SvgFromXml = SvgFromXml;\nclass SvgFromUri extends _react.Component {\n  state = {\n    xml: null\n  };\n  componentDidMount() {\n    this.fetch(this.props.uri);\n  }\n  componentDidUpdate(prevProps) {\n    const {\n      uri\n    } = this.props;\n    if (uri !== prevProps.uri) {\n      this.fetch(uri);\n    }\n  }\n  async fetch(uri) {\n    try {\n      this.setState({\n        xml: uri ? await (0, _fetchData.fetchText)(uri) : null\n      });\n    } catch (e) {\n      console.error(e);\n    }\n  }\n  render() {\n    const {\n      props,\n      state: {\n        xml\n      }\n    } = this;\n    return /*#__PURE__*/React.createElement(SvgFromXml, {\n      xml: xml,\n      override: props,\n      onError: props.onError\n    });\n  }\n}\nexports.SvgFromUri = SvgFromUri;\nconst upperCase = (_match, letter) => letter.toUpperCase();\nconst camelCase = phrase => phrase.replace(/[:-]([a-z])/g, upperCase);\nexports.camelCase = camelCase;\nfunction getStyle(string) {\n  const style = {};\n  const declarations = string.split(';').filter(v => v.trim());\n  const {\n    length\n  } = declarations;\n  for (let i = 0; i < length; i++) {\n    const declaration = declarations[i];\n    if (declaration.length !== 0) {\n      const split = declaration.split(':');\n      const property = split[0];\n      const value = split[1];\n      style[camelCase(property.trim())] = value.trim();\n    }\n  }\n  return style;\n}\nfunction astToReact(value, index) {\n  if (typeof value === 'object') {\n    const {\n      Tag,\n      props,\n      children\n    } = value;\n    if (props !== null && props !== void 0 && props.class) {\n      props.className = props.class;\n      delete props.class;\n    }\n    return /*#__PURE__*/React.createElement(Tag, _extends({\n      key: index\n    }, props), children.map(astToReact));\n  }\n  return value;\n}\n\n// slimmed down parser based on https://github.com/Rich-Harris/svg-parser\n\nfunction repeat(str, i) {\n  let result = '';\n  while (i--) {\n    result += str;\n  }\n  return result;\n}\nconst toSpaces = tabs => repeat('  ', tabs.length);\nfunction locate(source, i) {\n  const lines = source.split('\\n');\n  const nLines = lines.length;\n  let column = i;\n  let line = 0;\n  for (; line < nLines; line++) {\n    const {\n      length\n    } = lines[line];\n    if (column >= length) {\n      column -= length;\n    } else {\n      break;\n    }\n  }\n  const before = source.slice(0, i).replace(/^\\t+/, toSpaces);\n  const beforeExec = /(^|\\n).*$/.exec(before);\n  const beforeLine = beforeExec && beforeExec[0] || '';\n  const after = source.slice(i);\n  const afterExec = /.*(\\n|$)/.exec(after);\n  const afterLine = afterExec && afterExec[0];\n  const pad = repeat(' ', beforeLine.length);\n  const snippet = `${beforeLine}${afterLine}\\n${pad}^`;\n  return {\n    line,\n    column,\n    snippet\n  };\n}\nconst validNameCharacters = /[a-zA-Z0-9:_-]/;\nconst commentStart = /<!--/;\nconst whitespace = /[\\s\\t\\r\\n]/;\nconst quotemarks = /['\"]/;\nfunction parse(source, middleware) {\n  const length = source.length;\n  let currentElement = null;\n  let state = metadata;\n  let children = null;\n  let root;\n  const stack = [];\n  function error(message) {\n    const {\n      line,\n      column,\n      snippet\n    } = locate(source, i);\n    throw new Error(`${message} (${line}:${column}). If this is valid SVG, it's probably a bug. Please raise an issue\\n\\n${snippet}`);\n  }\n  function metadata() {\n    while (i + 1 < length && (source[i] !== '<' || !(validNameCharacters.test(source[i + 1]) || commentStart.test(source.slice(i, i + 4))))) {\n      i++;\n    }\n    return neutral();\n  }\n  function neutral() {\n    let text = '';\n    let char;\n    while (i < length && (char = source[i]) !== '<') {\n      text += char;\n      i += 1;\n    }\n    if (/\\S/.test(text)) {\n      children.push(text);\n    }\n    if (source[i] === '<') {\n      return openingTag;\n    }\n    return neutral;\n  }\n  function openingTag() {\n    const char = source[i];\n    if (char === '?') {\n      return neutral;\n    } // <?xml...\n\n    if (char === '!') {\n      const start = i + 1;\n      if (source.slice(start, i + 3) === '--') {\n        return comment;\n      }\n      const end = i + 8;\n      if (source.slice(start, end) === '[CDATA[') {\n        return cdata;\n      }\n      if (/doctype/i.test(source.slice(start, end))) {\n        return neutral;\n      }\n    }\n    if (char === '/') {\n      return closingTag;\n    }\n    const tag = getName();\n    const props = {};\n    const element = {\n      tag,\n      props,\n      children: [],\n      parent: currentElement,\n      Tag: _xmlTags.tags[tag] || missingTag\n    };\n    if (currentElement) {\n      children.push(element);\n    } else {\n      root = element;\n    }\n    getAttributes(props);\n    const {\n      style\n    } = props;\n    if (typeof style === 'string') {\n      element.styles = style;\n      props.style = getStyle(style);\n    }\n    let selfClosing = false;\n    if (source[i] === '/') {\n      i += 1;\n      selfClosing = true;\n    }\n    if (source[i] !== '>') {\n      error('Expected >');\n    }\n    if (!selfClosing) {\n      currentElement = element;\n      ({\n        children\n      } = element);\n      stack.push(element);\n    }\n    return neutral;\n  }\n  function comment() {\n    const index = source.indexOf('-->', i);\n    if (!~index) {\n      error('expected -->');\n    }\n    i = index + 2;\n    return neutral;\n  }\n  function cdata() {\n    const index = source.indexOf(']]>', i);\n    if (!~index) {\n      error('expected ]]>');\n    }\n    children.push(source.slice(i + 7, index));\n    i = index + 2;\n    return neutral;\n  }\n  function closingTag() {\n    const tag = getName();\n    if (!tag) {\n      error('Expected tag name');\n    }\n    if (currentElement && tag !== currentElement.tag) {\n      error(`Expected closing tag </${tag}> to match opening tag <${currentElement.tag}>`);\n    }\n    allowSpaces();\n    if (source[i] !== '>') {\n      error('Expected >');\n    }\n    stack.pop();\n    currentElement = stack[stack.length - 1];\n    if (currentElement) {\n      ({\n        children\n      } = currentElement);\n    }\n    return neutral;\n  }\n  function getName() {\n    let name = '';\n    let char;\n    while (i < length && validNameCharacters.test(char = source[i])) {\n      name += char;\n      i += 1;\n    }\n    return name;\n  }\n  function getAttributes(props) {\n    while (i < length) {\n      if (!whitespace.test(source[i])) {\n        return;\n      }\n      allowSpaces();\n      const name = getName();\n      if (!name) {\n        return;\n      }\n      let value = true;\n      allowSpaces();\n      if (source[i] === '=') {\n        i += 1;\n        allowSpaces();\n        value = getAttributeValue();\n        if (name !== 'id' && !isNaN(+value) && value.trim() !== '') {\n          value = +value;\n        }\n      }\n      props[camelCase(name)] = value;\n    }\n  }\n  function getAttributeValue() {\n    return quotemarks.test(source[i]) ? getQuotedAttributeValue() : getUnquotedAttributeValue();\n  }\n  function getUnquotedAttributeValue() {\n    let value = '';\n    do {\n      const char = source[i];\n      if (char === ' ' || char === '>' || char === '/') {\n        return value;\n      }\n      value += char;\n      i += 1;\n    } while (i < length);\n    return value;\n  }\n  function getQuotedAttributeValue() {\n    const quotemark = source[i++];\n    let value = '';\n    let escaped = false;\n    while (i < length) {\n      const char = source[i++];\n      if (char === quotemark && !escaped) {\n        return value;\n      }\n      if (char === '\\\\' && !escaped) {\n        escaped = true;\n      }\n      value += escaped ? `\\\\${char}` : char;\n      escaped = false;\n    }\n    return value;\n  }\n  function allowSpaces() {\n    while (i < length && whitespace.test(source[i])) {\n      i += 1;\n    }\n  }\n  let i = 0;\n  while (i < length) {\n    if (!state) {\n      error('Unexpected character');\n    }\n    state = state();\n    i += 1;\n  }\n  if (state !== neutral) {\n    error('Unexpected end of input');\n  }\n  if (root) {\n    const xml = (middleware ? middleware(root) : root) || root;\n    const ast = xml.children.map(astToReact);\n    const jsx = xml;\n    jsx.children = ast;\n    return jsx;\n  }\n  return null;\n}\n//# sourceMappingURL=xml.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/xml.js\n");

/***/ }),

/***/ "../../node_modules/react-native-svg/lib/commonjs/xmlTags.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/react-native-svg/lib/commonjs/xmlTags.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.tags = void 0;\nvar _elements = __webpack_require__(/*! ./elements */ \"../../node_modules/react-native-svg/lib/commonjs/elements.web.js\");\nconst tags = exports.tags = {\n  circle: _elements.Circle,\n  clipPath: _elements.ClipPath,\n  defs: _elements.Defs,\n  ellipse: _elements.Ellipse,\n  filter: _elements.Filter,\n  feBlend: _elements.FeBlend,\n  feColorMatrix: _elements.FeColorMatrix,\n  feComponentTransfer: _elements.FeComponentTransfer,\n  feComposite: _elements.FeComposite,\n  feConvolveMatrix: _elements.FeConvolveMatrix,\n  feDiffuseLighting: _elements.FeDiffuseLighting,\n  feDisplacementMap: _elements.FeDisplacementMap,\n  feDistantLight: _elements.FeDistantLight,\n  feDropShadow: _elements.FeDropShadow,\n  feFlood: _elements.FeFlood,\n  feGaussianBlur: _elements.FeGaussianBlur,\n  feImage: _elements.FeImage,\n  feMerge: _elements.FeMerge,\n  feMergeNode: _elements.FeMergeNode,\n  feMorphology: _elements.FeMorphology,\n  feOffset: _elements.FeOffset,\n  fePointLight: _elements.FePointLight,\n  feSpecularLighting: _elements.FeSpecularLighting,\n  feSpotLight: _elements.FeSpotLight,\n  feTile: _elements.FeTile,\n  feTurbulence: _elements.FeTurbulence,\n  foreignObject: _elements.ForeignObject,\n  g: _elements.G,\n  image: _elements.Image,\n  line: _elements.Line,\n  linearGradient: _elements.LinearGradient,\n  marker: _elements.Marker,\n  mask: _elements.Mask,\n  path: _elements.Path,\n  pattern: _elements.Pattern,\n  polygon: _elements.Polygon,\n  polyline: _elements.Polyline,\n  radialGradient: _elements.RadialGradient,\n  rect: _elements.Rect,\n  stop: _elements.Stop,\n  svg: _elements.Svg,\n  symbol: _elements.Symbol,\n  text: _elements.Text,\n  textPath: _elements.TextPath,\n  tspan: _elements.TSpan,\n  use: _elements.Use\n};\n//# sourceMappingURL=xmlTags.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/react-native-svg/lib/commonjs/xmlTags.js\n");

/***/ })

};
;