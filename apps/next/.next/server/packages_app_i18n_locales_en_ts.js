"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "packages_app_i18n_locales_en_ts";
exports.ids = ["packages_app_i18n_locales_en_ts"];
exports.modules = {

/***/ "../../packages/app/i18n/locales/en.ts":
/*!*********************************************!*\
  !*** ../../packages/app/i18n/locales/en.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst en = {\n    common: {\n        confirm: \"Confirm\",\n        cancel: \"Cancel\",\n        save: \"Save\",\n        delete: \"Delete\",\n        edit: \"Edit\",\n        copy: \"Copy\",\n        share: \"Share\",\n        loading: \"Loading...\",\n        error: \"Error\",\n        success: \"Success\",\n        back: \"Back\",\n        next: \"Next\",\n        done: \"Done\",\n        close: \"Close\"\n    },\n    wallet: {\n        balance: \"Balance\",\n        send: \"Send\",\n        receive: \"Receive\",\n        transaction: \"Transaction\",\n        address: \"Address\",\n        amount: \"Amount\",\n        gasPrice: \"Gas Price\",\n        gasLimit: \"Gas Limit\",\n        networkFee: \"Network Fee\",\n        totalBalance: \"Total Balance\",\n        availableBalance: \"Available Balance\",\n        sendTransaction: \"Send Transaction\",\n        receiveTokens: \"Receive Tokens\",\n        transactionHistory: \"Transaction History\",\n        copyAddress: \"Copy Address\",\n        shareAddress: \"Share\",\n        insufficientBalance: \"Insufficient Balance\",\n        transactionFailed: \"Transaction Failed\",\n        transactionSuccess: \"Transaction Success\",\n        pending: \"Pending\",\n        confirmed: \"Confirmed\",\n        failed: \"Failed\",\n        yourAddress: \"Your Address\",\n        enterRecipientAddress: \"Enter recipient address\",\n        max: \"Max\",\n        sending: \"Sending...\",\n        usingWallet: \"Using Wallet\",\n        network: \"Network\"\n    },\n    navigation: {\n        home: \"Home\",\n        wallet: \"Wallet\",\n        settings: \"Settings\",\n        security: \"Security\",\n        about: \"About\",\n        language: \"Language\",\n        network: \"Network\",\n        backup: \"Backup\",\n        import: \"Import\",\n        export: \"Export\",\n        display: \"Display\"\n    },\n    form: {\n        enterAmount: \"Enter Amount\",\n        enterAddress: \"Enter Address\",\n        selectNetwork: \"Select Network\",\n        selectLanguage: \"Select Language\",\n        password: \"Password\",\n        confirmPassword: \"Confirm Password\",\n        mnemonic: \"Mnemonic\",\n        privateKey: \"Private Key\",\n        walletName: \"Wallet Name\",\n        required: \"Please fill in all required fields\",\n        invalid: \"Invalid\",\n        tooShort: \"Too Short\",\n        tooLong: \"Too Long\",\n        passwordMismatch: \"Password Mismatch\"\n    },\n    errors: {\n        networkError: \"Network Error\",\n        invalidAddress: \"Invalid Address\",\n        invalidAmount: \"Invalid Amount\",\n        insufficientFunds: \"Insufficient Funds\",\n        transactionFailed: \"Transaction Failed\",\n        walletNotFound: \"Wallet Not Found\",\n        invalidMnemonic: \"Invalid Mnemonic\",\n        invalidPrivateKey: \"Invalid Private Key\",\n        passwordRequired: \"Password Required\",\n        confirmationFailed: \"Confirmation Failed\",\n        copyFailed: \"Copy failed\"\n    },\n    success: {\n        transactionSent: \"Transaction Sent\",\n        addressCopied: \"Address copied to clipboard\",\n        walletCreated: \"Wallet Created\",\n        walletImported: \"Wallet Imported\",\n        settingsSaved: \"Settings Saved\",\n        backupCompleted: \"Backup Completed\",\n        copySuccess: \"Copy Success\",\n        balanceRefreshed: \"Balance Refreshed\"\n    },\n    error: {\n        refreshFailed: \"Refresh failed, please try again later\"\n    },\n    // 首页相关\n    home: {\n        rewards: \"Rewards\",\n        earnRewards: \"Earn {rate}% rewards\",\n        addToWallet: \"Add {token} on {network} to your wallet\",\n        yearlyEarnings: \"Earn {rate}% annually\",\n        watchlist: \"Watchlist\",\n        addressLabel: \"Address {number}\",\n        copyFailed: \"Copy failed, please copy manually\",\n        createWatchlist: 'Create \"My Watchlist\"',\n        getPriceAlerts: \"Get price alerts and stay informed\",\n        swapCount: \"swaps\",\n        swapAction: \"Swap\",\n        boughtPercent: \"Bought {percent}%\",\n        soldPercent: \"Sold {percent}%\"\n    },\n    // 钱包管理\n    walletManagement: {\n        addAndManage: \"Add and manage wallets\",\n        backupWallet: \"Backup your wallet\",\n        securityWarning: \"Never share these words. Anyone who knows them can steal all your cryptocurrency. Coinbase will never ask for this information.\",\n        recoveryPhrase: \"The following 12 words are your wallet recovery phrase. This phrase allows you to recover your wallet if you lose your device. Back it up to iCloud (recommended) or write it down. Or use both methods.\",\n        copyToClipboard: \"Copy to clipboard\",\n        neverShare: \"Never share\",\n        writeDown: \"Write down\",\n        backupToiCloud: \"Backup to iCloud\",\n        importMnemonic: \"Import mnemonic\",\n        importDescription: \"Enter your mnemonic phrase to add or restore your wallet. The imported mnemonic will be encrypted and securely stored on your device. For your asset security, your mnemonic will not be stored.\",\n        enterMnemonic: \"Enter mnemonic\",\n        mnemonicPlaceholder: \"Enter mnemonic words separated by spaces\",\n        yourAddress: \"Your\",\n        address: \"address\",\n        shareAddress: \"Share\",\n        editLabel: \"Edit Label\",\n        addressLabel: \"Address Label\",\n        labelDescription: \"Provide a label for your address for easy identification. Labels are stored locally and only you can see them.\",\n        enterWalletName: \"Enter wallet name\",\n        saving: \"Saving...\",\n        save: \"Save\"\n    },\n    // 交易相关\n    trading: {\n        all: \"All\",\n        exchange: \"Exchange\",\n        earn: \"Earn\",\n        socialMedia: \"Social Media\",\n        manage: \"Manage\",\n        listen: \"Listen\",\n        tradeAssets: \"Trade Assets\",\n        buy: \"Buy\"\n    },\n    // 时间相关\n    time: {\n        today: \"Today\",\n        yesterday: \"Yesterday\",\n        filter: \"Filter\"\n    },\n    settings: {\n        languageNote: \"Language settings will take effect on next app launch. Some features may require reload.\",\n        languageSubtitle: \"Choose app language\",\n        securitySubtitle: \"Password and security settings\",\n        notifications: \"Notifications\",\n        notificationsSubtitle: \"Manage notification settings\",\n        aboutSubtitle: \"Version info and help\",\n        version: \"Version\",\n        copyright: \"\\xa9 2024 Coinbase Wallet\"\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (en);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/i18n/locales/en.ts\n");

/***/ })

};
;