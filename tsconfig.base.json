{
  "compilerOptions": {
    "baseUrl": ".",
    "rootDir": ".",
    "importHelpers": true,
    "allowJs": false,
    "allowSyntheticDefaultImports": true,
    "downlevelIteration": true,
    "esModuleInterop": true,
    "preserveSymlinks": true,
    "incremental": true,
    "jsx": "react-jsx",
    "module": "system",
    "moduleResolution": "node",
    "noEmitOnError": false,
    "noImplicitAny": false,
    "noImplicitReturns": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "useUnknownInCatchVariables": false,
    "preserveConstEnums": true,
    // DONT DO THIS so jsdoc will remain
    "removeComments": false,
    "skipLibCheck": true,
    "sourceMap": false,
    "strictNullChecks": true,
    "target": "es2020",
    "types": ["node"],
    "lib": ["dom", "esnext"]
  },
  "exclude": ["_"],
  "typeAcquisition": {
    "enable": true
  }
}
